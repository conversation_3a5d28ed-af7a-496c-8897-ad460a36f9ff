# ImageDO 到 ImageGroup 数据迁移指南

## 功能说明

这个迁移功能将已经通过 `metadata.groupId` 筛选出来的 ImageDO 样本数据聚合到 `image_group` 表中。

### 主要功能：

1. **数据聚合**：根据 `groupId` 对 ImageDO 数据进行分组
2. **取代表数据**：每个组取第一条数据作为代表插入到 ImageGroup 表
3. **数据结构转换**：将 metadata 中的 label 数据结构进行转换
4. **扩展信息处理**：处理 `ext_info.modifyUser` 字段

### 数据转换规则：

#### 原始 metadata 结构：
```json
{
  "tags": ["moncler"],
  "label": {
    "flat.1": {
      "checked": true,
      "isReference": false
    },
    "person.1": {
      "checked": false,
      "isReference": false
    }
  },
  "groupId": "45bbd7fb-3d9f-45c3-9d22-f240e629b933"
}
```

#### 转换后的 metadata 结构：
```json
{
  "tags": ["moncler"],
  "groupId": "45bbd7fb-3d9f-45c3-9d22-f240e629b933",
  "label.flat.1": [
    {
      "checked": true,
      "isReference": false
    }
  ],
  "reference.flat.1": [],
  "label.person.1": [],
  "reference.person.1": []
}
```

#### 转换规则说明：
- `checked` 为 `true` 的数据会被放入对应的 `label.{key}` 数组中
- `isReference` 为 `true` 的数据会被放入对应的 `reference.{key}` 数组中
- 如果同时满足两个条件，数据会同时出现在两个数组中
- 如果都不满足条件，对应的数组为空

## 使用方法

### 1. 通过 REST API 调用

```bash
curl -X POST http://localhost:8080/imageGallery/imageGroup/migrate \
  -H "Content-Type: application/json"
```

### 2. 通过代码调用

```java
@Autowired
private ImageGroupService imageGroupService;

public void performMigration() {
    imageGroupService.migrateImageToImageGroup();
}
```

## 迁移过程

1. **查询数据**：查询所有有 `groupId` 的 ImageDO 数据
2. **数据验证**：验证 metadata 格式是否正确
3. **分组聚合**：按 `groupId` 对数据进行分组
4. **重复检查**：检查目标 ImageGroup 表中是否已存在相同的 `groupId`
5. **数据转换**：
   - 转换 metadata 中的 label 结构
   - 根据 checked 和 isReference 值分配到对应数组
   - 设置 imageIds 列表
   - 处理 extInfo 中的 modifyUser
6. **数据插入**：将转换后的数据插入到 ImageGroup 表

## 注意事项

1. **幂等性**：迁移操作是幂等的，重复执行不会产生重复数据
2. **数据安全**：迁移过程不会删除或修改原始的 ImageDO 数据
3. **错误处理**：单个组的迁移失败不会影响其他组的迁移
4. **日志记录**：详细的日志记录帮助跟踪迁移过程和排查问题

## 监控和验证

### 迁移前检查：
```sql
-- 查看有 groupId 的 ImageDO 数据数量
SELECT COUNT(*) FROM image 
WHERE metadata::jsonb ? 'groupId' 
AND deleted = false;

-- 查看按 groupId 分组的统计
SELECT 
    metadata::jsonb->>'groupId' as group_id,
    COUNT(*) as image_count
FROM image 
WHERE metadata::jsonb ? 'groupId' 
AND deleted = false
GROUP BY metadata::jsonb->>'groupId'
ORDER BY image_count DESC;
```

### 迁移后验证：
```sql
-- 查看 ImageGroup 表中的数据
SELECT COUNT(*) FROM image_group WHERE deleted = false;

-- 验证数据完整性
SELECT 
    group_id,
    type,
    JSON_ARRAY_LENGTH(image_ids::json) as image_count,
    metadata::jsonb ? 'label.flat.1' as has_label_flat,
    metadata::jsonb ? 'reference.flat.1' as has_reference_flat
FROM image_group 
WHERE deleted = false
ORDER BY create_time DESC;
```

## 回滚方案

如果需要回滚迁移，可以执行以下 SQL：

```sql
-- 删除迁移产生的 ImageGroup 数据（谨慎操作）
DELETE FROM image_group 
WHERE ext_info::jsonb->>'modifyUser' = 'system_migration';
```

## 性能考虑

- 迁移过程会对数据库产生一定的负载
- 建议在业务低峰期执行
- 大量数据迁移时可以考虑分批处理
- 监控数据库连接池和内存使用情况
