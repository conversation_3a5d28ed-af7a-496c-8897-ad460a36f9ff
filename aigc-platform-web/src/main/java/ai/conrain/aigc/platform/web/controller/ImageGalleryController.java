package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.dal.example.ImageCaptionExample;
import ai.conrain.aigc.platform.dal.pgsql.dao.ImageCaptionDAO;
import ai.conrain.aigc.platform.dal.pgsql.entity.ImageCaptionDO;
import ai.conrain.aigc.platform.integration.ai.imageAnalysis.ImageQualityApiService;
import ai.conrain.aigc.platform.service.component.ImageCaptionService;
import ai.conrain.aigc.platform.service.component.ImageService;
import ai.conrain.aigc.platform.service.component.onnx.GenreRecognizeOnnxService;
import ai.conrain.aigc.platform.service.enums.ClothShootGenreEnum;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.request.UploadSortEmbeddingsRequest;
import ai.conrain.aigc.platform.service.model.vo.ImageCaptionVO;
import ai.conrain.aigc.platform.service.model.vo.ImageVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import com.alibaba.fastjson2.JSONObject;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Validated
@RestController
@RequestMapping("/imageGallery")
public class ImageGalleryController {

    @Autowired
    private ImageService imageService;

    @Autowired
    private ImageCaptionService imageCaptionService;

    @Autowired
    private ImageCaptionDAO imageCaptionDAO;

    @Autowired
    private ImageQualityApiService imageQualityApiService;

    @Autowired
    private GenreRecognizeOnnxService genreRecognizeOnnxService;

    @PostMapping("/selectGeminiFlashCaptionedSceneImages4Embedding")
    public Result<List<Integer>> selectGeminiFlashCaptionedSceneImages4Embedding(@NotNull @JsonArg Integer count) {
        List<ImageVO> imageVOS = imageService.selectGeminiFlashCaptionedSceneImages4Embedding(count);
        if (CollectionUtils.isNotEmpty(imageVOS)) {
            return Result.success(imageVOS.stream().map(ImageVO::getId).toList());
        }

        return Result.success(new ArrayList<>());
    }

    @PostMapping("/createImageCaptionEmbeddings")
    public Result<Integer> createImageCaptionEmbeddings(@NotNull @JsonArg Integer imageId) {
        ImageCaptionVO imageCaptionVO = imageCaptionService.createImageCaptionEmbeddings(imageService.selectById(imageId), true, true);

        if (imageCaptionVO == null) {
            return Result.error("imageCaptionVO is null");
        }
        return Result.success(imageCaptionVO.getId());
    }

    @PostMapping("/fixImageCaptionGenre")
    public Result<?> fixImageCaptionGenre(@JsonArg @NotBlank String imagePath, @JsonArg @NotBlank String genre) {
        ImageVO img = imageService.getByHashOrPath(null, imagePath);
        if (img != null) {
            // 更新图片元数据,genre
            if (img.getMetadata() == null){
                img.setMetadata(new JSONObject());
            }
            img.getMetadata().put("genre", genre);

            ClothShootGenreEnum genreEnum = ClothShootGenreEnum.getByCode(genre);
            if (genreEnum != null) {
                img.getMetadata().put("intended_use_from_model", genreEnum.getDisplayName());
            }
            imageService.updateByIdSelective(img);

            // 更新image_caption.genre
            ImageCaptionDO imageCaptionDO = imageCaptionDAO.selectOneByImageId(img.getId());
            if (imageCaptionDO != null) {
                ImageCaptionVO target = new ImageCaptionVO();
                target.setId(imageCaptionDO.getId());
                target.setGenre(genre);
                target.setCaptionVersion("genre.0928");
                imageCaptionService.updateByIdSelective(target);
            }
        }
        return Result.success();
    }

    @PostMapping("/fixImageCaptionQualityScore")
    public Result<?> fixImageCaptionQualityScore() {
        List<ImageCaptionDO> imageCaptionVOS = imageCaptionDAO.selectNoQualityScoreImageCaptionIds();

        for (ImageCaptionDO imageCaptionVO : imageCaptionVOS) {
            ImageVO imageVO = imageService.selectById(imageCaptionVO.getImageId());
            if (imageVO != null) {
                Double score = imageQualityApiService.classifyImageQuality(imageVO.getUrl());
                if (score != null) {
                    ImageCaptionVO target = new ImageCaptionVO();
                    target.setId(imageCaptionVO.getId());
                    target.setQualityScore(score);
                    imageCaptionService.updateByIdSelective(target);
                }
            }
        }

        return Result.success();
    }

    @PostMapping("/uploadEmbeddingsResult4Sort")
    public Result uploadEmbeddingsResult4Sort(@RequestBody UploadSortEmbeddingsRequest req){
        imageCaptionService.uploadEmbeddingsResult4Sort(req.getImgHash(), req.getImgPath(), req.getSortModelEmbeddings());
        return Result.success();
    }
}
