package ai.conrain.aigc.platform.service.component.agent;

import ai.conrain.aigc.platform.integration.ai.imageAnalysis.ImageAnalysisService;
import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisResult;
import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisTaskResponse;
import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.service.component.AgentSessionTaskService;
import ai.conrain.aigc.platform.service.component.agent.entity.params.RefImageAnalysisParams;
import ai.conrain.aigc.platform.service.component.agent.translation.TranslationService;
import ai.conrain.aigc.platform.service.enums.agent.AgentSessionStatusEnum;
import ai.conrain.aigc.platform.service.enums.agent.AgentSessionTaskTypeEnum;
import ai.conrain.aigc.platform.service.model.vo.AgentSessionTaskVO;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static ai.conrain.aigc.platform.service.component.agent.constants.AgentConstants.*;


/**
 * 参考图分析任务处理器
 * 采用异步任务模式，处理参考图片的风格场景分析
 */
@Slf4j
@Component
public class RefImageAnalysisTaskHandler implements AgentTaskHandler<RefImageAnalysisParams> {

    @Autowired
    private ImageAnalysisService imageAnalysisService;

    @Autowired
    private TairService tairService;

    @Autowired
    private AgentSessionTaskService agentSessionTaskService;

    @Autowired
    private TranslationService translationService;

    // 缓存有效期：24小时
    private static final int CACHE_EXPIRE_SECONDS = 60 * 60 * 24;

    @Override
    public void handleTask(AgentSessionTaskVO agentSessionTask) {
        log.info("【agentTask】RefImageAnalysisTaskHandler::handleTask::开始处理参考图分析任务, agentTaskId={}", agentSessionTask.getId());

        try {
            // 1. 提取和验证参数
            RefImageAnalysisParams params = extractAndValidateParameters(agentSessionTask);
            String refImageUrl = extractRefImageUrl(params);

            // 2. 检查或创建分析任务
            if (!hasExistingTask(agentSessionTask)) {
                createAnalysisTask(agentSessionTask, refImageUrl);
                return;
            }

            // 3. 查询分析结果
            boolean isCompleted = queryAnalysisResult(agentSessionTask, refImageUrl);
            if (!isCompleted) {
                return;
            }

            // 4. 执行翻译
            performTranslation(agentSessionTask);

            // 5. 转换数据结构为编辑模式
            transformToEditableStructure(agentSessionTask);

            // 6. 完成任务
            completeTask(agentSessionTask);
        } catch (Exception e) {
            handleTaskFailure(agentSessionTask, e);
        }
    }

    /**
     * 提取和验证任务参数
     */
    private RefImageAnalysisParams extractAndValidateParameters(AgentSessionTaskVO agentSessionTask) {
        RefImageAnalysisParams params = extractParameters(agentSessionTask);
        
        if (params == null) {
            throw new IllegalArgumentException("【agentTask】RefImageAnalysisTaskHandler::handleTask::任务参数不完整，无法提取参数");
        }
        
        return params;
    }

    /**
     * 提取参考图片URL
     */
    private String extractRefImageUrl(RefImageAnalysisParams params) {
        String refImageUrl = null;
        
        if (params.getReferenceImageList() != null && !params.getReferenceImageList().isEmpty()) {
            refImageUrl = params.getReferenceImageList().getFirst();
            log.info("【agentTask】RefImageAnalysisTaskHandler::handleTask::从参考图列表中获取第一张图片, url={}, 列表大小={}",
                    refImageUrl, params.getReferenceImageList().size());
        } else if (StringUtils.isNotBlank(params.getRefImageUrl())) {
            refImageUrl = params.getRefImageUrl();
            log.info("【agentTask】RefImageAnalysisTaskHandler::handleTask::使用单个参考图片URL: {}", refImageUrl);
        }
        
        if (StringUtils.isBlank(refImageUrl)) {
            throw new IllegalArgumentException("【agentTask】RefImageAnalysisTaskHandler::handleTask::参考图片URL不能为空");
        }
        
        return refImageUrl;
    }

    /**
     * 检查是否已有现存的分析任务
     */
    private boolean hasExistingTask(AgentSessionTaskVO agentSessionTask) {
        String refAnalysisTaskId = agentSessionTask.getStringFromExtInfo(KEY_REF_ANALYSIS_TASK_ID);
        return StringUtils.isNotBlank(refAnalysisTaskId);
    }

    /**
     * 创建分析任务
     */
    private void createAnalysisTask(AgentSessionTaskVO agentSessionTask, String refImageUrl) {
        log.info("【agentTask】RefImageAnalysisTaskHandler::handleTask::创建参考图分析任务, taskId={}, refImageUrl={}",
                agentSessionTask.getId(), refImageUrl);

        // 检查是否应该创建任务（考虑重试次数）
        if (shouldCreateRefAnalysisTask(agentSessionTask)) {
            String refAnalysisTaskId = createRefImageAnalysisTask(refImageUrl);
            if (StringUtils.isNotBlank(refAnalysisTaskId)) {
                agentSessionTask.addExtInfo(KEY_REF_ANALYSIS_TASK_ID, refAnalysisTaskId);
                agentSessionTask.setStatus(AgentSessionStatusEnum.PROCESSING);
                agentSessionTaskService.updateByIdSelective(agentSessionTask);
            } else {
                throw new RuntimeException("【agentTask】RefImageAnalysisTaskHandler::handleTask::创建参考图分析任务失败");
            }

            log.info("【agentTask】RefImageAnalysisTaskHandler::handleTask::参考图分析任务已创建，等待下次调度查询结果, taskId={}, refAnalysisTaskId={}",
                    agentSessionTask.getId(), refAnalysisTaskId);
        } else {
            throw new RuntimeException("【agentTask】RefImageAnalysisTaskHandler::handleTask::参考图分析任务重试次数已达上限");
        }
    }

    /**
     * 查询分析结果
     */
    private boolean queryAnalysisResult(AgentSessionTaskVO agentSessionTask, String refImageUrl) {
        String refAnalysisTaskId = agentSessionTask.getStringFromExtInfo(KEY_REF_ANALYSIS_TASK_ID);
        
        log.info("【agentTask】RefImageAnalysisTaskHandler::handleTask::查询参考图分析结果, taskId={}, refAnalysisTaskId={}",
                agentSessionTask.getId(), refAnalysisTaskId);

        ImageAnalysisResult analysisResult = queryAnalysisResultWithRetry(agentSessionTask, refAnalysisTaskId, refImageUrl);

        // 检查任务是否完成
        boolean isCompleted = analysisResult != null && 
                ("completed".equals(analysisResult.getStatus()) || "failed".equals(analysisResult.getStatus()));

        if (isCompleted) {
            // 存储分析结果
            agentSessionTask.addExtInfo(KEY_REF_ANALYSIS_TASK_RESULT, analysisResult);
        } else {
            log.info("【agentTask】RefImageAnalysisTaskHandler::handleTask::参考图分析任务未完成，等待下次调度, taskId={}, status={}",
                    agentSessionTask.getId(), analysisResult != null ? analysisResult.getStatus() : "null");
        }

        return isCompleted;
    }

    /**
     * 执行翻译
     */
    private void performTranslation(AgentSessionTaskVO agentSessionTask) {
        String translationStatus = agentSessionTask.getStringFromExtInfo(KEY_TRANSLATION_STATUS);
        
        if (!TRANSLATION_STATUS_COMPLETED.equals(translationStatus)) {
            // 获取分析结果
            Object analysisResultObj = agentSessionTask.getExtInfo().get(KEY_REF_ANALYSIS_TASK_RESULT);
            if (analysisResultObj instanceof ImageAnalysisResult analysisResult && 
                "completed".equals(analysisResult.getStatus())) {
                
                log.info("【agentTask】RefImageAnalysisTaskHandler::handleTask::开始翻译参考图分析结果, taskId={}", agentSessionTask.getId());
                
                try {
                    // 标记翻译进行中
                    agentSessionTask.addExtInfo(KEY_TRANSLATION_STATUS, TRANSLATION_STATUS_IN_PROGRESS);
                    agentSessionTaskService.updateByIdSelective(agentSessionTask);
                    
                    // 执行翻译
                    ImageAnalysisResult translatedResult = translationService.translateAnalysisResult(analysisResult);
                    
                    // 存储翻译结果
                    if (translatedResult != null) {
                        ImageAnalysisResult translationResultToStore = new ImageAnalysisResult();
                        translationResultToStore.setTaskId(analysisResult.getTaskId());
                        translationResultToStore.setStatus(analysisResult.getStatus());
                        translationResultToStore.setAnalysis(translatedResult.getAnalysis());
                        translationResultToStore.setPreCaption(translatedResult.getPreCaption());
                        
                        agentSessionTask.addExtInfo(KEY_TRANSLATION_REF_ANALYSIS_TASK_RESULT, translationResultToStore);
                        
                        log.info("【agentTask】RefImageAnalysisTaskHandler::handleTask::翻译结果已存储, taskId={}", agentSessionTask.getId());
                    }
                    
                    // 标记翻译完成
                    agentSessionTask.addExtInfo(KEY_TRANSLATION_STATUS, TRANSLATION_STATUS_COMPLETED);
                    
                    log.info("【agentTask】RefImageAnalysisTaskHandler::handleTask::翻译完成, taskId={}", agentSessionTask.getId());
                    
                } catch (Exception e) {
                    log.error("【agentTask】RefImageAnalysisTaskHandler::handleTask::翻译失败，使用原始结果, taskId={}", agentSessionTask.getId(), e);
                    agentSessionTask.addExtInfo(KEY_TRANSLATION_STATUS, TRANSLATION_STATUS_FAILED);
                    
                    // 翻译失败时，将原始结果作为翻译结果存储
                    agentSessionTask.addExtInfo(KEY_TRANSLATION_REF_ANALYSIS_TASK_RESULT, analysisResult);
                }
            } else {
                log.warn("【agentTask】RefImageAnalysisTaskHandler::handleTask::分析结果不存在或未完成，跳过翻译, taskId={}", agentSessionTask.getId());
            }
        } else {
            log.debug("【agentTask】RefImageAnalysisTaskHandler::handleTask::翻译已完成，跳过翻译步骤, taskId={}", agentSessionTask.getId());
        }
    }

    /**
     * 转换数据结构为可编辑结构
     * 将analysis属性下的最底层字符串值转换为 {content: '', isUpdate: false} 的对象结构
     */
    private void transformToEditableStructure(AgentSessionTaskVO agentSessionTask) {
        log.info("【agentTask】RefImageAnalysisTaskHandler::transformToEditableStructure::开始转换数据结构为编辑模式, taskId={}", agentSessionTask.getId());

        try {
            // 获取翻译后的分析结果
            Object translationResult = agentSessionTask.getExtInfo().get(KEY_TRANSLATION_REF_ANALYSIS_TASK_RESULT);
            if (translationResult instanceof ImageAnalysisResult analysisResult) {

                if (analysisResult.getAnalysis() != null) {
                    // 转换analysis对象
                    Object transformedAnalysis = transformObjectToEditableStructure(analysisResult.getAnalysis());
                    
                    // 直接封装为JSONObject存储
                    JSONObject editableResult = new JSONObject();
                    editableResult.put("taskId", analysisResult.getTaskId());
                    editableResult.put("status", analysisResult.getStatus());
                    editableResult.put("analysis", transformedAnalysis);
                    
                    // 存储可编辑结构的结果
                    agentSessionTask.addExtInfo(KEY_TRANSLATION_REF_ANALYSIS_TASK_RESULT, editableResult);
                    
                    log.info("【agentTask】RefImageAnalysisTaskHandler::transformToEditableStructure::数据结构转换完成, taskId={}", agentSessionTask.getId());
                }
            }
            
        } catch (Exception e) {
            log.error("【agentTask】RefImageAnalysisTaskHandler::transformToEditableStructure::转换数据结构失败, taskId={}", agentSessionTask.getId(), e);
            // 转换失败不影响主流程，继续执行
        }
    }

    /**
     * 递归转换对象为可编辑结构
     */
    private Object transformObjectToEditableStructure(Object obj) {
        switch (obj) {
            case null -> {
                return null;
            }
            case JSONObject jsonObj -> {
                JSONObject result = new JSONObject();

                for (String key : jsonObj.keySet()) {
                    Object value = jsonObj.get(key);
                    result.put(key, transformObjectToEditableStructure(value));
                }

                return result;
            }
            case String s -> {
                // 将字符串转换为可编辑结构
                JSONObject editableField = new JSONObject();
                editableField.put("content", obj);
                editableField.put("isUpdate", false);
                return editableField;
            }
            default -> {
                // 对于其他复杂对象，先转换为JSONObject再处理
                try {
                    // 将POJO对象转换为JSONObject
                    JSONObject jsonObj = JSONObject.parseObject(JSONObject.toJSONString(obj));
                    return transformObjectToEditableStructure(jsonObj);
                } catch (Exception e) {
                    log.warn("【agentTask】transformObjectToEditableStructure::无法转换对象类型: {}, 保持原值", obj.getClass().getSimpleName());
                    // 如果转换失败，保持原值
                    return obj;
                }
            }
        }
    }

    /**
     * 完成任务
     */
    private void completeTask(AgentSessionTaskVO agentSessionTask) {
        // 获取分析结果
        Object analysisResultObj = agentSessionTask.getExtInfo().get(KEY_REF_ANALYSIS_TASK_RESULT);
        if (analysisResultObj instanceof ImageAnalysisResult analysisResult) {
            
            // 确定最终状态
            AgentSessionStatusEnum finalStatus = "completed".equals(analysisResult.getStatus())
                    ? AgentSessionStatusEnum.FINISHED : AgentSessionStatusEnum.FAILED;

            // 更新任务状态
            agentSessionTask.setStatus(finalStatus);
            agentSessionTaskService.updateByIdSelective(agentSessionTask);

            log.info("【agentTask】RefImageAnalysisTaskHandler::handleTask::参考图分析任务完成, taskId={}, status={}, hasAnalysis={}",
                    agentSessionTask.getId(), finalStatus,
                    analysisResult.getAnalysis() != null || analysisResult.getPreCaption() != null);
        }
    }

    /**
     * 处理任务失败
     */
    private void handleTaskFailure(AgentSessionTaskVO agentSessionTask, Exception e) {
        log.error("【agentTask】RefImageAnalysisTaskHandler::handleTask::参考图分析任务失败, taskId={}", agentSessionTask.getId(), e);

        // 构建错误信息
        JSONObject errorResult = new JSONObject();
        errorResult.put("error", true);
        errorResult.put("errorMessage", e.getMessage());
        errorResult.put("timestamp", new Date());

        // 更新任务信息
        agentSessionTask.addExtInfo(KEY_ERROR_RESULT, errorResult);
        agentSessionTask.setStatus(AgentSessionStatusEnum.FAILED);
        agentSessionTaskService.updateByIdSelective(agentSessionTask);
    }

    /**
     * 创建参考图分析任务
     */
    private String createRefImageAnalysisTask(String refImageUrl) {
        try {
            // 先检查缓存
            String cacheKey = DigestUtils.md5Hex(refImageUrl + "_ref");
            ImageAnalysisResult cachedResult = tairService.getObject(cacheKey, ImageAnalysisResult.class);

            if (cachedResult != null &&
                    (cachedResult.getAnalysis() != null || cachedResult.getPreCaption() != null)) {
                log.info("【agentTask】RefImageAnalysisTaskHandler::createRefImageAnalysisTask::从缓存获取参考图分析结果，无需创建任务, cacheKey={}", cacheKey);
                return "cached_" + cacheKey;
            }

            // 创建分析任务，参考图分析通常不限制只分析服装
            ImageAnalysisTaskResponse task = imageAnalysisService.createAnalysisTask(
                    refImageUrl, "gemini-flash", false, true, null, false, false);

            log.info("【agentTask】RefImageAnalysisTaskHandler::createRefImageAnalysisTask::参考图分析任务已创建, taskId={}", task.getTaskId());
            return task.getTaskId();

        } catch (Exception e) {
            log.error("【agentTask】RefImageAnalysisTaskHandler::createRefImageAnalysisTask::创建参考图分析任务失败, refImageUrl={}", refImageUrl, e);
            return null;
        }
    }

    /**
     * 查询分析结果
     */
    private ImageAnalysisResult queryAnalysisResult(String taskId, String refImageUrl) {
        try {
            // 如果是缓存标识，直接从缓存获取
            if (taskId.startsWith("cached_")) {
                String cacheKey = taskId.substring(7);
                ImageAnalysisResult cachedResult = tairService.getObject(cacheKey, ImageAnalysisResult.class);
                if (cachedResult != null) {
                    cachedResult.setStatus("completed");
                }
                return cachedResult;
            }

            // 查询分析结果
            ImageAnalysisResult result = imageAnalysisService.getAnalysisResult(taskId);

            // 如果分析完成，缓存结果
            if (result != null && "completed".equals(result.getStatus()) &&
                    (result.getAnalysis() != null || result.getPreCaption() != null)) {
                String cacheKey = DigestUtils.md5Hex(refImageUrl + "_ref");
                tairService.setObject(cacheKey, result, CACHE_EXPIRE_SECONDS);
                log.info("【agentTask】RefImageAnalysisTaskHandler::queryAnalysisResult::参考图分析结果已缓存, taskId={}, cacheKey={}", taskId, cacheKey);
            }

            return result;

        } catch (Exception e) {
            log.error("【agentTask】RefImageAnalysisTaskHandler::queryAnalysisResult::查询参考图分析结果失败, taskId={}", taskId, e);
            return null;
        }
    }


    @Override
    public String getSupportedTaskType() {
        return AgentSessionTaskTypeEnum.REF_IMAGE_ANALYSIS.getCode();
    }

    @Override
    public Class<RefImageAnalysisParams> getParameterType() {
        return RefImageAnalysisParams.class;
    }

    /**
     * 带重试机制的分析结果查询
     */
    private ImageAnalysisResult queryAnalysisResultWithRetry(AgentSessionTaskVO agentSessionTask, String taskId, String refImageUrl) {
        try {
            // 检查是否为缓存任务
            if (taskId.startsWith("cached_")) {
                String cacheKey = taskId.substring(7);
                ImageAnalysisResult cachedResult = tairService.getObject(cacheKey, ImageAnalysisResult.class);
                if (cachedResult != null) {
                    cachedResult.setStatus("completed");
                }
                return cachedResult;
            }

            // 查询分析结果
            ImageAnalysisResult result = imageAnalysisService.getAnalysisResult(taskId);
            
            // 检查结果状态
            if (result != null && "failed".equals(result.getStatus())) {
                log.warn("【agentTask】参考图分析任务失败，准备重试, taskId={}, refAnalysisTaskId={}", 
                        agentSessionTask.getId(), taskId);
                
                // 处理任务失败，触发重试
                handleTaskFailureAndRetry(agentSessionTask, taskId, refImageUrl);
                return null; // 返回null表示需要重新创建任务
            }

            // 如果任务完成，缓存结果
            if (result != null && "completed".equals(result.getStatus()) &&
                    (result.getAnalysis() != null || result.getPreCaption() != null)) {
                String cacheKey = DigestUtils.md5Hex(refImageUrl + "_ref");
                tairService.setObject(cacheKey, result, CACHE_EXPIRE_SECONDS);
                log.info("【agentTask】参考图分析结果已缓存, taskId={}, cacheKey={}", taskId, cacheKey);
            }
            
            return result;
        } catch (Exception e) {
            log.error("【agentTask】查询参考图分析结果失败, taskId={}, refAnalysisTaskId={}", agentSessionTask.getId(), taskId, e);
            return null;
        }
    }

    /**
     * 处理任务失败并进行重试
     */
    private void handleTaskFailureAndRetry(AgentSessionTaskVO agentSessionTask, String failedTaskId, String refImageUrl) {
        // 获取当前重试次数
        Integer currentRetryCount = agentSessionTask.getIntegerFromExtInfo(KEY_REF_ANALYSIS_RETRY_COUNT);
        if (currentRetryCount == null) {
            currentRetryCount = 0;
        }

        // 添加失败的任务ID到失败列表
        List<String> failedTaskIds = getFailedTaskIdList(agentSessionTask, KEY_FAILED_REF_ANALYSIS_TASK_ID_LIST);
        if (!failedTaskIds.contains(failedTaskId)) {
            failedTaskIds.add(failedTaskId);
            agentSessionTask.addExtInfo(KEY_FAILED_REF_ANALYSIS_TASK_ID_LIST, failedTaskIds);
        }

        // 增加重试次数
        currentRetryCount++;
        agentSessionTask.addExtInfo(KEY_REF_ANALYSIS_RETRY_COUNT, currentRetryCount);

        // 检查是否超过重试上限
        if (currentRetryCount >= MAX_RETRY_COUNT) {
            log.error("【agentTask】参考图分析任务重试次数已达上限，任务失败, taskId={}, retryCount={}", 
                    agentSessionTask.getId(), currentRetryCount);

            // 设置为失败
            agentSessionTask.setStatus(AgentSessionStatusEnum.FAILED);
        } else {
            log.info("【agentTask】准备重试参考图分析任务, taskId={}, retryCount={}/{}", 
                    agentSessionTask.getId(), currentRetryCount, MAX_RETRY_COUNT);
            
            // 移除当前任务ID，以便重新创建
            agentSessionTask.getExtInfo().remove(KEY_REF_ANALYSIS_TASK_ID);
        }

        // 更新任务信息
        agentSessionTaskService.updateByIdSelective(agentSessionTask);
    }

    /**
     * 获取失败任务ID列表
     */
    @SuppressWarnings("unchecked")
    private List<String> getFailedTaskIdList(AgentSessionTaskVO agentSessionTask, String key) {
        Object failedListObj = agentSessionTask.getExtInfo().get(key);
        List<String> failedTaskIds;
        
        if (failedListObj instanceof List) {
            failedTaskIds = (List<String>) failedListObj;
        } else {
            failedTaskIds = new ArrayList<>();
        }
        
        return failedTaskIds;
    }

    /**
     * 判断是否应该创建参考图分析任务
     */
    private boolean shouldCreateRefAnalysisTask(AgentSessionTaskVO agentSessionTask) {
        // 检查是否已有任务ID
        String existingTaskId = agentSessionTask.getStringFromExtInfo(KEY_REF_ANALYSIS_TASK_ID);
        if (StringUtils.isNotBlank(existingTaskId)) {
            return false;
        }

        // 检查重试次数是否超限
        Integer retryCount = agentSessionTask.getIntegerFromExtInfo(KEY_REF_ANALYSIS_RETRY_COUNT);
        if (retryCount != null && retryCount >= MAX_RETRY_COUNT) {
            log.warn("【agentTask】参考图分析任务重试次数已达上限，不再创建新任务, taskId={}, retryCount={}", 
                    agentSessionTask.getId(), retryCount);
            return false;
        }

        return true;
    }
}