package ai.conrain.aigc.platform.service.model.query;

import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * TrainingRoundQuery
 *
 * @version TrainingRoundService.java
 */
@Data
public class TrainingRoundQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

        /** 主键，自增 */
        private Integer id;

        /** 关联的训练任务ID */
        private Integer taskId;

        /** 轮次编号，从1开始 */
        private Integer roundNumber;

        /** 轮次状态：pending/sampling/labeling/training/evaluating/completed/stopped */
        private String status;

        /** 本轮训练配置 */
        private String trainingConfig;

        /** 训练样本导出信息 */
        private String trainingSampleExport;

        /** 训练作业状态：pending/running/success/failed */
        private String trainStatus;

        /** 训练作业开始时间 */
        private Date trainStartedTime;

        /** 训练作业完成时间 */
        private Date trainCompletedTime;

        /** 模型标识 */
        private String modelScene;

        /** 模型版本 */
        private String modelVersion;

        /** 模型文件存储路径 */
        private String modelFilePath;

        /** 模型访问URL */
        private String modelUrl;

        /** 模型评估指标，如准确率、F1值等 */
        private String modelPerformanceMetrics;

        /** 扩展信息字段 */
        private String extInfo;

        /** 记录创建时间 */
        private Date createTime;

        /** 记录最后修改时间 */
        private Date modifyTime;

        /** 翻页参数：页面大小 */
        private Integer pageSize;

        /** 翻页参数：页数（从1开始） */
        private Integer pageNum;

        /** 排序指令（示例："id asc"） */
        private String orderBy;

}