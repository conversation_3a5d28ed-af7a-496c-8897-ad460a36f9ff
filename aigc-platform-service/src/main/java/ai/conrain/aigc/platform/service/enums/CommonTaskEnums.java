package ai.conrain.aigc.platform.service.enums;

import ai.conrain.aigc.platform.integration.aliyun.model.AliyunTryonTaskStatusEnum;
import ai.conrain.aigc.platform.integration.kling.KlingTaskStatusEnum;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

public interface CommonTaskEnums {
    enum TaskStatus {
        INIT, PENDING, RUNNING, COMPLETED, FAILED, CANCELED;

        public static boolean isEnd(String status) {
            return COMPLETED.name().equals(status) || FAILED.name().equals(status) || CANCELED.name().equals(status);
        }

        public static TaskStatus fromKlingTaskStatus(KlingTaskStatusEnum klStatus) {
            AssertUtil.assertNotNull(klStatus, ResultCode.BIZ_FAIL, "klStatus is null");

            switch (klStatus) {
                case completed:
                    return TaskStatus.COMPLETED;
                case processing:
                    return TaskStatus.RUNNING;
                case pending:
                    return TaskStatus.PENDING;
                case failed:
                    return TaskStatus.FAILED;
                default:
                    throw new RuntimeException("unknown klStatus:" + klStatus);
            }
        }

        public static TaskStatus fromAliyunTaskStatus(String aliyunStatus) {
            AliyunTryonTaskStatusEnum status = AliyunTryonTaskStatusEnum.getByName(aliyunStatus);
            AssertUtil.assertNotNull(status, ResultCode.BIZ_FAIL, "status is null");

            switch (status) {
                case PENDING:
                    return TaskStatus.PENDING;
                case PRE_PROCESSING:
                case RUNNING:
                case POST_PROCESSING:
                    return TaskStatus.RUNNING;
                case SUCCEEDED:
                    return TaskStatus.COMPLETED;
                case FAILED:
                    return TaskStatus.FAILED;
                default:
                    throw new RuntimeException("unknown status:" + status);
            }
        }


        public static TaskStatus fromHuiwaTaskStatus(Long huiwaStatus) {
            if (huiwaStatus == 4L) {
                return TaskStatus.COMPLETED;
            } else if (huiwaStatus == 2L) {
                return TaskStatus.RUNNING;
            } else if (huiwaStatus == -1L) {
                return TaskStatus.FAILED;
            } else if (huiwaStatus == -2L) {
                return TaskStatus.FAILED;
            }
            throw new RuntimeException("unknown huiwaStatus:" + huiwaStatus);
        }
    }

    enum TaskType {
        VIDEO_GENERATION, TRY_ON, TRY_ON_REFINER, REMOVE_WRINKLE, ERASE_BRUSH, AUTO_GEN_IMGS_BEFORE_DELIVER, BASIC_POSTURE_CREATIVE,
        IMPORT_STRUCTURAL_SCENE
    }

    enum OutTaskPlatform {
        KLING, ALIYUN, XIU_TU, HUI_WA
    }

    @Getter
    @AllArgsConstructor
    enum RelatedBizType {
        CREATE_VIDEO_CLIP("CREATE_VIDEO_CLIP", "视频创作任务片段"),

        CREATIVE_BATCH("CREATIVE_BATCH", "创作任务"),

        LORA_MODEL("LORA_MODEL", "模型"),

        ;

        private String code;

        private String desc;

        public static RelatedBizType from(String code) {
            return Arrays.stream(RelatedBizType.values()).filter(e -> e.getCode().equals(code)).findFirst().orElse(null);
        }
    }
}
