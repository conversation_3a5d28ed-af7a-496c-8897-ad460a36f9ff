package ai.conrain.aigc.platform.service.model.query;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * AgentSessionTaskQuery
 *
 * @version AgentSessionTaskService.java
 */
@Data
public class AgentSessionTaskQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

        /** 任务主键ID */
        private Integer id;

        /** 关联 agent_session.id */
        private Integer sessionId;

        /** 批次 ID */
        private Integer batchId;

        /** 前置任务ID（仅关联一个前置任务，第一个任务为NULL） */
        private Integer preTaskId;

        /** 多个前置任务 id（,分割） */
        private String preTaskIds;

        /** 用户ID（冗余，方便直接查询） */
        private Integer userId;

        /** 任务类型：CLOTH_ANALYSIS（服装分析）、REF_IMAGE_ANALYSIS（参考图分析）等 */
        private String taskType;

        /** 消息来源（system：系统恢复  user：用户发送） */
        private String messageFrom;

        /** 任务状态：ACTIVE/FINISHED/CANCELED/FAILED */
        private String status;

        /** 任务排序（由小到大排序） */
        private Integer orderNum;

        /** 回滚删除 正常：0  删除：1 */
        private Integer deletedByRollback;

        /** 回滚至哪个任务节点 */
        private Integer rollbackToTaskId;

        /** 任务完成时间 */
        private Date finishTime;

        /** 创建时间 */
        private Date createTime;

        /** 更新时间 */
        private Date modifyTime;

        /** 任务扩展信息 */
        private String extInfo;

        /** 任务结果（JSON 存结构化数据） */
        private String resultInfo;

        /** 翻页参数：页面大小 */
        private Integer pageSize;

        /** 翻页参数：页数（从1开始） */
        private Integer pageNum;

        /** 排序指令（示例："id asc"） */
        private String orderBy;

}