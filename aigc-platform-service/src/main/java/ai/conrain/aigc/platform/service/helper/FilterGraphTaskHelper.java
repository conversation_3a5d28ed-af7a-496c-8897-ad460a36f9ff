package ai.conrain.aigc.platform.service.helper;

import ai.conrain.aigc.platform.integration.ai.ComfyUIService;
import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.integration.javacv.JavaCVService;
import ai.conrain.aigc.platform.service.component.CreativeTaskService;
import ai.conrain.aigc.platform.service.component.PipelineService;
import ai.conrain.aigc.platform.service.component.ShortLinkService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.component.dispatch.ServerHelper;
import ai.conrain.aigc.platform.service.enums.CreativeStatusEnum;
import ai.conrain.aigc.platform.service.enums.ServerTypeEnum;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.model.vo.PipelineVO;
import ai.conrain.aigc.platform.service.model.vo.ServerVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.ComfyUIUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.FileUtils;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.*;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.FILTER_GRAPH_COMMAND;

/**
 * 视频抽帧 task 帮助类
 */
@Slf4j
@Component
public class FilterGraphTaskHelper {

    @Value("${ffmpeg.input.path}")
    private String inputPath;
    @Value("${ffmpeg.output.path}")
    private String outputPath;

    @Lazy
    @Autowired
    private FilterGraphTaskHelper self;

    @Autowired
    OssService ossService;
    @Autowired
    private OssHelper ossHelper;
    @Autowired
    private ComfyUIService comfyUIService;
    @Autowired
    private PipelineService pipelineService;
    @Autowired
    private ServerHelper serverHelper;
    @Autowired
    private CreativeTaskService creativeTaskService;
    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired
    private ShortLinkService shortLinkService;
    @Autowired
    private JavaCVService javaCVService;

    public void batchSyncStatus(List<CreativeTaskVO> list) {
        list.forEach(this::syncStatusEach);
    }

    private void syncStatusEach(CreativeTaskVO task) {
        switch (task.getStatus()) {
            case INIT, QUEUE -> toProcess(task);
            case PROCESSING -> toFinish(task);
            case FINISHED -> log.info("");
            default -> log.error("Unexpected task status: " + task.getStatus());
        }
    }

    private void toProcess(CreativeTaskVO task) {

        // ex. http://xxx.com/2222/33333/example_image.jpg?.Expires=....
        String videoUrl = task.getStringFromExtInfo(KEY_VIDEO_URL);
        String second = task.getExtInfo(KEY_SECOND);
        String frame = task.getStringFromExtInfo(KEY_FRAME);
        String commandPattern = systemConfigService.queryStringValue(FILTER_GRAPH_COMMAND, "");

        // ex. 2222/33333/example_image.jpg
        String objName = CommonUtil.getFilePathAndNameFromURL(videoUrl);
        // ex. example_image.jpg
        String fileName = CommonUtil.getFileNameWithExtension(objName);
        // ex. example_image
        String fileNameNoExt = CommonUtil.getFileNameWithoutExtension(objName);
        // ex. 2222/33333/
        String relativePath = FileUtils.combinePath(StringUtils.substringBeforeLast(objName, "/"), task.getBatchId().toString());
        // ex. /tmp/ffmpeg/output/2222/33333/444444.xxx
        String tempPath = ossService.downloadFile(objName, FileUtils.combinePath(inputPath, relativePath), fileNameNoExt);
        AssertUtil.assertNotBlank(tempPath, ResultCode.BIZ_FAIL, "oss download file fail");
        String prefix = ComfyUIUtils.buildFileNamePrefix(task.getId());
        try {
//            ffmpegService.filterGraph(relativePath, fileName, prefix, FILTER_GRAPH_OUTPUT_SUFFIX, frame + "/" + second, 4, commandPattern);
            // javacv 方案
            javaCVService.extractFramesByTime(tempPath, outputPath + relativePath, "product_" + task.getId(), second);
            File resultFile = new File(outputPath + relativePath);
            AssertUtil.assertTrue(resultFile.exists(), ResultCode.BIZ_FAIL, "result file doesn't exists");
            AssertUtil.assertTrue(resultFile.isDirectory(), ResultCode.BIZ_FAIL, "result file is not a directory");

            // 获取resultFile目录中的所有图片文件
            List<File> imageFiles = List.of(Objects.requireNonNull(resultFile.listFiles((dir, name) -> {
                String lowerName = name.toLowerCase();
                return name.startsWith(prefix)
                        && (lowerName.endsWith(".jpg") || lowerName.endsWith(".jpeg") || lowerName.endsWith(".png"));
            })));

            AssertUtil.assertNotEmpty(imageFiles, ResultCode.BIZ_FAIL, "result images is invalid");

            // 使用ossHelper.uploadParallel并行上传所有图片, 最大并行数量 10
            List<String> uploadedImageUrls = ossHelper.uploadFilesParallel(imageFiles, task.getUserId(), 10);
            // 转成短链接 id
            List<Integer> shortIds = shortLinkService.originalList2ShortIdList(uploadedImageUrls);
            // 将上传结果保存到target
            task.setResultImages(CommonUtil.listConverter(shortIds, String::valueOf));
            task.setStatus(CreativeStatusEnum.PROCESSING);
            creativeTaskService.updateByIdSelective(task);

        } catch (IOException e) {
            // 捕获不抛出, 让其他的任务继续执行
            log.error("[视频抽帧]任务执行失败, batchId: {}, taskId: {}, error: {}", task.getBatchId(), task.getId(), e.getMessage());
        }
    }

    private void toFinish(CreativeTaskVO task) {
        List<Integer> originalResults = CommonUtil.listConverter(task.getResultImages(), Integer::valueOf);
        // 已有的质量检测结果, 避免重复调接口
        JSONObject lowQualityResults = task.getExtInfo(KEY_LOW_QUALITY_RESULTS, JSONObject.class);
        if (lowQualityResults == null) {
            lowQualityResults = new JSONObject();
        }

        if (!StringUtils.equals(task.getExtInfo(KEY_FILTER_LOW_QUALITY), YES)) {
            for (Integer resultShortId : originalResults) {
                lowQualityResults.put(String.valueOf(resultShortId), false);
            }
        }

        List<Integer> unfinishedShortIds = new ArrayList<>();
        for (Integer item : originalResults) {
            if (!lowQualityResults.containsKey(String.valueOf(item))) {
                unfinishedShortIds.add(item);
            }
            // 防止图片过多, 这里截取一下
            if (unfinishedShortIds.size() > 20) {
                break;
            }
        }

        // 短链接id 转 原始 url
        List<String> unfinishedUrls = shortLinkService.shortIdList2OriginalList(unfinishedShortIds);
        Map<String, Boolean> lowQualityResultMap = lowQualityParallel(unfinishedUrls, task.getUserId(), 3);

        for (Map.Entry<String, Boolean> entry : lowQualityResultMap.entrySet()) {
            if (entry.getValue() != null) {
                // 检测之后再转为 shortId
                Integer shortId = shortLinkService.original2ShortId(entry.getKey());
                lowQualityResults.put(String.valueOf(shortId), entry.getValue());
            }
        }
        task.addExtInfo(KEY_LOW_QUALITY_RESULTS, lowQualityResults);
        if (CollectionUtils.isNotEmpty(originalResults) && originalResults.size() == lowQualityResults.size()) {
            List<Integer> resultShortIds = lowQualityResults.entrySet()
                    .stream()
                    .filter(entry -> !(Boolean) entry.getValue())
                    .map(Map.Entry::getKey)
                    .filter(StringUtils::isNotBlank)
                    .map(Integer::valueOf)
                    .toList();
            // 如果检测之后一个都没剩, 就取第一张图
            if (CollectionUtils.isEmpty(resultShortIds)) {
                resultShortIds = Collections.singletonList(originalResults.getFirst());
            }
            task.setResultImages(CommonUtil.listConverter(resultShortIds, String::valueOf));
            task.setBatchCnt(resultShortIds.size());
            task.setStatus(CreativeStatusEnum.FINISHED);
        }

        creativeTaskService.updateByIdSelective(task);
    }

    private Map<String, Boolean> lowQualityParallel(List<String> imageUrls, Integer userId, int parallelNum) {
        if (CollectionUtils.isEmpty(imageUrls)) {
            return new HashMap<>();
        }
        if (parallelNum <= 0) {
            parallelNum = imageUrls.size();
        }
        Map<String, Boolean> resultMap = new HashMap<>();
        for (int i = 0; i < imageUrls.size(); i += parallelNum) {

            int end = Math.min(i + parallelNum, imageUrls.size());
            List<String> batch = imageUrls.subList(i, end);

            List<CompletableFuture<AbstractMap.SimpleEntry<String, Boolean>>> futures = batch.stream()
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .map(imageUrl -> self.lowQualityAsync(imageUrl, userId)
                            .thenApply(result -> new AbstractMap.SimpleEntry<>(imageUrl, result))
                            .exceptionally(throwable -> {
                                log.error("调用 /low_quality 接口失败, imageUrl: {}", imageUrl);
                                return new AbstractMap.SimpleEntry<>(imageUrl, null);
                            })
                    ).toList();

            Map<String, Boolean> batchResult = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .thenApply(v -> futures.stream()
                            .map(CompletableFuture::join)
                            .filter(entry -> entry.getValue() != null)
                            .collect(Collectors.toConcurrentMap(
                                    Map.Entry::getKey,
                                    Map.Entry::getValue,
                                    (existing, replacement) -> existing
                            ))
                    ).join();

            resultMap.putAll(batchResult);
        }
        return resultMap;
    }

    @Async
    protected CompletableFuture<Boolean> lowQualityAsync(String imageUrl, Integer userId) {
        try {
            String serverUrl = fetchServer(userId);
            String base64 = ossHelper.downloadToBase64(imageUrl);
            Boolean result = comfyUIService.lowQuality(base64, serverUrl);
            return CompletableFuture.completedFuture(result);
        } catch (Exception e) {
            log.warn("call /low_quality fail, imageUrl: {}, error: {}", imageUrl, e.getMessage());
            CompletableFuture<Boolean> future = new CompletableFuture<>();
            future.completeExceptionally(e);
            return future;
        }
    }

    private String fetchServer(Integer userId) {
        PipelineVO pipeline = pipelineService.fetchByUserId(userId);
        List<ServerVO> modelServers = serverHelper.getServersByType(ServerTypeEnum.MODEL_SERVER, pipeline);
        AssertUtil.assertNotEmpty(modelServers, ResultCode.BIZ_FAIL, "当前管道无可用模型服务, pipelineId=" + pipeline.getId());

        // 随机获取一个模型服务
        Collections.shuffle(modelServers);
        return serverHelper.getServerUrl(modelServers.get(0));
    }

}
