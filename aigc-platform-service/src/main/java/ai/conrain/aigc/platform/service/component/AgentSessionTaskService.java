package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.AgentSessionTaskQuery;
import ai.conrain.aigc.platform.service.model.vo.AgentSessionTaskVO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 会话任务表：1个会话可包含多个子任务 Service定义
 *
 * <AUTHOR>
 * @version AgentSessionTaskService.java
 */
public interface AgentSessionTaskService {

    /**
     * 查询会话任务表：1个会话可包含多个子任务对象
     *
     * @param id 主键
     * @return 返回结果
     */
    AgentSessionTaskVO selectById(Integer id);

    /**
     * 删除会话任务表：1个会话可包含多个子任务对象
     *
     * @param id 主键
     */
    void deleteById(Integer id);

    /**
     * 添加会话任务表：1个会话可包含多个子任务对象
     *
     * @param agentSessionTask 对象参数
     * @return 返回结果
     */
    AgentSessionTaskVO insert(AgentSessionTaskVO agentSessionTask);

    /**
     * 修改会话任务表：1个会话可包含多个子任务对象
     *
     * @param agentSessionTask 对象参数
     */
    void updateByIdSelective(AgentSessionTaskVO agentSessionTask);

    /**
     * 带条件批量查询会话任务表：1个会话可包含多个子任务列表
     *
     * @param query 查询条件
     *              return 结果
     */
    List<AgentSessionTaskVO> queryAgentSessionTaskList(AgentSessionTaskQuery query);

    /**
     * 带条件查询会话任务表：1个会话可包含多个子任务数量
     *
     * @param query 查询条件
     *              return 记录条数
     */
    Long queryAgentSessionTaskCount(AgentSessionTaskQuery query);

    /**
     * 带条件分页查询会话任务表：1个会话可包含多个子任务
     *
     * @param query 查询条件
     *              return 分页结果
     */
    PageInfo<AgentSessionTaskVO> queryAgentSessionTaskByPage(AgentSessionTaskQuery query);

    /**
     * 根据 sessionId 查询对应的任务列表
     *
     * @param id sessionId
     * @return agent 任务列表
     */
    List<AgentSessionTaskVO> queryAgentSessionTaskBySessionId(Integer id);

    /**
     * 用户确认开始策划
     * @param idList id集合
     */
    void makeSurePlan(@Valid List<Integer> idList);
}