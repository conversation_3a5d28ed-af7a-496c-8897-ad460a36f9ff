package ai.conrain.aigc.platform.service.model.query;

import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * ImageGroupCaptionQuery
 *
 * @version ImageGroupCaptionService.java v 0.1 2025-08-14 11:09:51
 */
@Data
public class ImageGroupCaptionQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

        /** 主键 */
        private Integer id;

        /** 图片组ID */
        private Integer imageGroupId;

        /** 图片标注ID列表 */
        private String imageCaptionIds;

        /** 多人打标结果（意见一致，分歧） */
        private String result;

        /** 标注 */
        private String caption;

        /** 标注日志 */
        private String captionLog;

        /** 标注版本 */
        private String captionVersion;

        /** 创建时间 */
        private Date createTime;

        /** 修改时间 */
        private Date modifyTime;


        /** 翻页参数：页面大小 */
        private Integer pageSize;

        /** 翻页参数：页数（从1开始） */
        private Integer pageNum;

        /** 排序指令（示例："id asc"） */
        private String orderBy;

}