package ai.conrain.aigc.platform.service.model.query;

import com.alibaba.fastjson.JSONObject;

import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * MerchantPreferenceQuery
 *
 * @version MerchantPreferenceService.java v 0.1 2024-11-12 08:02:24
 */
@Data
public class MerchantPreferenceQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 用户id */
    private Integer userId;

    /** 类型，PREFERENCE、FAVORITE */
    private String type;

    /** 备注 */
    private String memo;

    /** 模特id配置 */
    private String faces;

    /** 场景id配置 */
    private String scenes;

    /** 操作者id */
    private Integer operatorId;


    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 服装搭配信息 */
    private String clothCollocation;

    /** 扩展信息 */
    private JSONObject extInfo;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

}
