package ai.conrain.aigc.platform.service.component.agent.constants;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Agent 相关常量定义
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class AgentConstants {

    // 错误信息
    public static final String KEY_ERROR_RESULT = "errorResult";
    // 分析标识
    public static final String KEY_ANALYSIS = "analysis";
    // 打标标识
    public static final String KEY_PRE_CAPTION = "preCaption";
    // 组件内容常量
    public static final String KEY_COMPONENT_CONTENT_DATA = "componentContentData";

    //=============================== 翻译相关常量 Key =====================================
    // 翻译状态标记
    public static final String KEY_TRANSLATION_STATUS = "translationStatus";
    // 翻译完成标记
    public static final String TRANSLATION_STATUS_COMPLETED = "completed";
    // 翻译进行中标记
    public static final String TRANSLATION_STATUS_IN_PROGRESS = "in_progress";
    // 翻译失败标记
    public static final String TRANSLATION_STATUS_FAILED = "failed";

    //=============================== 字段修改跟踪相关常量 Key =====================================
    // 预标注结果带修改状态版本
    public static final String KEY_PRE_LABEL_TASK_RESULT_WITH_STATUS = "translationPreLabelTaskResult";
    // 详细分析结果带修改状态版本
    public static final String KEY_ANALYSIS_CLOTHES_TASK_RESULT_WITH_STATUS = "translationAnalysisClothesTaskResult";
    // 最终版本结果（包含修改状态和英文翻译）
    public static final String KEY_FINAL_RESULT = "finalResult";
    // 字段修改记录
    public static final String KEY_FIELD_MODIFICATION_RECORD = "fieldModificationRecord";

    //================================ 服装分析相关常量 Key =====================================
    // 详细分析任务ID
    public static final String KEY_ANALYSIS_CLOTHES_TASK_ID = "analysisClothesTaskId";
    // 失败详细分析任务ID集合
    public static final String KEY_FAILED_ANALYSIS_CLOTHES_TASK_ID_LIST = "failedAnalysisClothesTaskIdList";
    // 详细分析任务重试次数
    public static final String KEY_ANALYSIS_CLOTHES_RETRY_COUNT = "analysisClothesRetryCount";
    // 详细分析任务结果
    public static final String KEY_ANALYSIS_CLOTHES_TASK_RESULT = "analysisClothesTaskResult";
    // 详细分析任务结果翻译版本
    public static final String KEY_TRANSLATION_ANALYSIS_CLOTHES_TASK_RESULT = "translationAnalysisClothesTaskResult";

    // 预标注任务ID
    public static final String KEY_PRE_LABEL_TASK_ID = "preLabelTaskId";
    // 失败详细预标注任务ID集合
    public static final String KEY_FAILED_PRE_LABEL_TASK_ID_LIST = "failedPreLabelTaskIdList";
    // 预标注任务重试次数
    public static final String KEY_PRE_LABEL_RETRY_COUNT = "preLabelRetryCount";
    // 预标注任务结果
    public static final String KEY_PRE_LABEL_TASK_RESULT = "preLabelTaskResult";
    // 预标注任务结果的可编辑版本
    public static final String KEY_EDITABLE_PRE_LABEL_TASK_RESULT = "editablePreLabelTaskResult";

    // 服装分析任务 ID key
    public static final String KEY_CLOTHES_TASK_ID = "clothesTaskId";
    
    // 重试次数限制
    public static final int MAX_RETRY_COUNT = 3;



    //=============================== 参考图分析相关常量 Key =====================================
    // 详细分析参考图任务ID
    public static final String KEY_REF_ANALYSIS_TASK_ID = "refAnalysisTaskId";
    // 失败 详细分析参考图任务ID集合
    public static final String KEY_FAILED_REF_ANALYSIS_TASK_ID_LIST = "failedRefAnalysisTaskIdList";
    // 详细分析参考图任务重试次数
    public static final String KEY_REF_ANALYSIS_RETRY_COUNT = "refAnalysisRetryCount";
    // 详细分析参考图任务结果
    public static final String KEY_REF_ANALYSIS_TASK_RESULT = "refAnalysisTaskResult";
    // 详细分析参考图任务结果的翻译版本
    public static final String KEY_TRANSLATION_REF_ANALYSIS_TASK_RESULT = "translationRefAnalysisTaskResult";
    // 是否确认开始策划
    public static final String KEY_IS_SURE_START_PLAN = "isSureStartPlan";

    // 参考图分析任务 ID key
    public static final String KEY_REFERENCE_TASK_ID = "referenceTaskId";


    //=============================== 参考图推荐相关常量 Key =====================================
    // 详细分析参考图任务结果
    public static final String KEY_STYLE_SCENE_RECOMMENDED_RESULT = "styleSceneRecommendedResult";
    // 完整返回信息
    public static final String KEY_STYLE_SCENE_RECOMMENDED_FULL_RESPONSE = "styleSceneRecommendedFullResponse";



}
