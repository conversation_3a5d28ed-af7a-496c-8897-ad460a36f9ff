package ai.conrain.aigc.platform.service.component.agent.entity.params;

import lombok.Data;

import java.util.List;

/**
 * 服装分析任务参数
 */
@Data
public class ClothAnalysisParams {

    /**
     * 服装图片URL
     */
    private String clothesImage;

    /**
     * 服装类型
     */
    private String clothesType;

    /**
     * 性别
     */
    private String gender;

    /**
     * 尺码
     */
    private String bigSize;

    /**
     * 年龄段
     */
    private String ageRange;

    /**
     * 会话ID
     */
    private Integer sessionId;

    /**
     * 服装名称
     */
    private String clothesName;

    /**
     * 参考图片列表
     */
    private List<String> referenceImageList;

    /**
     * 流派
     */
    private List<String> intendedUse;

    /**
     * 拍摄计划
     */
    private List<String> shootingPlan;

    /**
     * 合适的平台
     */
    private List<String> suitablePlatform;
}