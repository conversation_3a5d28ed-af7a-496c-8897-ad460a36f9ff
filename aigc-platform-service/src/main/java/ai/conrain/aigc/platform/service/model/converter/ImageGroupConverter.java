package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.pgsql.entity.ImageGroupDO;
import ai.conrain.aigc.platform.service.model.query.ImageGroupQuery;
import ai.conrain.aigc.platform.dal.example.ImageGroupExample;
import ai.conrain.aigc.platform.service.model.vo.ImageGroupVO;

import com.alibaba.fastjson2.JSON;
import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * ImageGroupConverter
 *
 * @version ImageGroupService.java v 0.1 2025-07-30 08:19:30
 */
public class ImageGroupConverter {

    /**
     * DO -> VO
     */
    public static ImageGroupVO do2VO(ImageGroupDO from) {
        ImageGroupVO to = new ImageGroupVO();
        to.setId(from.getId());
        to.setType(from.getType());
        to.setImageIds(JSON.parseArray(from.getImageIds(), Integer.class));
        to.setMetadata(JSON.parseObject(from.getMetadata()));
        to.setExtInfo(JSON.parseObject(from.getExtInfo()));
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * VO -> DO
     */
    public static ImageGroupDO vo2DO(ImageGroupVO from) {
        ImageGroupDO to = new ImageGroupDO();
        to.setId(from.getId());
        to.setImageIds(JSON.toJSONString(from.getImageIds()));
        to.setMetadata(JSON.toJSONString(from.getMetadata()));
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> Example
     */
    public static ImageGroupExample query2Example(ImageGroupQuery from) {
        ImageGroupExample to = new ImageGroupExample();
        ImageGroupExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getGroupId())) {
            c.andGroupIdEqualTo(from.getGroupId());
        }
        if (!ObjectUtils.isEmpty(from.getType())) {
            c.andTypeEqualTo(from.getType());
        }
        if (!ObjectUtils.isEmpty(from.getImageIds())) {
            c.andImageIdsEqualTo(JSON.toJSONString(from.getImageIds()));
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //逻辑删除过滤
        for (ImageGroupExample.Criteria each : to.getOredCriteria()) {
            each.andLogicalDeleted(false);
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.setOffset((from.getPageNum() - 1) * from.getPageSize());
            to.setRows(from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<ImageGroupVO> doList2VOList(List<ImageGroupDO> list) {
        return CommonUtil.listConverter(list, ImageGroupConverter::do2VO);
    }
}