package ai.conrain.aigc.platform.service.component.agent.entity.response;

import ai.conrain.aigc.platform.service.component.agent.entity.params.ImageDetail;
import ai.conrain.aigc.platform.service.enums.ClothShootGenreEnum;
import lombok.Data;

import java.util.List;

/**
 * 风格场景参考图推荐响应数据
 */
@Data
public class StyleSceneRecommendedResponse {

    /**
     * 流派类型 {@link ClothShootGenreEnum 流派枚举}
     */
    private String clothShoot;

    /**
     * 流派名称
     */
    private String clothShootName;

    /**
     * 推荐原因，需要通过 Gpt 进行汇总
     */
    private String reason;

    /**
     * 相似度
     */
    private String styleSimilarity;

    /**
     * 图片详情列表
     */
    private List<ImageDetail> imageDetailList;

    /** 查询任务 ID */
    private Integer searchId;
}
