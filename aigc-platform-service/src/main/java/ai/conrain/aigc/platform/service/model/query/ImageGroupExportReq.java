package ai.conrain.aigc.platform.service.model.query;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * ImageGroupExportReq
 */
@Data
public class ImageGroupExportReq implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Integer id;

    /** 模型 */
    private String model = "gemini-flash";

    /** 图片ID列表 */
    private List<Integer> imageIds;

    /** 标签 */
    private String tag;

    /** 是否多人打标 */
    private boolean multiUser;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;
}
