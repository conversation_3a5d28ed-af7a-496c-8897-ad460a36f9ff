package ai.conrain.aigc.platform.service.component.agent.entity.params;


import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 推荐流派入参
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RecommendedStyleSceneParams extends ClothAnalysisParams {

    // 服装分析任务 ID
    private Integer clothesTaskId;

    // 参考图任务 ID
    private Integer referenceTaskId;

    // 是否重新推荐
    private Boolean isReCommended;

    // 流派推荐



    // 套图推荐
    // 图片 id
    private Integer specifiedRetImgCaptionId;

    // 查询任务id
    private Integer specifiedSearchId;

    // 流派信息（需要转换为枚举）
    private String specifiedGenre;
}
