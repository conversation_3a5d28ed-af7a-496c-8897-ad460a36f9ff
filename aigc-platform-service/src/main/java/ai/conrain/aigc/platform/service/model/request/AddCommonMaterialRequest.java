package ai.conrain.aigc.platform.service.model.request;

import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.model.biz.CommonMaterialDetail;
import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class AddCommonMaterialRequest implements AddMaterialRequest, Serializable {
    private static final long serialVersionUID = -1767422660578150331L;

    @NotBlank
    private String name;

    // 素材类型，大分类，服装｜脸
    @NotBlank
    private String materialType;

    // 素材子类型 male/female @see MaterialCategory
    private String materialSubType;

    // 年龄区段
    @NotBlank
    private String ageRange;

    // 素材标签
    private String materialTags;

    //素材详情，图片列表等
    private CommonMaterialDetail materialDetail;

    private String captionPrompt;

    private String waterMarkDesc;

    /** Y/N */
    private String noshowFace;

    private Integer trainRepeatTimes;

    /** 场景类型 */
    private String sceneType;

    /** 服装款式 */
    private List<String> clothCategory;

    //是否需要预处理被裁剪的人脸 Y|N
    private String preprocessCensoredFace;

    //是否专属
    private boolean exclusive;

    /** 打标类型 */
    private String labelType;

    /** 底模版本 */
    private String baseModelVersion;

    //服装的男女款式描述（man/woman/child，默认值是woman，解决历史数据为空的情况）
    @Override
    public String getClothStyleType4TrainParam() {
        if (StringUtils.equalsIgnoreCase(materialSubType, CommonConstants.child)) {
            return CommonConstants.child;
        } else if (StringUtils.equalsIgnoreCase(materialSubType, CommonConstants.male)) {
            return CommonConstants.man;
        } else if (StringUtils.equalsIgnoreCase(materialSubType, CommonConstants.female)) {
            return CommonConstants.woman;
        } else if (StringUtils.equalsIgnoreCase(materialSubType, CommonConstants.unisex)) {
            return CommonConstants.unisex;
        } else {
            throw new RuntimeException("clothStyleType is invalid:" + materialSubType);
        }
    }
}
