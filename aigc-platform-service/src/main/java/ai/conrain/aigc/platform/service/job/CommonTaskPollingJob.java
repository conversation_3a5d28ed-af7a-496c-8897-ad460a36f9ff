/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.job;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import ai.conrain.aigc.platform.service.component.CommonTaskService;
import ai.conrain.aigc.platform.service.enums.CommonTaskEnums.TaskStatus;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.CommonTaskQuery;
import ai.conrain.aigc.platform.service.model.vo.CommonTaskVO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static ai.conrain.aigc.platform.service.enums.CommonTaskEnums.TaskType.IMPORT_STRUCTURAL_SCENE;
import static ai.conrain.aigc.platform.service.job.JobMdcWrap.wrapProcess;

/**
 * 通用任务轮询job
 *
 * <AUTHOR>
 * @version : CommonTaskPollingJob.java, v 0.1 2025/9/28 11:10 renxiao.wu Exp $
 */
@Slf4j
@Component
public class CommonTaskPollingJob extends JavaProcessor {
    private static final List<String> PROCESS_TASK_TYPES = List.of(IMPORT_STRUCTURAL_SCENE.name());
    @Autowired
    private CommonTaskService commonTaskService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        return wrapProcess(() -> {
            String traceId = MDC.get("traceId");

            CommonTaskQuery query = new CommonTaskQuery();
            query.setTaskTypeList(PROCESS_TASK_TYPES);
            query.setTaskStatus(TaskStatus.INIT.name());
            query.setPageNum(1);
            query.setPageSize(100);
            PageInfo<CommonTaskVO> pageInfo = commonTaskService.queryCommonTaskByPage(query);
            if (CollectionUtils.isEmpty(pageInfo.getList())) {
                log.info("【通用任务轮询】未找到需要处理的任务，直接跳过");
                return new ProcessResult(true);
            }

            pageInfo.getList().forEach(task -> {
                log.info("【通用任务轮询】开始处理任务：{}", task);
                MDC.put("traceId", traceId + "_" + task.getId());
                commonTaskService.polling(task);

            });

            return new ProcessResult(true);
        });
    }
}
