package ai.conrain.aigc.platform.service.component.agent.factory;

import ai.conrain.aigc.platform.service.component.agent.AgentTaskHandler;
import ai.conrain.aigc.platform.service.enums.agent.AgentSessionTaskTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Agent任务处理器工厂类
 */
@Component
public class AgentTaskHandlerFactory {
    
    @Autowired
    private List<AgentTaskHandler<?>> taskHandlers;
    
    private final Map<String, AgentTaskHandler<?>> handlerMap = new HashMap<>();

    public AgentTaskHandlerFactory(List<AgentTaskHandler<?>> taskHandlers) {
        this.taskHandlers = taskHandlers;
    }

    @PostConstruct
    public void init() {
        for (AgentTaskHandler<?> handler : taskHandlers) {
            handlerMap.put(handler.getSupportedTaskType(), handler);
        }
    }
    
    /**
     * 根据任务类型获取对应的任务处理器
     * @param taskType 任务类型
     * @return 任务处理器
     */
    public AgentTaskHandler<?> getTaskHandler(String taskType) {
        return handlerMap.get(taskType);
    }
    
    /**
     * 根据任务类型获取对应的任务处理器
     * @param taskTypeEnum 任务类型枚举
     * @return 任务处理器
     */
    public AgentTaskHandler<?> getTaskHandler(AgentSessionTaskTypeEnum taskTypeEnum) {
        if (taskTypeEnum == null) {
            return null;
        }
        return handlerMap.get(taskTypeEnum.getCode());
    }
}