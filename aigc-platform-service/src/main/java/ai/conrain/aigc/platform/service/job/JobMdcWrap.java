/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.job;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import java.util.function.Supplier;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

/**
 * job mdc wrap
 *
 * <AUTHOR>
 * @version : JobMdcWrap.java, v 0.1 2025/9/28 11:22 renxiao.wu Exp $
 */
public abstract class JobMdcWrap {
    private final static String traceId = "traceId";
    private final static String env = "env";

    /**
     * job执行逻辑包装
     *
     * @param processLogic job执行逻辑
     * @return job执行结果
     */
    public static ProcessResult wrapProcess(Supplier<ProcessResult> processLogic) {
        String uuid = CommonUtil.uuid();
        MDC.put(traceId, uuid);
        MDC.put(env, StringUtils.upperCase(EnvUtil.getEnv()));
        OperationContextHolder.setContext(CommonUtil.mockSystemContext());

        try {
            return processLogic.get();
        } finally {
            MDC.remove(env);
            MDC.remove(traceId);
            OperationContextHolder.clean();
        }
    }
}
