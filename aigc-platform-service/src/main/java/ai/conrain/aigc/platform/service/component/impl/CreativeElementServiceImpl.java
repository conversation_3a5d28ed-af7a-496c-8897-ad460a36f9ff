package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.service.util.MaterialModelUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;

import ai.conrain.aigc.platform.dal.dao.CreativeElementDAO;
import ai.conrain.aigc.platform.dal.entity.CreativeElementDO;
import ai.conrain.aigc.platform.dal.example.CreativeElementExample;
import ai.conrain.aigc.platform.integration.ai.model.FileVO;
import ai.conrain.aigc.platform.integration.ai.model.QueueResult;
import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.integration.gpt.AIModel.GptResponse;
import ai.conrain.aigc.platform.integration.gpt.AIModel.Status;
import ai.conrain.aigc.platform.integration.gpt.AIService;
import ai.conrain.aigc.platform.integration.gpt.OpenAIService;
import ai.conrain.aigc.platform.service.component.CreativeBatchService;
import ai.conrain.aigc.platform.service.component.CreativeElementService;
import ai.conrain.aigc.platform.service.component.DistributorCustomerService;
import ai.conrain.aigc.platform.service.component.MaterialInfoService;
import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.constants.SystemConstants;
import ai.conrain.aigc.platform.service.enums.CameraAngleEnum;
import ai.conrain.aigc.platform.service.enums.ClothTypeEnum;
import ai.conrain.aigc.platform.service.enums.CreativeBizTypeEnum;
import ai.conrain.aigc.platform.service.enums.CreativeStatusEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.enums.ElementConfigKeyEnum;
import ai.conrain.aigc.platform.service.enums.ElementStatusEnum;
import ai.conrain.aigc.platform.service.enums.MaterialModelStatusEnum;
import ai.conrain.aigc.platform.service.enums.ModelTypeEnum;
import ai.conrain.aigc.platform.service.enums.ModelVersionEnum;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.helper.OssHelper;
import ai.conrain.aigc.platform.service.model.biz.CommonMaterialDetail;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.CreativeElementConverter;
import ai.conrain.aigc.platform.service.model.query.CreativeBatchQuery;
import ai.conrain.aigc.platform.service.model.query.CreativeElementQuery;
import ai.conrain.aigc.platform.service.model.query.MaterialModelQuery;
import ai.conrain.aigc.platform.service.model.request.IntelligentRecommendationRequest;
import ai.conrain.aigc.platform.service.model.request.QueryAutoGenElementRecommendRequest;
import ai.conrain.aigc.platform.service.model.request.ResetOrderRequest;
import ai.conrain.aigc.platform.service.model.vo.ClothCategoryVO;
import ai.conrain.aigc.platform.service.model.vo.ClothCategoryVO.SubcategoryVO;
import ai.conrain.aigc.platform.service.model.vo.ClothTypeScopeItem;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.DistributorCustomerVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialInfoVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.model.vo.ShowImageVO;
import ai.conrain.aigc.platform.service.model.vo.SystemConfigVO;
import ai.conrain.aigc.platform.service.model.vo.UserFaceSceneVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.DateUtils;
import ai.conrain.aigc.platform.service.util.ElementUtils;
import ai.conrain.aigc.platform.service.util.MapConvertUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import ai.conrain.aigc.platform.service.util.PromptModificationUtil;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.CREATIVE_ELEMENT_ID;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.DEFAULT_IMAGE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ANALYSIS_JSON;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_DELIVERY_OPERATOR;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_DELIVERY_TIME;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_FACE_IMAGE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_IS_LORA;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_MODEL_FINISHED;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_OPEN_SCOPE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_PURE_BACKGROUND;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_PURE_RGB;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_STYLE_IMAGE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TRAIN_TYPE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.MASK_LOSS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.YES;

/**
 * CreativeElementService实现
 *
 * <AUTHOR>
 * @version CreativeElementService.java v 0.1 2024-05-06 04:08:22
 */
@Slf4j
@Service
public class CreativeElementServiceImpl implements CreativeElementService {
    private static final String CACHE_ELEMENT_ROOT_KEY = "_cache_element_root_";
    private static final int DEFAULT_QUERY_SIZE = 5000;

    @Autowired
    private CreativeElementService self;

    /**
     * DAO
     */
    @Autowired
    private CreativeElementDAO creativeElementDAO;

    @Autowired
    private OssHelper ossHelper;

    @Autowired
    private MaterialModelService materialModelService;

    @Autowired
    private MaterialInfoService materialInfoService;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private OpenAIService openAIService;

    @Autowired
    private UserService userService;

    @Autowired
    private PromptModificationUtil promptModificationUtil;

    @Autowired
    private TairService tairService;

    @Autowired
    private CreativeBatchService creativeBatchService;

    @Autowired
    private DistributorCustomerService distributorCustomerService;

    @Autowired
    private AIService aiService;

    private final Cache<Integer, List<DistributorCustomerVO>> distributorCustomerCache = CacheBuilder.newBuilder()
        .expireAfterWrite(10, TimeUnit.SECONDS).build();

    @Override
    public CreativeElementVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        CreativeElementDO data = creativeElementDAO.selectByPrimaryKey(id);
        if (null == data) {
            return null;
        }

        return CreativeElementConverter.do2VO(data);
    }

    @Override
    public CreativeElementVO lockById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        CreativeElementDO data = creativeElementDAO.lockByPrimaryKey(id);
        if (null == data) {
            return null;
        }

        return CreativeElementConverter.do2VO(data);
    }

    @Override
    public CreativeElementVO selectByIdWithChildren(Integer id) {
        List<CreativeElementDO> list = creativeElementDAO.selectByPrimaryKeyWithChildren(id);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        CreativeElementVO root = null;
        List<CreativeElementVO> children = new ArrayList<>();
        for (CreativeElementDO data : list) {
            if (data.getId().equals(id)) {
                root = CreativeElementConverter.do2VO(data);
                if (CollectionUtils.isNotEmpty(list)) {
                    // 创建者身份
                    if (data.getUserId() != null) {
                        UserVO user = userService.selectById(data.getUserId());
                        if (user != null) {
                            root.setUserRoleType(user.getRoleType());
                        }
                    }
                }
            } else if (data.getParentId().equals(id)) {
                children.add(CreativeElementConverter.do2VO(data));
            }
        }

        if (root != null) {
            root.setChildren(children);
        }
        return root;
    }

    @Override
    public CreativeElementVO selectPrimaryInfoByIdWithChildren(Integer id) {
        List<CreativeElementDO> list = creativeElementDAO.selectPrimaryInfoByIdWithChildren(id);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        CreativeElementVO root = null;
        List<CreativeElementVO> children = new ArrayList<>();
        for (CreativeElementDO data : list) {
            if (data.getId().equals(id)) {
                root = CreativeElementConverter.do2VO(data);
            } else if (data.getParentId().equals(id)) {
                children.add(CreativeElementConverter.do2VO(data));
            }
        }

        if (root != null) {
            root.setChildren(children);
        }
        return root;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        CreativeElementVO data = selectById(id);
        if (data == null) {
            log.warn("要删除的数据记录不存在");
            return;
        }

        // 删除关联的lora模型
        if (data.getLoraModelId() != null) {
            materialModelService.deleteById(data.getLoraModelId());
        }

        // 删除子节点
        if (data.getLevel() == 2) {
            List<CreativeElementVO> children = data.getChildren();
            if (CollectionUtils.isNotEmpty(children)) {
                for (CreativeElementVO child : children) {
                    int n = creativeElementDAO.deleteByPrimaryKey(child.getId());
                    AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除CreativeElement失败");
                }
            }
        }

        int n = creativeElementDAO.deleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除CreativeElement失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreativeElementVO insert(CreativeElementVO creativeElement) {
        AssertUtil.assertNotNull(creativeElement, ResultCode.PARAM_INVALID, "creativeElement is null");
        AssertUtil.assertTrue(creativeElement.getId() == null, ResultCode.PARAM_INVALID,
            "creativeElement.id is present");

        // 创建时间、修改时间兜底
        if (creativeElement.getCreateTime() == null) {
            creativeElement.setCreateTime(new Date());
        }

        if (creativeElement.getModifyTime() == null) {
            creativeElement.setModifyTime(new Date());
        }

        if (creativeElement.getParentId() == null) {
            CreativeElementVO parent = queryRootKey(creativeElement.getConfigKey());

            AssertUtil.assertNotNull(parent, ResultCode.PARAM_INVALID, "父级元素不存在");
            creativeElement.setParentId(parent.getId());
        }

        if (CollectionUtils.isNotEmpty(creativeElement.getType())) {
            creativeElement.setType(
                creativeElement.getType().stream().filter(e -> !StringUtils.isNumeric(e)).collect(Collectors.toList()));
        }

        if (creativeElement.getIsNew() == null) {
            creativeElement.setIsNew(false);
        }

        int maxOrder = 0;

        creativeElement.setOrder(maxOrder + 1);
        resetShowImages(creativeElement);

        CreativeElementDO data = CreativeElementConverter.vo2DO(creativeElement);
        data.setDeleted(false);
        int n = creativeElementDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建CreativeElement失败");
        AssertUtil.assertNotNull(data.getId(), "新建CreativeElement返回id为空");
        creativeElement.setId(data.getId());

        if (creativeElement.isOpChildren()) {
            updateChildren(creativeElement, false);
        }

        return creativeElement;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreativeElementVO copyInsert(CreativeElementVO creativeElement) {
        AssertUtil.assertNotNull(creativeElement, ResultCode.PARAM_INVALID, "creativeElement is null");
        AssertUtil.assertTrue(creativeElement.getId() == null, ResultCode.PARAM_INVALID,
            "creativeElement.id is present");

        // 创建时间、修改时间兜底
        if (creativeElement.getCreateTime() == null) {
            creativeElement.setCreateTime(new Date());
        }

        if (creativeElement.getModifyTime() == null) {
            creativeElement.setModifyTime(new Date());
        }

        if (creativeElement.getParentId() == null) {
            CreativeElementVO parent = queryRootKey(creativeElement.getConfigKey());

            AssertUtil.assertNotNull(parent, ResultCode.PARAM_INVALID, "父级元素不存在");
            creativeElement.setParentId(parent.getId());
        }

        if (creativeElement.getIsNew() == null) {
            creativeElement.setIsNew(false);
        }

        int maxOrder = 0;

        creativeElement.setOrder(maxOrder + 1);
        resetShowImages(creativeElement);

        CreativeElementDO data = CreativeElementConverter.vo2DO(creativeElement);
        data.setDeleted(false);
        int n = creativeElementDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建CreativeElement失败");
        AssertUtil.assertNotNull(data.getId(), "新建CreativeElement返回id为空");
        creativeElement.setId(data.getId());

        if (creativeElement.isOpChildren()) {
            updateChildren(creativeElement, false);
        }

        return creativeElement;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateById(CreativeElementVO creativeElement) {
        CreativeElementVO origin = this.selectById(creativeElement.getId());
        AssertUtil.assertNotNull(origin, ResultCode.PARAM_INVALID, "未找到元素");

        AssertUtil.assertNotNull(creativeElement, ResultCode.PARAM_INVALID, "creativeElement is null");
        AssertUtil.assertTrue(creativeElement.getId() != null, ResultCode.PARAM_INVALID, "creativeElement.id is null");
        // 修改时间必须更新
        creativeElement.setModifyTime(new Date());

        // 更新展示图状态
        if (creativeElement.getShowImageStatus()) {
            creativeElement.addExtInfo(CommonConstants.IS_SET_POSTURE, YES);
        }

        if (CollectionUtils.isNotEmpty(creativeElement.getType())) {
            creativeElement.setType(
                creativeElement.getType().stream().filter(e -> !StringUtils.isNumeric(e)).collect(Collectors.toList()));
        }

        resetShowImages(creativeElement);

        Integer operatorUserId = OperationContextHolder.getOperatorUserId();
        if (operatorUserId != null && !CommonUtil.isSystemUser(operatorUserId)) {
            creativeElement.setOperatorId(operatorUserId);
        }

        if (creativeElement.isOpChildren()) {
            updateChildren(creativeElement, true);
        }

        ElementUtils.correctType(creativeElement, creativeElement.getChildren());

        boolean isDelivery = origin.getStatus() != ElementStatusEnum.PROD
                             && creativeElement.getStatus() == ElementStatusEnum.PROD;

        //审核不通过的状态
        boolean reviewFailed = origin.getStatus() != ElementStatusEnum.REVIEW_FAILED
                               && creativeElement.getStatus() == ElementStatusEnum.REVIEW_FAILED;

        // bugfix。针对重新转测试的情况。  测试--》发布线上--〉测试。但是模型表中的状态不会更改为测试
        boolean isTest = origin.getStatus() != ElementStatusEnum.TEST
                         && creativeElement.getStatus() == ElementStatusEnum.TEST;
        // 设置交付信息
        if (isDelivery && StringUtils.isBlank(origin.getExtInfo(KEY_DELIVERY_TIME, String.class))) {
            creativeElement.addExtInfo(KEY_DELIVERY_TIME, DateUtils.formatTime(new Date()));
        }
        // 发布线上或审核不通过情况下都设置操作人
        if (isDelivery || reviewFailed) {
            creativeElement.addExtInfo(KEY_DELIVERY_OPERATOR, OperationContextHolder.getOperatorUserId());
        }

        CreativeElementDO data = CreativeElementConverter.vo2DO(creativeElement);
        int n = creativeElementDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新CreativeElement失败，影响行数:" + n);

        // 变更模型状态
        // 如果是发布线上/审核不通过/转为测试都要更新lora模型
        if ((isDelivery || reviewFailed || isTest) && origin.getLoraModelId() != null) {
            MaterialModelVO model = materialModelService.selectById(origin.getLoraModelId());
            if (model != null) {
                MaterialModelVO target = new MaterialModelVO();
                target.setId(model.getId());
                // 如果是发布线上设置模型状态为启用。 如果是审核不通过，设置模型状态为禁用
                // 并修复测试状态，模型的状态不会同步的情况
                if (isDelivery) {
                    target.setStatus(MaterialModelStatusEnum.ENABLED.getCode());
                } else if (reviewFailed) {
                    target.setStatus(MaterialModelStatusEnum.DISABLED.getCode());
                } else {
                    target.setStatus(MaterialModelStatusEnum.TESTING.getCode());
                }
                materialModelService.innerUpdate(target);

                log.info("当前元素状态改为发布，将其关联的lora模型状态置为审核通过,elementId:{},loraModelId:{}",
                    data.getId(), model.getId());
            }
        }
    }

    @Override
    public List<CreativeElementVO> queryAllByLevel(CreativeTypeEnum type, CreativeBizTypeEnum bizType,
                                                   boolean needAll) {
        return queryAllByLevel(type, bizType, false, needAll);
    }

    @Override
    public List<CreativeElementVO> queryAllByLevel(List<String> configKeys, CreativeBizTypeEnum bizType) {
        return queryAllByLevel(configKeys, bizType, false, false);
    }

    @Override
    public List<CreativeElementVO> queryAllByLevelOnlyExclusive(CreativeTypeEnum creativeType,
                                                                CreativeBizTypeEnum bizType, boolean needAll) {
        return queryAllByLevel(creativeType, bizType, true, needAll);
    }

    @Override
    public List<CreativeElementVO> queryAllByLevelOnlyExclusive(List<String> configKeys, CreativeBizTypeEnum bizType) {
        return queryAllByLevel(configKeys, bizType, true, false);
    }

    @Override
    public List<CreativeElementVO> queryByLevelAndKey(String key, boolean needChildren) {
        CreativeElementQuery query = new CreativeElementQuery();
        query.setConfigKey(key);
        query.setPageSize(DEFAULT_QUERY_SIZE);
        query.setPageNum(1);
        if (needChildren) {
            query.setLevels(Arrays.asList(2, 3));
        } else {
            query.setLevel(2);
        }

        if (!OperationContextHolder.isBackRole()) {
            query.setStatus(ElementStatusEnum.PROD.getCode());
        }

        PageInfo<CreativeElementVO> pageInfo = queryByPage(query);
        List<CreativeElementVO> list = pageInfo.getList();

        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        // 找到key一致的element，并将所有的list合并
        List<CreativeElementVO> ret = CreativeElementConverter.toListByLevel(list, 2, null);

        fillModelTrainingStatus(ret);

        return ret;
    }

    @Override
    public List<CreativeElementVO> queryByLevel(CreativeElementQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");
        if (query.getPageSize() == null) {
            query.setPageSize(DEFAULT_QUERY_SIZE);
        }
        if (query.getPageNum() == null) {
            query.setPageNum(1);
        }
        if (CollectionUtils.isEmpty(query.getLevels())) {
            query.setLevels(Arrays.asList(2, 3));
        }

        if (!OperationContextHolder.isBackRole()) {
            query.setStatus(ElementStatusEnum.PROD.getCode());

            if (!OperationContextHolder.isDistributorRole()) {
                query.setScopeUserId(OperationContextHolder.getMasterUserId());
            }
        }

        PageInfo<CreativeElementVO> pageInfo = queryByPage(query);
        List<CreativeElementVO> list = pageInfo.getList();

        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        // 找到key一致的element，并将所有的list合并
        return CreativeElementConverter.toListByLevel(list, 2, null);
    }

    private void fillModelTrainingStatus(List<CreativeElementVO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<CreativeElementVO> elements = list.stream().filter(item -> (StringUtils.equals(item.getConfigKey(),
            ElementConfigKeyEnum.FACE.name()) || StringUtils.equals(item.getConfigKey(),
            ElementConfigKeyEnum.SCENE.name())) && item.getLoraModelId() != null).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(elements)) {
            return;
        }
        List<Integer> ids = elements.stream().map(CreativeElementVO::getLoraModelId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        MaterialModelQuery query = new MaterialModelQuery();
        query.setIds(ids);

        List<MaterialModelVO> models = materialModelService.queryListWithBlogs(query);
        if (CollectionUtils.isEmpty(models)) {
            return;
        }
        Map<Integer, MaterialModelVO> modelMap = models.stream().collect(
            Collectors.toMap(MaterialModelVO::getId, item -> item));

        List<MaterialInfoVO> materials = materialInfoService.batchQueryByIds(
            models.stream().map(m -> m.getClothLoraTrainDetail().getOriginalMaterialId()).collect(Collectors.toList()));
        Map<Integer, MaterialInfoVO> materialMap = materials.stream().collect(
            Collectors.toMap(MaterialInfoVO::getId, item -> item));

        for (CreativeElementVO item : list) {
            if (item.getLoraModelId() == null) {
                continue;
            }

            MaterialModelVO model = modelMap.get(item.getLoraModelId());
            if (model == null) {
                continue;
            }

            item.setContentOrStyle(
                model.getClothLoraTrainDetail() != null ? model.getClothLoraTrainDetail().getContentOrStyle() : null);

            // 专属风格场景，取样本前10张作为展示图
            if (item.getConfigKey().equals(ElementConfigKeyEnum.SCENE.name()) && item.isStyleScene()
                && item.isExclusive()) {
                MaterialInfoVO materialInfoVO = materialMap.get(
                    model.getClothLoraTrainDetail().getOriginalMaterialId());
                item.setShowImgs4PrivateStyleScene(getSampleImgs(materialInfoVO));
            }

            if (StringUtils.equals(model.getStatus(), MaterialModelStatusEnum.IN_TRAINING.getCode())) {
                item.setLoraTraining(true);
                if (model.getClothLoraTrainDetail() != null && model.getClothLoraTrainDetail().getLabel() != null
                    && QueueResult.QueueCodeEnum.COMPLETED.equals(
                    model.getClothLoraTrainDetail().getLabel().getStatus())) {
                    item.setLabelFinished(true);
                }
                if (model.getClothLoraTrainDetail() != null && CommonConstants.YES.equals(
                    model.getClothLoraTrainDetail().getLoraConfirmed())) {
                    item.setLoraConfirmed(true);
                }
                if (model.getClothLoraTrainDetail() != null && model.getClothLoraTrainDetail().getLora() != null) {
                    item.setLoraTaskStatus(model.getClothLoraTrainDetail().getLora().getStatus().name());
                    item.setLoraTaskStatusDesc(model.getClothLoraTrainDetail().getLora().getStatusDesc());
                }
            }

            if (StringUtils.equals(model.getExtInfo(MASK_LOSS, String.class), YES)) {
                item.setMaskLoss(true);
            }
        }
    }

    private List<String> getSampleImgs(MaterialInfoVO materialInfoVO) {
        if (materialInfoVO == null) {
            return null;
        }
        if (materialInfoVO.getMaterialDetail() == null) {
            return null;
        }
        try {
            CommonMaterialDetail detail = materialInfoVO.getMaterialDetail().toJavaObject(CommonMaterialDetail.class);
            if (detail != null && CollectionUtils.isNotEmpty(detail.getImgUrls())) {
                return detail.getImgUrls().subList(0, Math.min(detail.getImgUrls().size(), 10));
            }
        } catch (Exception e) {
            log.error("getSampleImgs error", e);
        }
        return null;
    }

    @Override
    public CreativeElementVO queryRootKey(String key) {
        String cacheKey = CACHE_ELEMENT_ROOT_KEY + key;
        CreativeElementVO cache = tairService.getObject(cacheKey, CreativeElementVO.class);
        if (cache != null) {
            return cache;
        }

        CreativeElementDO creativeElementDO = creativeElementDAO.selectRootByKey(key);

        CreativeElementVO element = CreativeElementConverter.do2VO(creativeElementDO);

        // 缓存10分钟
        tairService.setObject(cacheKey, element, 60 * 10);

        return element;
    }

    @Override
    public void resetOrder(ResetOrderRequest request) {
        List<CreativeElementDO> list = new ArrayList<>();
        request.getItems().forEach(item -> {
            CreativeElementDO data = new CreativeElementDO();
            data.setId(item.getId());
            data.setOrder(item.getOrder());
            list.add(data);
        });
        creativeElementDAO.batchResetOrder(list);
    }

    @Override
    public long countCustom(CreativeElementQuery query) {
        query.setBelong(ModelTypeEnum.CUSTOM.getCode());
        CreativeElementExample example = CreativeElementConverter.query2Example(query);
        return creativeElementDAO.countByExample(example);
    }

    @Override
    public PageInfo<CreativeElementVO> queryCustomByPage(CreativeElementQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");
        query.setBelong(ModelTypeEnum.CUSTOM.getCode());
        return this.queryByPage(query);
    }

    @Override
    public PageInfo<CreativeElementVO> queryByPage(CreativeElementQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                              && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
            "pageNum or pageSize is invalid,pageNum:" + query.getPageNum() + ",pageSize:" + query.getPageSize());

        if (StringUtils.isBlank(query.getOrderBy())) {
            if (OperationContextHolder.getRoleType() == RoleTypeEnum.ADMIN) {
                String recentCreate = "create_time > '" + DateUtils.formatTime(DateUtils.addWeeks(new Date(), -1))
                                      + "'";
                // 优先逾期包括20个小时未处理 > 销售 > 客户
                query.setOrderBy("if(status not in ('PROD','REVIEW_FAILED') and create_time < '" + DateUtils.formatTime(
                    DateUtils.addHours(new Date(), -20)) + "' and " + recentCreate
                                 + " and user_role not in ('ADMIN', 'OPERATOR'),1,0) desc, "
                                 + "if(status not in ('PROD','REVIEW_FAILED') and user_role = 'DISTRIBUTOR' and "
                                 + recentCreate + ",1,0) desc,id desc");
            } else {
                query.setOrderBy("is_new desc,if(status='PROD',0,1),`order`,modify_time desc");
            }
        }

        List<String> subcategoryList = JSONObject.parseArray(
                systemConfigService.queryValueByKey(SystemConstants.CLOTH_CATEGORY_CFG), ClothCategoryVO.class).stream()
            .map(ClothCategoryVO::getChildren).flatMap(List::stream).map(SubcategoryVO::getSubcategory).collect(
                Collectors.toList());
        if (CollectionUtils.isNotEmpty(query.getClothCategory())) {
            // fix: 剔除不存在的子类别
            query.getClothCategory().retainAll(subcategoryList);
        }

        // 封装渠道商过滤条件
        if (OperationContextHolder.isDistributorRole()) {
            // 获取客户列表
            List<DistributorCustomerVO> distributorCustomers = getDistributorCustomers();

            if (CollectionUtils.isNotEmpty(distributorCustomers)) {
                // 提取渠道商关联客户id集合
                List<Integer> userIdList = distributorCustomers.stream().map(
                    DistributorCustomerVO::getCustomerMasterUserId).collect(Collectors.toList());

                // 将自身的id也天添加进去
                userIdList.add(OperationContextHolder.getMasterUserId());

                // 拼装查询参数
                query.setDistributorCustomerIds(userIdList);
            }
        }

        if (query.getIsExclusive() != null) {
            String openScope = null;
            if (OperationContextHolder.isBackRole()) {
                openScope = query.getIsExclusive() ? "private" : "public";
            } else if (OperationContextHolder.isDistributorRole()) {
                // 渠道商查询专属场景时，依赖前面已设置的distributorCustomerIds进行权限控制
                openScope = query.getIsExclusive() ? "private" : "public";
            } else {
                openScope = query.getIsExclusive() ? OperationContextHolder.getMasterUserId().toString() : "public";
            }
            query.setOpenScope(openScope);
        } else {
            // 当isExclusive为null时，查询所有场景（公共+专属），但需要为渠道商添加权限控制
            if (OperationContextHolder.isDistributorRole()) {
                // 渠道商查询所有场景时，设置特殊标识
                query.setOpenScope("distributor_all");
            }
        }

        // 此处筛选需要额外筛选出专属场景
        if (query.getIsHasShowImage() != null && query.getIsHasShowImage()) {
            String tempOpenScope = null;
            if (OperationContextHolder.isBackRole() || OperationContextHolder.isDistributorRole()) {
                tempOpenScope = "private";
            } else {
                tempOpenScope = OperationContextHolder.getMasterUserId().toString();
            }
            query.setTempOpenScope(tempOpenScope);
        }

        CreativeBizTypeEnum bizType = CreativeBizTypeEnum.getByCode(query.getBizType());
        if (bizType != null && !CreativeBizTypeEnum.ALL.equals(bizType)) {
            if (bizType.equals(CreativeBizTypeEnum.LOOK)) {
                query.setType(KEY_PURE_BACKGROUND);
            } else {
                query.setTypeNotEqual(KEY_PURE_BACKGROUND);
            }
        }

        if (!OperationContextHolder.isBackRole() || OperationContextHolder.getRoleType() == RoleTypeEnum.DEMO_ACCOUNT) {
            query.setStatus("PROD");
        }

        if (query.isOnlyLora()) {
            if (StringUtils.equals(query.getConfigKey(), ElementConfigKeyEnum.FACE.name())) {
                query.setFaceOnlyLora(true);
            } else {
                query.setStyleScene(true);
            }
        }

        // 查询未构建数据
        if (query.isNotBuild()) {
            // 1、公共场景
            query.setOpenScope("public");

            // 2、风格场景
            query.setIsLora(Boolean.TRUE);

            // 3、未生成或设置参考图，在convert中处理
        }

        PageInfo<CreativeElementVO> page = new PageInfo<>();

        CreativeElementExample example = CreativeElementConverter.query2Example(query);
        long totalCount = creativeElementDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<CreativeElementDO> data = creativeElementDAO.selectByExample(example);

        List<CreativeElementVO> list = CreativeElementConverter.doList2VOList(data);
        if (CollectionUtils.isNotEmpty(list)) {
            fillModelTrainingStatus(list);
        }

        if (CollectionUtils.isNotEmpty(list)) {
            // 补充修改人、操作人昵称
            List<Integer> userIds = list.stream().map(CreativeElementVO::getOperatorId).collect(Collectors.toList());
            list.forEach(item -> {
                Integer deliveryId = item.getExtInfo(KEY_DELIVERY_OPERATOR, Integer.class);
                if (deliveryId != null) {
                    userIds.add(deliveryId);
                }
            });
            List<UserVO> ops = userService.batchQueryById(userIds);

            list.forEach(item -> {
                item.setOperatorNick(ops.stream().filter(user -> user.getId().equals(item.getOperatorId())).findFirst()
                    .map(UserVO::getNickName).orElse(null));

                Integer deliveryId = item.getExtInfo(KEY_DELIVERY_OPERATOR, Integer.class);
                if (deliveryId != null) {
                    item.setDeliveryName(ops.stream().filter(user -> user.getId().equals(deliveryId)).findFirst()
                        .map(UserVO::getNickName).orElse(null));
                }
            });

            // 补充客户昵称
            List<UserVO> users = userService.batchQueryById(
                list.stream().map(CreativeElementVO::getUserId).collect(Collectors.toList()));

            list.forEach(item -> {
                item.setUserNick(users.stream().filter(user -> user.getId().equals(item.getUserId())).findFirst()
                    .map(UserVO::getNickName).orElse(null));
                item.setUserRoleType(users.stream().filter(user -> user.getId().equals(item.getUserId())).findFirst()
                    .map(UserVO::getRoleType).orElse(null));
            });
        }

        page.setList(list);
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Override
    public CreativeElementVO queryByIdAndLevel(Integer id, int level) {
        CreativeElementVO element = selectById(id);

        if (element == null) {
            return null;
        }

        if (element.getLevel() > level) {
            return queryByIdAndLevel(element.getParentId(), level);
        }
        return element;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void clone(Integer id, boolean fullCopy) {
        clone(id, fullCopy, null);
    }

    @Override
    public void clone(Integer id, boolean fullCopy, Integer userId) {
        CreativeElementVO origin = selectByIdWithChildren(id);
        AssertUtil.assertNotNull(origin, ResultCode.PARAM_INVALID, "未找到元素");

        // 1.复制父节点
        CreativeElementVO parent = cloneAndStore(origin, null, fullCopy, userId);

        if (CollectionUtils.isEmpty(origin.getChildren())) {
            return;
        }

        // 2.复制子节点
        origin.getChildren().forEach(item -> cloneAndStore(item, parent.getId(), fullCopy, userId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetStyleScene(CreativeElementVO e, List<FileVO> labelFiles, String loraName) {
        e.addExtInfo(CommonConstants.KEY_LORA_PATH, loraName);
        e.addExtInfo(KEY_IS_LORA, YES);
        e.addExtInfo(KEY_MODEL_FINISHED, YES);
        if (StringUtils.isBlank(e.getExtInfo(CommonConstants.KEY_OPEN_SCOPE, String.class))) {
            e.addExtInfo(CommonConstants.KEY_OPEN_SCOPE, "ALL");
        }
        e.setOpChildren(true);
        e.setType(new ArrayList<>(e.getType()));
        e.getType().add(ModelVersionEnum.FLUX.getCode());
        e.getType().add(CommonConstants.STYLE_SCENE); // 风格场景标

        if (CollectionUtils.isEmpty(labelFiles)) {
            log.error("风格场景参数初始化异常，打标文件为空");
            throw new BizException(ResultCode.SYS_ERROR);
        }

        log.info("风格场景参数初始化，打标文件size={}", labelFiles.size());

        int idx = 1;
        for (FileVO file : labelFiles) {
            CreativeElementVO child = CreativeElementConverter.loadFromFile(file, e);
            if (child == null) {
                continue;
            }

            if (e.getChildren() == null) {
                e.setChildren(new ArrayList<>());
            }
            child.getType().add(Integer.toString(idx++));
            child.setConfigKey(ElementConfigKeyEnum.SCENE.name());

            // 回填图片
            String fileName = CommonUtil.getFileNameWithoutExtension(file.getFileName());
            FileVO imageFile = labelFiles.stream().filter(
                f -> StringUtils.equals(f.getType(), "img") && StringUtils.startsWith(f.getFileName(), fileName)
                     && !StringUtils.endsWith(f.getFileDir(), "/mask")).findFirst().orElse(null);
            child.addExtInfo(KEY_STYLE_IMAGE, imageFile != null ? imageFile.getImgUrl() : null);

            e.getChildren().add(child);
        }

        updateById(e);
    }

    @Override
    public void resetLoraFace(CreativeElementVO e, List<FileVO> labelFiles, String loraName,
                              String faceImgRelativePath) {

        e.addExtInfo(CommonConstants.KEY_FACE_LORA, loraName);
        e.addExtInfo(CommonConstants.KEY_SWAP_TYPE, "LORA");
        if (StringUtils.isBlank(e.getExtInfo(CommonConstants.KEY_OPEN_SCOPE, String.class))) {
            e.addExtInfo(CommonConstants.KEY_OPEN_SCOPE, "ALL");
        }
        e.addExtInfo(CommonConstants.KEY_LORA_SWAP_FACE, "Y");
        e.addExtInfo(CommonConstants.KEY_REPAIR_FACE_TYPE, "PW");
        e.addExtInfo(CommonConstants.KEY_FACE_LORA_STRENGTH, "0.5");

        e.setType(CollectionUtils.isNotEmpty(e.getType()) ? new ArrayList<>(e.getType()) : new ArrayList<>());
        e.getType().add(ModelVersionEnum.FLUX.getCode());

        List<CreativeElementVO> children = CreativeElementConverter.loadMainFromFile(labelFiles, e);
        if (CollectionUtils.isNotEmpty(children)) {
            e.setOpChildren(true);

            // 设置其中正面子元素的id
            CreativeElementVO origin = selectByIdWithChildren(e.getId());
            if (CollectionUtils.isNotEmpty(origin.getChildren())) {
                origin.getChildren().forEach(child -> {
                    children.stream().filter(target -> target.getType().contains(ModelVersionEnum.FLUX.getCode())
                                                       && CameraAngleEnum.getOrientationByStr(target.getType())
                                                          == CameraAngleEnum.getOrientationByStr(child.getType()))
                        .findFirst().ifPresent(target -> target.setId(child.getId()));
                });
            }

            e.setChildren(children);
        }

        updateById(e);
    }

    @Override
    public void batchCorrectType(List<Integer> ids) {
        for (Integer id : ids) {
            CreativeElementVO element = selectById(id);
            if (element == null || CollectionUtils.isEmpty(element.getChildren())) {
                log.info("元素不存在或没有子元素，id={}", id);
                continue;
            }

            element.getChildren().forEach(child -> {

                CreativeElementVO e = selectById(child.getId());
                // 优化type
                ElementUtils.correctType(e);

                creativeElementDAO.updateByPrimaryKeySelective(CreativeElementConverter.vo2DO(e));
            });

            ElementUtils.correctType(element, element.getChildren());
            creativeElementDAO.updateByPrimaryKeySelective(CreativeElementConverter.vo2DO(element));
        }
    }

    /**
     * 获取场景的服装类型范围（后台GPT离线计算）
     */
    @Override
    public List<ClothTypeScopeItem> autoCalcClothTypeScopeItem4Scene(Integer sceneElementId) {
        List<ClothTypeScopeItem> allClothTypeScopeCfgItems = systemConfigService.queryClothTypeScopeCfg();
        AssertUtil.assertTrue(CollectionUtils.isNotEmpty(allClothTypeScopeCfgItems), ResultCode.SYS_ERROR,
            "clothTypeScopeItems配置为空");

        // 从children中获取服装评价所需要的图片样本
        CreativeElementVO sceneElement = this.selectById(sceneElementId);
        AssertUtil.assertNotNull(sceneElement, ResultCode.PARAM_INVALID, "未找到场景元素");

        List<String> imgUrls = getSampleImgs(sceneElement);
        if (CollectionUtils.isEmpty(imgUrls)) {
            return null;
        }

        String prompt = buildPrompt(allClothTypeScopeCfgItems);

        String res = invokeVlmOpenAI(prompt, imgUrls);
        if (CommonUtil.isValidJsonArray(res)) {
            return parseResponse(res, allClothTypeScopeCfgItems);
        } else {
            log.error("自动计算服装类型范围失败，prompt={},imgUrls={},res={}", prompt, imgUrls, res);
            return null;
        }
    }

    @Override
    public List<CreativeElementVO> queryRecommendAutoGenElementsByCloth(QueryAutoGenElementRecommendRequest request) {

        AssertUtil.assertNotEmpty(request.getClothImgUrls(), ResultCode.PARAM_INVALID, "clothImgUrls不能为空");
        // 获取所有风格场景，这里不处理数据水平权限，由上层controller处理过滤，CreativeElementController#pruneForFront

        // 获取所有level=2的lora scene元素，且已经完成了风格特征计算的场景
        List<CreativeElementVO> styleScenes = getStyleScenes(request);

        if (CollectionUtils.isEmpty(styleScenes)) {
            log.info("根据服装参数获取风格场景为空，无法推荐，styleScenes={}", styleScenes);
            return null;
        }

        // 计算当前服装的样本图特征
        List<ClothTypeScopeItem> clothFeatures = calcClothFeatures(request);
        if (!isValidFeatures(clothFeatures)) {
            log.info("服装特征无效，clothFeatures={}", clothFeatures);
            return null;
        }

        // 遍历所有场景，计算服装特征与场景特征的匹配度
        List<SceneMatchScore> matchScores = getSceneMatchScores(styleScenes, clothFeatures);

        // 按分数降序排序并取前10个（匹配度越高越靠前），前端展示最多前5位
        List<CreativeElementVO> ret = matchScores.stream().sorted((a, b) -> Double.compare(b.getScore(), a.getScore()))
            .limit(10).map(SceneMatchScore::getScene).collect(Collectors.toList());

        log.info("根据服装参数推荐风格场景，ret={}", ret);
        return ret;
    }

    @Override
    public List<Integer> getElementIdsWithChildren(Integer elementId) {
        CreativeElementVO element = selectByIdWithChildren(elementId);
        if (element == null) {
            return null;
        }

        List<Integer> elementIds = new ArrayList<>();
        if (element.getLevel() <= 2 && CollectionUtils.isNotEmpty(element.getChildren())) {
            elementIds.addAll(element.getChildren().stream().map(CreativeElementVO::getId).toList());
        }
        // 添加自身
        elementIds.add(elementId);

        return elementIds;
    }

    @Override
    public void batchInitTrainType(List<Integer> ids) {
        for (Integer id : ids) {
            CreativeElementVO element = selectById(id);
            if (element == null) {
                log.info("元素不存在，id={}", id);
                continue;
            }

            if (element.getLoraModelId() == null || StringUtils.isNotBlank(
                element.getExtInfo(KEY_TRAIN_TYPE, String.class))) {
                log.info("元素不存在或已经初始化过trainType，id={},trainType={}", id,
                    element.getExtInfo(KEY_TRAIN_TYPE, String.class));
                continue;
            }

            MaterialModelVO model = materialModelService.selectById(element.getLoraModelId());
            if (model == null) {
                log.info("模型不存在，id={}", id);
                continue;
            }

            String contentOrStyle = model.getClothLoraTrainDetail().getContentOrStyle();
            if (StringUtils.isBlank(contentOrStyle)) {
                contentOrStyle = "content";
            }
            element.addExtInfo(KEY_TRAIN_TYPE, contentOrStyle);

            creativeElementDAO.updateByPrimaryKeySelective(CreativeElementConverter.vo2DO(element));
        }
    }

    @Override
    public List<CreativeElementVO> batchQueryByIds(List<Integer> ids) {
        CreativeElementQuery query = new CreativeElementQuery();
        query.setIds(ids);
        List<CreativeElementDO> list = creativeElementDAO.selectByExample(
            CreativeElementConverter.query2Example(query));
        return CreativeElementConverter.doList2VOList(list);
    }

    @Override
    public List<CreativeElementVO> batchQueryIncludesDelByIds(List<Integer> ids) {
        CreativeElementExample example = new CreativeElementExample();
        example.createCriteria().andIdIn(ids).andDeletedIn(Arrays.asList(true, false));

        List<CreativeElementDO> list = creativeElementDAO.selectByExampleWithSimple(example);
        return CreativeElementConverter.doList2VOList(list);
    }

    @Override
    public List<CreativeElementVO> batchQueryByIdsWithChildren(List<Integer> ids) {
        CreativeElementQuery query = new CreativeElementQuery();
        query.setIdsOrParents(ids);
        List<CreativeElementDO> list = creativeElementDAO.selectByExample(
            CreativeElementConverter.query2Example(query));

        List<CreativeElementVO> elements = CreativeElementConverter.doList2VOList(list);

        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        Map<String, Integer> parentDepthMap = new HashMap<>();
        elements.forEach(e -> {
            Integer current = parentDepthMap.get(e.getConfigKey());
            if (current == null) {
                current = e.getLevel();
            } else if (current > e.getLevel()) {
                current = e.getLevel();
            }
            parentDepthMap.put(e.getConfigKey(), current);
        });

        List<CreativeElementVO> parentIds = elements.stream().filter(
            e -> e.getLevel().equals(parentDepthMap.get(e.getConfigKey()))).collect(Collectors.toList());

        parentIds.forEach(e -> {
            e.setChildren(elements.stream().filter(c -> c.getParentId() != null && c.getParentId().equals(e.getId()))
                .collect(Collectors.toList()));
        });

        return parentIds;
    }

    @Override
    public void batchCorrectLora(List<Integer> ids) {
        for (Integer id : ids) {
            CreativeElementVO element = selectById(id);
            if (element == null || element.getLoraModelId() == null) {
                log.info("元素不存在或非lora模特或场景，id={}", id);
                continue;
            }

            materialModelService.initRelatedElementConfig(element.getLoraModelId());
        }
    }

    @Override
    public void updateExperimental(Integer id, boolean experimental) {
        CreativeElementVO creativeElementVO = selectById(id);
        AssertUtil.assertNotNull(creativeElementVO, "元素不存在");

        creativeElementVO.addExtInfo(CommonConstants.KEY_EXPERIMENTAL, experimental);

        CreativeElementDO creativeElementDO = new CreativeElementDO();
        creativeElementDO.setId(id);
        creativeElementDO.setExtInfo(creativeElementVO.getExtInfo().toJSONString());
        int n = creativeElementDAO.updateByPrimaryKeySelective(creativeElementDO);
        AssertUtil.assertTrue(n == 1, "元素不存在");
    }

    @Override
    public Map<String, Integer> countNeedProcessByKeys(List<String> list) {
        Map<String, Map<String, Object>> map = creativeElementDAO.countNeedProcessByKeys(list);
        if (MapUtils.isEmpty(map)) {
            return null;
        }
        Map<String, Integer> resultMap = new HashMap<>();

        for (Map<String, Object> row : map.values()) {
            String key = row.get("configKey").toString();
            Integer cnt = row.get("cnt") == null ? 0 : Integer.parseInt(row.get("cnt").toString());
            resultMap.put(key, cnt);
        }

        return resultMap;
    }

    @Override
    public List<UserFaceSceneVO> selectAllUserElementByDate(String startDate, String endDate) {

        List<Map<String, Object>> objects = creativeElementDAO.selectAllUserElementByDate(startDate, endDate);

        // 将objects转换为UserFaceSceneVO列表,其中 Object 中的属性分别是user_id、face_cnt、scene_cnt
        List<UserFaceSceneVO> userFaceSceneList = new ArrayList<>();

        if (CollectionUtils.isEmpty(objects)) {
            return userFaceSceneList;
        }

        for (Map<String, Object> obj : objects) {
            try {
                UserFaceSceneVO vo = new UserFaceSceneVO();
                vo.setUserId(MapConvertUtil.getValue(obj, "user_id", Integer.class));
                vo.setFaceCnt(MapConvertUtil.getValue(obj, "face_cnt", Integer.class));
                vo.setSceneCnt(MapConvertUtil.getValue(obj, "scene_cnt", Integer.class));

                userFaceSceneList.add(vo);
            } catch (Exception e) {
                log.error("转换用户元素数据异常: {}", e.getMessage(), e);
            }
        }

        return userFaceSceneList;
    }

    @Override
    public boolean isChildModel(CreativeElementVO face) {
        if (face == null) {
            return false;
        }

        boolean childModel = ElementUtils.isChildModel(face);
        if (childModel) {
            return true;
        }

        if (face.getLevel() > 2) {
            Integer parentId = face.getParentId();
            CreativeElementVO parent = selectById(parentId);
            return ElementUtils.isChildModel(parent);
        }

        return false;
    }

    @Override
    public List<CreativeElementVO> queryUserExclusiveElement(CreativeElementQuery query, int maxCount) {
        // 只查询 2 级
        query.setLevels(Collections.singletonList(2));

        // 只查询 lora 场景
        query.setOnlyLora(Boolean.TRUE);

        // 设置查询专属数据
        query.setIsExclusive(Boolean.TRUE);

        // 只查询非实验数据
        query.setOnlyExperimental(Boolean.FALSE);

        // 查询子记录大于 0
        query.setHasChildren(Boolean.TRUE);

        // 设置正式环境
        query.setStatus(ElementStatusEnum.PROD.getCode());

        // 设置查询数量
        query.setPageNum(1);
        query.setPageSize(maxCount);

        PageInfo<CreativeElementVO> creativeElementVOPageInfo = queryByPage(query);

        // 查询数据并返回
        return creativeElementVOPageInfo.getList();
    }

    @Override
    public void batchSetScenePose(List<Integer> sceneIdList) {

        if (CollectionUtils.isNotEmpty(sceneIdList)) {
            // 递归处理数据
            sceneIdList.forEach(sceneId -> {

                // 查询level 为 2 和 3 场景
                CreativeElementVO creativeElementVO = selectPrimaryInfoByIdWithChildren(sceneId);
                if (creativeElementVO == null) {
                    log.info("场景不存在，id={}", sceneId);
                    return;
                }

                // 获取场景的子场景
                List<CreativeElementVO> children = creativeElementVO.getChildren();
                if (CollectionUtils.isEmpty(children)) {
                    return;
                }
                // 姿势 id 集合
                List<Integer> poseIdList = children.stream().map(CreativeElementVO::getId).distinct().collect(
                    Collectors.toList());

                // 批量查询当前场景中关联的最新的第一的创作记录
                log.info("开始查询场景ID {} 下 {} 个姿势的最新创作记录", sceneId, poseIdList.size());
                Map<Integer, CreativeBatchVO> latestCreativeBatchMap = getLatestCreativeBatchByPoseIds(poseIdList);

                if (MapUtils.isNotEmpty(latestCreativeBatchMap)) {
                    log.info("场景ID {} 查询到 {} 个姿势的最新创作记录", sceneId, latestCreativeBatchMap.size());

                    children.forEach(childElement -> {
                        CreativeBatchVO creativeBatchVO = latestCreativeBatchMap.get(childElement.getId());
                        if (creativeBatchVO == null) {
                            log.debug("场景ID {} ,姿势ID {} 未找到对应的创作记录", sceneId, childElement.getId());
                            return;
                        }

                        List<String> resultImageList = creativeBatchVO.getResultImages();
                        if (CollectionUtils.isEmpty(resultImageList)) {
                            log.debug("场景ID {} ,姿势ID {} 的创作记录没有结果图片", sceneId, childElement.getId());
                            return;
                        }

                        int randomIndex = (int)(Math.random() * resultImageList.size());

                        // 随机获取一个结果设置进入扩展信息中
                        String selectedImage = resultImageList.get(randomIndex);
                        childElement.addExtInfo(CommonConstants.SHOW_IMAGE, selectedImage);

                        log.debug("场景ID {} ,姿势ID {} 设置展示图片成功", sceneId, childElement.getId());
                    });
                } else {
                    log.info("场景ID {} ,未查询到任何姿势的创作记录", sceneId);
                }

                // 设置完成的数据进行打标
                creativeElementVO.addExtInfo(CommonConstants.IS_SET_POSTURE, YES);

                // 重新设置 children
                creativeElementVO.setChildren(children);

                // 操作子元素
                creativeElementVO.setOpChildren(Boolean.TRUE);

                // 更新场景 信息
                self.updateById(creativeElementVO);
            });
        }
    }

    @Override
    public Map<Integer, ShowImageVO> selectShowImageByScene(Integer sceneId) {
        // 查询level 为 2 和 3 场景
        CreativeElementVO creativeElementVO = selectPrimaryInfoByIdWithChildren(sceneId);
        if (creativeElementVO == null) {
            log.info("【获取场景所有姿势展示图】场景不存在，id={}", sceneId);
            return null;
        }

        // 获取场景的子场景
        List<CreativeElementVO> children = creativeElementVO.getChildren();
        if (CollectionUtils.isEmpty(children)) {
            log.info("【获取场景所有姿势展示图】该场景未配置姿势，id={}", sceneId);
            return null;
        }

        // 提取姿势 id 列表，并按照 order 排序
        List<Integer> poseIdList = children.stream().sorted(Comparator.comparing(CreativeElementVO::getOrder)).map(
            CreativeElementVO::getId).collect(Collectors.toList());

        // 再次提取数据,其中key为id，value为ShowImageVO,ShowImageVO中的selectedShowImage为extInfo.showImage
        Map<Integer, ShowImageVO> poseShowImageMap = children.stream().sorted(
            Comparator.comparing(CreativeElementVO::getOrder)).collect(
            Collectors.toMap(CreativeElementVO::getId, child -> {
                ShowImageVO showImageVO = new ShowImageVO();
                showImageVO.setSelectedShowImage(child.getExtInfo(CommonConstants.SHOW_IMAGE, String.class));
                return showImageVO;
            }));

        // 初始化查询条件
        CreativeBatchQuery creativeBatchQuery = new CreativeBatchQuery();
        creativeBatchQuery.setPostureIdList(poseIdList);
        creativeBatchQuery.setType(CreativeTypeEnum.POSE_SAMPLE_DIAGRAM.getCode());
        creativeBatchQuery.setBizTag(CommonConstants.POSE_SAMPLE_DIAGRAM);
        creativeBatchQuery.setStatus(CreativeStatusEnum.FINISHED.getCode());
        creativeBatchQuery.setOrderBy("create_time desc");
        // 查询所有符合条件的创作记录
        List<CreativeBatchVO> creativeBatchVOList = creativeBatchService.queryCreativeBatchList(creativeBatchQuery);
        if (CollectionUtils.isEmpty(creativeBatchVOList)) {
            log.info("【获取场景所有姿势展示图】未查询到任何姿势的创作记录，id={}", sceneId);
            return null;
        }

        // 按照poseShowImageMap的顺序进行结果的封装,ShowImageVO中的showImages为对应 creativeBatchVOList
        // 中的 resultImages
        // 将创作记录按元素ID分组
        Map<Integer, List<CreativeBatchVO>> creativeBatchMap = creativeBatchVOList.stream().filter(
            batch -> batch.getStringFromExtInfo(CommonConstants.CURRENT_POSE_ID) != null).collect(Collectors.groupingBy(
            batch -> Integer.valueOf(batch.getStringFromExtInfo(CommonConstants.CURRENT_POSE_ID))));

        // 按照poseShowImageMap的顺序设置showImages
        poseShowImageMap.forEach((poseId, showImageVO) -> {
            List<CreativeBatchVO> batchList = creativeBatchMap.get(poseId);
            if (CollectionUtils.isNotEmpty(batchList)) {
                // 获取所有创作记录的结果图片
                List<String> allResultImages = batchList.stream().map(CreativeBatchVO::getResultImages).filter(
                    CollectionUtils::isNotEmpty).flatMap(List::stream).collect(Collectors.toList());
                showImageVO.setShowImages(allResultImages);
            }
        });

        // 返回结果
        return poseShowImageMap;
    }

    @Override
    public List<Integer> queryExperimentalIds(List<Integer> idList) {
        return creativeElementDAO.queryExperimentalIds(idList);
    }

    @Override
    public Map<Integer, CreativeElementVO> intelligentRecommendation(IntelligentRecommendationRequest request,
                                                                     Integer maxCount) {
        // 1、获取参数
        JSONArray clothesTypeArr = request.getClothesTypeList();
        List<String> clothesTypeList = null;

        // 2、若服装类型为空则重新调用 gpt 获取
        if (CollectionUtils.isEmpty(clothesTypeArr)) {
            clothesTypeList = getClothesType(request);
        } else {
            clothesTypeList = clothesTypeArr.toJavaList(String.class);
        }

        //  3、构建请求入参
        CreativeElementQuery creativeElementQuery = buildQueryParam(clothesTypeList, maxCount, request);

        // 4、分页查询场景列表
        PageInfo<CreativeElementVO> creativeElementVOPageInfo = queryByPage(creativeElementQuery);

        // 5、提取场景ID并获取
        Map<Integer, CreativeElementVO> result = new HashMap<>();
        List<Integer> exclusiveIds = creativeElementVOPageInfo.getList().stream().map(CreativeElementVO::getId).collect(
            Collectors.toList());
        if (CollectionUtils.isNotEmpty(exclusiveIds)) {
            exclusiveIds.forEach(id -> {
                CreativeElementVO elementVO = selectPrimaryInfoByIdWithChildren(id);
                // 专属场景中 Child 不为空时
                if (!elementVO.getChildren().isEmpty()) {
                    result.put(id, elementVO);
                }
            });
        }

        return result;
    }

    @Override
    public List<String> getClothesType(IntelligentRecommendationRequest request) {
        // 1、拼装prompt提示词
        String prompt = buildGetClothesTypePrompt(request.getClothType());

        // 2、调用AI接口获取结果
        String clothesTypes = aiService.chat(prompt, request.getClothImgUrls());

        // 3、结果转换为集合
        return JSON.parseArray(clothesTypes, String.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean initStructuralScene(String imagePath, String analysisJson) {
        //参考imagePath = /mnt/vdb/dev/data_hub/cloth_data/20250816_style_images
        // /男装-美式街拍风格-适合皮夹克男款式_19725_20250522_095126/11_c990a90e7e2841519fedada9b9d09390.jpg

        // 先解析出clothDir：男装-美式街拍风格-适合皮夹克男款式_19725_20250522_095126
        String[] split = StringUtils.split(imagePath, "/");
        String clothDir = split[split.length - 2];
        String imageName = split[split.length - 1];

        Integer infoId = MaterialModelUtils.parseInfoIdFromClothDir(clothDir);
        MaterialModelVO modelVO = materialModelService.queryByInfoId(infoId);
        if (modelVO == null) {
            log.warn("【初始化结构化场景】模型不存在，id={}", infoId);
            return false;
        }
        Integer elementId = modelVO.getExtInfo(CREATIVE_ELEMENT_ID, Integer.class);
        if (elementId == null) {
            log.warn("【初始化结构化场景】模型未关联元素，id={}", infoId);
            return false;
        }

        CreativeElementVO parent = lockById(elementId);
        if (parent == null) {
            log.warn("【初始化结构化场景】模型未关联元素，id={}", elementId);
            return false;
        }

        List<CreativeElementVO> children = queryChildrenById(elementId);
        CreativeElementVO child = children.stream().filter(each -> {
            String styleImage = ElementUtils.getRootStyleImagePath(each.getExtInfo(KEY_STYLE_IMAGE, String.class));
            return StringUtils.equals(StringUtils.substringBefore(styleImage, "_"),
                StringUtils.substringBefore(imageName, "_"));
        }).findFirst().orElse(null);

        if (child == null) {
            log.warn("【初始化结构化场景】未找到对应的子元素，id={}", imageName);
            return false;
        }

        log.warn("【初始化结构化场景】找到对应的子元素，id={},parentId={}", child.getId(), child.getParentId());

        child.addExtInfo(KEY_ANALYSIS_JSON, analysisJson);
        updateById(child);

        parent.addExtInfo(CommonConstants.KEY_HAS_ANALYSIS, YES);
        updateById(parent);
        return true;
    }

    @Override
    public void assignToUser(Integer elementId, Integer userId, boolean exclusive, boolean free) {
        CreativeElementVO element = selectById(elementId);
        AssertUtil.assertNotNull(element, "元素不存在");
        AssertUtil.assertTrue(element.getLoraModelId() == null, "该元素必须为非Lora模型");

        element.setUserId(userId);
        element.setOperatorId(userId);
        element.addExtInfo(CommonConstants.KEY_OPEN_SCOPE, exclusive ? userId.toString() : CommonConstants.ALL);
        updateById(element);
    }

    // 计算特征匹配分数的辅助类
    @Data
    @AllArgsConstructor
    private static class SceneMatchScore {

        private CreativeElementVO scene;

        private double score;

    }

    private List<CreativeElementVO> queryAllByLevel(CreativeTypeEnum type, CreativeBizTypeEnum bizType,
                                                    boolean isOnlyExternal, boolean needAll) {
        return queryAllByLevel(Arrays.stream(type.getConfigKeys()).map(Enum::name).collect(Collectors.toList()),
            bizType, isOnlyExternal, needAll);
    }

    private List<CreativeElementVO> queryAllByLevel(List<String> configKeys, CreativeBizTypeEnum bizType,
                                                    boolean isOnlyExternal, boolean needAll) {
        long startTime = System.currentTimeMillis();

        List<CreativeElementVO> root = configKeys.stream().map(this::queryRootKey).filter(Objects::nonNull).collect(
            Collectors.toList());
        if (CollectionUtils.isEmpty(root)) {
            return new ArrayList<>();
        }

        CreativeElementQuery query = new CreativeElementQuery();
        query.setBizType(bizType != null ? bizType.getCode() : null);

        if (isOnlyExternal) {
            if (OperationContextHolder.isBackRole() || OperationContextHolder.isDistributorRole()) {
                query.setOpenScope("private");
            } else {
                query.setScopeUserId(OperationContextHolder.getMasterUserId());
            }
        }

        if (needAll) {
            query.setConfigKeys(configKeys);
            query.setLevels(Arrays.asList(2, 3));
        } else {
            query.setParentIds(root.stream().map(CreativeElementVO::getId).collect(Collectors.toList()));
            query.setLevels(Collections.singletonList(2));
        }

        // 过滤掉训练未完成的
        if (configKeys.contains(ElementConfigKeyEnum.SCENE.name())) {
            query.setTrainFinished(true);
        }

        List<CreativeElementVO> result = queryByLevel(query);

        root.forEach(e -> {
            e.setChildren(
                result.stream().filter(item -> item.getParentId().equals(e.getId())).collect(Collectors.toList()));
        });

        log.info("获取配置信息，加载所有元素2,rt={}ms", System.currentTimeMillis() - startTime);

        return root;
    }

    // 计算两组特征之间的匹配度
    private double calculateMatchScore(List<ClothTypeScopeItem> clothFeatures, List<ClothTypeScopeItem> sceneFeatures) {
        if (CollectionUtils.isEmpty(clothFeatures) || CollectionUtils.isEmpty(sceneFeatures)) {
            return 0;
        }

        double totalScore = 0;
        int matchedDimensions = 0;

        // 遍历服装特征的每个维度
        for (ClothTypeScopeItem clothFeature : clothFeatures) {
            // 找到场景中对应的维度
            Optional<ClothTypeScopeItem> matchingSceneFeature = sceneFeatures.stream().filter(
                sf -> StringUtils.equals(sf.getKey(), clothFeature.getKey())).findFirst();

            if (matchingSceneFeature.isPresent() && CollectionUtils.isNotEmpty(clothFeature.getValues())) {
                // 计算该维度的匹配程度
                List<String> commonValues = clothFeature.getValues().stream().filter(
                    v -> matchingSceneFeature.get().getValues().contains(v)).collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(commonValues)) {
                    // 计算维度匹配分数 = 共同值的数量 / 服装特征值的数量
                    double dimensionScore = (double)commonValues.size() / clothFeature.getValues().size();
                    totalScore += dimensionScore;
                    matchedDimensions++;
                }
            }
        }

        // 如果没有任何维度匹配，返回0
        if (matchedDimensions == 0) {
            return 0;
        }

        // 返回平均匹配分数
        return totalScore / matchedDimensions;
    }

    /**
     * 重置显示图片
     *
     * @param data 元素
     */
    private void resetShowImages(CreativeElementVO data) {
        // 目前只处理模特和场景
        if (!(StringUtils.equals(data.getConfigKey(), ElementConfigKeyEnum.FACE.name()) || StringUtils.equals(
            data.getConfigKey(), ElementConfigKeyEnum.SCENE.name()))) {
            return;
        }

        String processedUrl = ossHelper.reprocessImage(data.getShowImage());
        data.setShowImage(processedUrl);
    }

    /**
     * 更新子元素
     *
     * @param parent 父节点
     */
    private void updateChildren(CreativeElementVO parent, boolean isUpdate) {
        // 如果是场景审核不通过，不校验子元素是否存在
        if (CollectionUtils.isEmpty(parent.getChildren()) && StringUtils.equals(parent.getConfigKey(),
            ElementConfigKeyEnum.SCENE.name()) && Objects.equals(parent.getStatus(), ElementStatusEnum.REVIEW_FAILED)) {
            return;
        }
        AssertUtil.assertTrue(CollectionUtils.isNotEmpty(parent.getChildren()), ResultCode.PARAM_INVALID,
            "子元素不存在");

        // 1.先进行删除操作
        if (isUpdate) {
            CreativeElementVO origin = selectByIdWithChildren(parent.getId());

            // 删除在当前列表中没有的子元素
            List<CreativeElementVO> deleteList = origin.getChildren().stream().filter(
                item -> parent.getChildren().stream()
                    .noneMatch(child -> child.getId() != null && child.getId().equals(item.getId()))).collect(
                Collectors.toList());

            if (CollectionUtils.isNotEmpty(deleteList)) {
                deleteList.forEach(item -> {
                    int n = creativeElementDAO.deleteByPrimaryKey(item.getId());
                    AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除CreativeElement失败");
                });
            }

            // 记录变更信息
            promptModificationUtil.recordCreativeElementChanges(origin, parent);
        }

        // 2.再进行新增和更新
        int i = 0;
        for (CreativeElementVO item : parent.getChildren()) {
            i++;

            JSONObject originExtInfo = item.getExtInfo();
            if (item.getExtInfo() == null) {
                item.setExtInfo(new JSONObject());
            }
            item.getExtInfo().putAll(parent.getExtInfo());
            if (originExtInfo != null) {
                item.getExtInfo().putAll(originExtInfo);
            }
            // 对模特图片进行特殊处理
            String faceImage = parent.getExtInfo(KEY_FACE_IMAGE, String.class);
            if (StringUtils.isNotBlank(faceImage)) {
                item.addExtInfo(KEY_FACE_IMAGE, faceImage);
            }
            if (StringUtils.isBlank(item.getTags())) {
                item.setTags(parent.getTags());
            }
            if (StringUtils.isBlank(item.getExtTags())) {
                item.setExtTags(parent.getExtTags());
            }

            if (item.getExtInfo() != null) {
                item.getExtInfo().remove(KEY_PURE_RGB);
            }

            // 优化type
            ElementUtils.correctType(item);

            CreativeElementDO data = CreativeElementConverter.vo2DO(item);
            data.setOrder(i);
            data.setStatus(parent.getStatus().getCode());
            data.setModifyTime(new Date());
            data.setShowImage(parent.getShowImage());
            data.setName(parent.getName());
            data.setLevel(parent.getLevel() + 1);
            data.setBelong(parent.getBelong() == null ? ModelTypeEnum.SYSTEM.getCode() : parent.getBelong().getCode());
            data.setConfigKey(parent.getConfigKey());
            if (data.getIsNew() == null) {
                data.setIsNew(false);
            }

            if (item.getId() != null) {
                int n = creativeElementDAO.updateByPrimaryKeySelective(data);
                AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新CreativeElement失败，影响行数:" + n);
            } else {
                data.setDeleted(false);
                data.setParentId(parent.getId());
                data.setCreateTime(new Date());
                int n = creativeElementDAO.insert(data);
                AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建CreativeElement失败");
                AssertUtil.assertNotNull(data.getId(), "新建CreativeElement返回id为空");
            }
        }
    }

    private String getBodyAreaByKey(String key, List<ClothTypeScopeItem> all) {
        for (ClothTypeScopeItem item : all) {
            if (StringUtils.equals(item.getKey(), key)) {
                return item.getBodyArea();
            }
        }
        return null;
    }

    // 验证服装特征是否合法，至少要有一个维度上有值
    @SuppressWarnings("BooleanMethodIsAlwaysInverted")
    private boolean isValidFeatures(List<ClothTypeScopeItem> clothFeatures) {
        if (CollectionUtils.isNotEmpty(clothFeatures)) {
            for (ClothTypeScopeItem item : clothFeatures) {
                if (CollectionUtils.isNotEmpty(item.getValues())) {
                    return true;
                }
            }
        }

        return false;
    }

    private List<ClothTypeScopeItem> calcClothFeatures(QueryAutoGenElementRecommendRequest request) {
        List<ClothTypeScopeItem> allClothTypeScopeCfgItems = systemConfigService.queryClothTypeScopeCfg();
        AssertUtil.assertTrue(CollectionUtils.isNotEmpty(allClothTypeScopeCfgItems), ResultCode.SYS_ERROR,
            "clothTypeScopeItems配置为空");
        String prompt = buildPrompt(allClothTypeScopeCfgItems);

        List<String> imgUrls = request.getClothImgUrls().stream().map(url -> ossHelper.reprocessImage(url)).collect(
            Collectors.toList());

        String res = invokeVlmOpenAI(prompt, imgUrls);
        if (JSONArray.isValidArray(res)) {
            List<ClothTypeScopeItem> list = parseResponse(res, allClothTypeScopeCfgItems);

            if (CollectionUtils.isEmpty(list)) {
                return list;
            }

            // 匹配上装特征和下装特征，丢弃不要的特征
            for (ClothTypeScopeItem item : list) {
                if (StringUtils.equals(request.getClothType(), ClothTypeEnum.Tops.getCode()) && StringUtils.equals(
                    "lower", item.getBodyArea())) {
                    item.setValues(new ArrayList<>());
                } else if (StringUtils.equals(request.getClothType(), ClothTypeEnum.Bottoms.getCode())
                           && StringUtils.equals("upper", item.getBodyArea())) {
                    item.setValues(new ArrayList<>());
                }

                // 如果当前衣服为外套，则需要指定需要内搭（用于优先匹配有内搭的外套）
                if (StringUtils.equals("是否外套", item.getKey()) && CollectionUtils.isNotEmpty(item.getValues())
                    && StringUtils.equals("是", item.getValues().get(0))) {
                    for (ClothTypeScopeItem item2 : list) {
                        if (StringUtils.equals("是否是带有内搭的外套", item2.getKey())) {
                            item2.setValues(Collections.singletonList("是"));
                        }
                    }
                }
            }

            return list;

        } else {
            log.error("自动计算服装类型范围失败，prompt={}, imgUrls={}, res={}", prompt, request.getClothImgUrls(), res);
            return null;
        }
    }

    private String invokeVlmOpenAI(String prompt, List<String> imgUrls) {
        GptResponse res = openAIService.requestGpt(prompt, imgUrls);
        if (res.getStatus() == Status.OK && JSONArray.isValidArray(res.getText())) {
            return CommonUtil.parseJsonStringFromGpt(res.getText());
        } else {
            return null;
        }
    }

    private List<ClothTypeScopeItem> parseResponse(String text, List<ClothTypeScopeItem> all) {
        if (CommonUtil.isValidJsonArray(text)) {
            List<String> allKeys = all.stream().map(ClothTypeScopeItem::getKey).toList();

            List<ClothTypeScopeItem> ret = JSONObject.parseArray(text, ClothTypeScopeItem.class);
            for (ClothTypeScopeItem item : ret) {

                // key不对，整个丢弃
                if (!allKeys.contains(item.getKey())) {
                    continue;
                }

                // value不对，丢弃这个value
                if (CollectionUtils.isNotEmpty(item.getValues())) {
                    List<String> values = item.getValues().stream().filter(v -> all.stream()
                            .anyMatch(i -> StringUtils.equals(i.getKey(), item.getKey()) && i.getValues().contains(v)))
                        .collect(Collectors.toList());
                    item.setValues(values);
                }

                item.setBodyArea(getBodyAreaByKey(item.getKey(), all));
            }

            return ret;
        }

        return null;
    }

    private List<SceneMatchScore> getSceneMatchScores(List<CreativeElementVO> styleScenes,
                                                      List<ClothTypeScopeItem> clothFeatures) {
        List<SceneMatchScore> matchScores = new ArrayList<>();
        for (CreativeElementVO scene : styleScenes) {
            // 获取场景的服装类型范围
            List<ClothTypeScopeItem> sceneFeatures = JSONArray.parseArray(
                scene.getExtInfo(CommonConstants.KEY_SCENE_CLOTH_TYPE_SCOPE, String.class), ClothTypeScopeItem.class);
            if (!isValidFeatures(sceneFeatures)) {
                log.warn("当前场景特征无效，忽略.elementId:{},features:{}", scene.getId(), sceneFeatures);
                continue;
            }

            // 计算匹配分数
            double score = calculateMatchScore(clothFeatures, sceneFeatures);
            if (score > 0) {
                matchScores.add(new SceneMatchScore(scene, score));
            }
        }
        return matchScores;
    }

    private List<CreativeElementVO> getStyleScenes(QueryAutoGenElementRecommendRequest request) {
        CreativeElementQuery query = new CreativeElementQuery();
        query.setConfigKey(ElementConfigKeyEnum.SCENE.name());
        query.setHasClothTypeScope(true);
        query.setStyleScene(true);
        query.setExcludesParentOrIds(request.getExcludeElementIds());

        // 规则1.clothStyleType=male时，匹配男装场景 type包含Male
        // 规则2.clothStyleType=female时，匹配女装场景 type包含Female
        if (StringUtils.equals(CommonConstants.male, request.getClothStyleType()) || StringUtils.equals(
            CommonConstants.man, request.getClothStyleType())) {
            query.setGenderType("Male");
        } else if (StringUtils.equals(CommonConstants.female, request.getClothStyleType()) || StringUtils.equals(
            CommonConstants.woman, request.getClothStyleType())) {
            query.setGenderType("Female");
        }

        if (StringUtils.isNotBlank(request.getAgeRange())) {
            query.setAgeRange(request.getAgeRange());
        }

        // 获取所有level=2的style scene元素
        return queryByLevel(query);
    }

    /**
     * 克隆元素并保存
     *
     * @param origin   原始元素
     * @param parentId 父节点id
     * @param fullCopy 完整拷贝
     * @param userId   归属用户id
     * @return 新元素
     */
    private CreativeElementVO cloneAndStore(CreativeElementVO origin, Integer parentId, boolean fullCopy,
                                            Integer userId) {
        CreativeElementVO result = CommonUtil.deepCopy(origin);
        result.setId(null);
        result.setName(getCloneElementName(origin));
        if (userId != null) {
            result.addExtInfo(KEY_OPEN_SCOPE, userId);
        } else {
            result.setStatus(ElementStatusEnum.TEST);
            result.setShowImage(DEFAULT_IMAGE);
        }
        result.setCreateTime(new Date());
        result.setParentId(parentId);

        userId = userId != null ? userId : OperationContextHolder.getMasterUserId();
        result.setUserId(userId);
        result.setOperatorId(OperationContextHolder.getOperatorUserId());

        // 插入数据
        CreativeElementVO inserted = copyInsert(result);

        // 判断是否关联了MaterialModel，如果有关联则克隆MaterialModel；只克隆父结点的模型，子模型没有模型，不处理。
        if (origin.getLevel() == 2 && origin.getLoraModelId() != null) {
            JSONObject extInfo = new JSONObject();
            extInfo.put(CREATIVE_ELEMENT_ID, inserted.getId());
            MaterialModelVO model = materialModelService.selectById(origin.getLoraModelId());
            if (model != null) {
                MaterialModelVO cloneModel = materialModelService.cloneLora(model.getId(), null, extInfo, fullCopy,
                    userId);
                AssertUtil.assertNotNull(cloneModel, ResultCode.BIZ_FAIL, "原始服装关联了模型，但克隆模型时失败");

                inserted.setLoraModelId(cloneModel.getId());

                updateById(inserted);
            }
        }

        return inserted;
    }

    private String getCloneElementName(CreativeElementVO origin) {
        String name = origin.getName() + "_copy";

        CreativeElementExample exam = new CreativeElementExample();
        CreativeElementExample.Criteria c = exam.createCriteria();
        c.andNameLike(name);
        c.andLevelEqualTo(2);
        exam.page(1, 100);

        long existedNum = creativeElementDAO.countByExample(exam);

        if (existedNum > 0) {
            name = name + "_" + existedNum;
        }

        return name;
    }

    private static List<String> getSampleImgs(CreativeElementVO sceneElement) {
        if (CollectionUtils.isEmpty(sceneElement.getChildren())) {
            log.info("样本的children不存在，未找到场景元素明细，当前元素未完成训练，先忽略");
            return new ArrayList<>();
        }

        List<CreativeElementVO> samples = sceneElement.getChildren().stream().filter(ElementUtils::isStyleScene)
            .collect(Collectors.toList());
        AssertUtil.assertTrue(CollectionUtils.isNotEmpty(samples), ResultCode.PARAM_INVALID,
            "未找到风格场景子元素风格样本");

        // 从children里挑选前至多5个样本的图片url
        List<String> imgUrls = samples.subList(0, Math.min(5, samples.size())).stream().map(
            CreativeElementVO::getShowImage).collect(Collectors.toList());
        AssertUtil.assertNotEmpty(imgUrls, ResultCode.PARAM_INVALID, "未找到风格场景子元素风格样本图片");
        return imgUrls;
    }

    private static String buildPrompt(List<ClothTypeScopeItem> clothTypeScopeItems) {
        JSONArray arr = new JSONArray();
        for (ClothTypeScopeItem item : clothTypeScopeItems) {
            JSONObject obj = new JSONObject();
            obj.put("维度", item.getKey());
            obj.put("选项", item.getValues());
            arr.add(obj);
        }

        return String.format(
            "你是一位专业的服装搭配分析师，请根据用户提供的服装展示图片，严格按照以下要求进行分析：\n" + "\n" + "【分析要求】\n"
            + "1. 分别从服装的上装和下装两个维度进行特征识别\n" + "2. 必须且只能从预定义的维度标签中选择匹配项：\n"
            + "%s\n" + "3. 若某维度不适用（如没有下装），保持该维度键值但留空数组\n" + "4. 确保数值与图片展示的实际服装特征完全匹配\n"
            + "5. 确保布尔判断是与否的情况，需要返回且只返回一个数值，不可返回空数组\n" + "\n" + "【输出格式】\n"
            + "必须返回严格符合此结构的JSON数组（不需要Markdown格式）：\n" + "[\n" + "  {\n" + "    \"key\": \"维度名称\",\n"
            + "    \"values\": [\"匹配的选项\"]\n" + "  },\n" + "  ...\n" + "]",
            CommonUtil.formatJsonOneRow(arr.toJSONString()));
    }

    /**
     * 根据姿势ID列表批量查询每个ID的最新创作记录
     *
     * @param poseIdList 姿势ID列表
     * @return Map<poseId, 最新的创作记录>
     */
    private Map<Integer, CreativeBatchVO> getLatestCreativeBatchByPoseIds(List<Integer> poseIdList) {
        if (CollectionUtils.isEmpty(poseIdList)) {
            return new HashMap<>();
        }

        // 调用DAO层的批量查询方法，直接在数据库层面实现窗口函数查询
        List<CreativeBatchVO> latestBatches = creativeBatchService.getLatestCreativeBatchByPoseIds(poseIdList);

        if (CollectionUtils.isEmpty(latestBatches)) {
            return new HashMap<>();
        }

        // 转换为Map格式，key为poseId
        Map<Integer, CreativeBatchVO> result = new HashMap<>();
        for (CreativeBatchVO batch : latestBatches) {
            if (batch.getExtInfo() != null) {
                String currentPoseId = batch.getStringFromExtInfo("currentPoseId");
                if (currentPoseId != null) {
                    result.put(Integer.valueOf(currentPoseId), batch);
                }
            }
        }
        return result;
    }

    /**
     * 批量更新创作元素
     *
     * @param elements 需要更新的元素列表
     */
    private void batchUpdateCreateElement(List<CreativeElementVO> elements) {
        if (CollectionUtils.isEmpty(elements)) {
            log.debug("批量更新元素列表为空，跳过更新");
            return;
        }

        // 过滤出需要更新的元素（有ID且扩展信息发生变化）
        List<CreativeElementVO> validElements = elements.stream().filter(element -> element.getId() != null).filter(
            element -> element.getExtInfo() != null && !element.getExtInfo().isEmpty()).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(validElements)) {
            log.debug("没有有效的元素需要更新");
            return;
        }

        log.info("开始批量更新创作元素，待更新数量: {}", validElements.size());

        try {
            Date currentTime = new Date();
            int successCount = 0;
            int failCount = 0;

            for (CreativeElementVO element : validElements) {
                try {
                    // 设置修改时间
                    element.setModifyTime(currentTime);
                    // 调用更新方法
                    self.updateById(element);
                    // 累加
                    successCount++;

                    log.debug("更新元素成功: ID={}, ExtInfo={}", element.getId(), element.getExtInfo());
                } catch (Exception e) {
                    failCount++;
                    log.warn("更新元素失败: ID={}, 错误: {}", element.getId(), e.getMessage());
                }
            }

            log.info("批量更新创作元素完成 - 成功: {}, 失败: {}, 总计: {}", successCount, failCount,
                validElements.size());
        } catch (Exception e) {
            log.error("批量更新创作元素过程中发生异常", e);
            throw new BizException(ResultCode.BIZ_FAIL, "批量更新创作元素失败: " + e.getMessage());
        }
    }

    /**
     * 获取渠道商客户列表
     *
     * @return 客户列表
     */
    private List<DistributorCustomerVO> getDistributorCustomers() {
        if (!OperationContextHolder.isDistributorRole()) {
            return null;
        }
        Integer currentUserId = OperationContextHolder.getMasterUserId();
        List<DistributorCustomerVO> customers = distributorCustomerCache.getIfPresent(currentUserId);
        if (customers != null) {
            return customers;
        }

        customers = distributorCustomerService.queryDistributorCustomersByCurrentUserAuth(false);
        distributorCustomerCache.put(currentUserId, customers);
        return customers;
    }

    /**
     * 构建获取服装类型的prompt
     *
     * @param clothType 服装类型
     * @return prompt
     */
    private String buildGetClothesTypePrompt(String clothType) {

        // 查询服装类型列表
        List<ClothCategoryVO> clothCategoryList = JSONObject.parseArray(
            systemConfigService.queryValueByKey(SystemConstants.CLOTH_CATEGORY_CFG), ClothCategoryVO.class);
        // 查询提示词
        SystemConfigVO promptConfig = systemConfigService.queryByKey(SystemConstants.CLOTH_TYPE_CATEGORY_PROMPT);

        // 1、拼装服装列表至提示词中
        String prompt = Optional.of(promptConfig).map(SystemConfigVO::getConfValue).filter(StringUtils::isNotBlank).map(
            s -> this.replacePrompt(s, clothCategoryList)).orElse(null);

        if (prompt == null) {
            return null;
        }

        // 2、拼装服装类型至提示词中
        return prompt.replace("{{clothType}}", clothType);
    }

    /**
     * 替换prompt中的{{categoryList}}
     *
     * @param prompt            提示词
     * @param clothCategoryList 服装分类列表
     * @return 最终提示词
     */
    private String replacePrompt(String prompt, List<ClothCategoryVO> clothCategoryList) {
        List<String> categoryList = clothCategoryList.stream().map(ClothCategoryVO::getChildren).flatMap(List::stream)
            .map(SubcategoryVO::getSubcategory).collect(Collectors.toList());

        return prompt.replace("{{categoryList}}", JSONObject.toJSONString(categoryList));
    }

    /**
     * 构建查询参数
     *
     * @param clothesTypeList 负责类型列表
     * @param maxCount        最大数量
     * @param request         请求入参
     * @return 查询参数
     */
    private CreativeElementQuery buildQueryParam(List<String> clothesTypeList, Integer maxCount,
                                                 IntelligentRecommendationRequest request) {
        String genderType = request.getGenderType();
        String ageRange = request.getAgeRange();

        // 初始化参数
        CreativeElementQuery query = new CreativeElementQuery();

        // 只查询 2 级
        query.setLevels(Collections.singletonList(2));

        // 只查询 lora 场景
        query.setOnlyLora(Boolean.TRUE);

        // 查询负责类型
        query.setClothCategory(clothesTypeList);

        // 只查询非实验数据
        query.setOnlyExperimental(Boolean.FALSE);

        // 查询子记录大于 0
        query.setHasChildren(Boolean.TRUE);

        // 设置正式环境
        query.setStatus(ElementStatusEnum.PROD.getCode());

        // 推荐逻辑不查询专属场景
        query.setIsExclusive(Boolean.FALSE);

        // 设置年龄范围
        query.setAgeRange(ageRange);

        // 设置服装类型
        query.setType(genderType);

        // 设置查询数量
        query.setPageNum(1);
        query.setPageSize(maxCount);

        // 返回query
        return query;
    }

    /**
     * 根据ID查询子记录
     *
     * @param id ID
     * @return 子记录
     */
    private List<CreativeElementVO> queryChildrenById(Integer id) {
        CreativeElementExample example = new CreativeElementExample();
        example.createCriteria().andParentIdEqualTo(id);

        List<CreativeElementDO> dos = creativeElementDAO.selectByExample(example);
        return CreativeElementConverter.doList2VOList(dos);
    }

}