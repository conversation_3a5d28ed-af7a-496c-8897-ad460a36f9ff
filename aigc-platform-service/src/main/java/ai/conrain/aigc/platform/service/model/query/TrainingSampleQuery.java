package ai.conrain.aigc.platform.service.model.query;

import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * TrainingSampleQuery
 *
 * @version TrainingSampleService.java
 */
@Data
public class TrainingSampleQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

        /** 主键，自增 */
        private Integer id;

        /** 样本数据ID */
        private Integer sampleDataId;

        /** 样本数据ID类型 */
        private String sampleDataIdType;

        /** 样本标注ID */
        private Integer sampleCaptionId;

        /** 样本标注ID类型 */
        private String sampleCaptionIdType;

        /** 关联训练ID */
        private Integer relatedTrainingId;

        /** 关联训练ID类型 */
        private String relatedTrainingIdType;

        /** 扩展信息字段 */
        private String extInfo;

        /** 记录创建时间 */
        private Date createTime;

        /** 记录最后修改时间 */
        private Date modifyTime;

        /** 翻页参数：页面大小 */
        private Integer pageSize;

        /** 翻页参数：页数（从1开始） */
        private Integer pageNum;

        /** 排序指令（示例："id asc"） */
        private String orderBy;

}