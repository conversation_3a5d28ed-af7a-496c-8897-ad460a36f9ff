package ai.conrain.aigc.platform.service.model.biz.agent;

import lombok.Data;

import java.io.Serializable;

/**
 * 背景聚类配置选项
 */
@Data
public class ClusterOptions implements Serializable {

    // 每个流派保留的背景簇数量，用于展示的背景簇数量
    private Integer bgGroupCountLimit = 15;

    // 每个背景簇最多保留的图片数量，为同背景聚类的套图功能设计，套图直接露出数量是bgSzShow，整个背景簇数量是bgSzCountLimit
    private Integer bgSzCountLimit = 30;

    // 每个背景簇的露出展示的图片数量
    private Integer bgSzShow = 2;

    //背景聚类时，如果聚类大小小于该值，则删除这个聚类结果
    private Integer minBgClusterSz = 1;

    // 禁用模型排序（用于测试）
    private boolean sortModelDisabled = false;
}