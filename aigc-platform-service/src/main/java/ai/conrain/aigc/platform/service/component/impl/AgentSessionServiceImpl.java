package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.dao.AgentSessionDAO;
import ai.conrain.aigc.platform.dal.dao.AgentSessionTaskDAO;
import ai.conrain.aigc.platform.dal.entity.AgentSessionDO;
import ai.conrain.aigc.platform.dal.entity.AgentSessionTaskDO;
import ai.conrain.aigc.platform.dal.example.AgentSessionExample;
import ai.conrain.aigc.platform.dal.example.AgentSessionTaskExample;
import ai.conrain.aigc.platform.dal.query.AgentSessionQueryDO;
import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.service.component.AgentSessionService;
import ai.conrain.aigc.platform.service.component.AgentSessionTaskService;
import ai.conrain.aigc.platform.service.component.agent.AgentTaskHandler;
import ai.conrain.aigc.platform.service.component.agent.factory.AgentTaskHandlerFactory;
import ai.conrain.aigc.platform.service.enums.agent.AgentSessionStatusEnum;
import ai.conrain.aigc.platform.service.helper.WeakLockHelper;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.AgentSessionConverter;
import ai.conrain.aigc.platform.service.model.converter.AgentSessionTaskConverter;
import ai.conrain.aigc.platform.service.model.query.AgentSessionQuery;
import ai.conrain.aigc.platform.service.model.vo.AgentSessionTaskVO;
import ai.conrain.aigc.platform.service.model.vo.AgentSessionVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * AgentSessionService实现
 *
 * <AUTHOR>
 * @version AgentSessionService.java
 */
@Slf4j
@Service
public class AgentSessionServiceImpl implements AgentSessionService {
    private static final String LOCK_KEY_PREFIX = "_sync_agent_lock_";
    private static final int LOCK_EXPIRE_TIME = 5 * 60 * 1000;

    @Autowired
    private WeakLockHelper weakLockHelper;
    @Autowired
    private TairService tairService;
    @Autowired
    private AgentSessionTaskService agentSessionTaskService;
    @Autowired
    private AgentSessionTaskDAO agentSessionTaskDAO;
    @Autowired
    private AgentTaskHandlerFactory agentTaskHandlerFactory;

    /**
     * DAO
     */
    @Autowired
    private AgentSessionDAO agentSessionDAO;

    @Override
    public AgentSessionVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        AgentSessionDO data = agentSessionDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
        if (null == data) {
            return null;
        }

        return AgentSessionConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = agentSessionDAO.logicalDeleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除AgentSession失败");
    }

    @Override
    public AgentSessionVO insert(AgentSessionVO agentSession) {
        AssertUtil.assertNotNull(agentSession, ResultCode.PARAM_INVALID, "agentSession is null");
        AssertUtil.assertTrue(agentSession.getId() == null, ResultCode.PARAM_INVALID, "agentSession.id is present");

        //创建时间、修改时间兜底
        if (agentSession.getCreateTime() == null) {
            agentSession.setCreateTime(new Date());
        }

        if (agentSession.getModifyTime() == null) {
            agentSession.setModifyTime(new Date());
        }

        // 默认状态设置为ACTIVE
        agentSession.setStatus(AgentSessionStatusEnum.ACTIVE);

        AgentSessionDO data = AgentSessionConverter.vo2DO(agentSession);
        //逻辑删除字段初始化（虽然一般表字段有默认值）
        data.setDeleted(false);
        Integer n = agentSessionDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建AgentSession失败");
        AssertUtil.assertNotNull(data.getId(), "新建AgentSession返回id为空");
        agentSession.setId(data.getId());
        return agentSession;
    }


    @Override
    public void updateByIdSelective(AgentSessionVO agentSession) {
        AssertUtil.assertNotNull(agentSession, ResultCode.PARAM_INVALID, "agentSession is null");
        AssertUtil.assertTrue(agentSession.getId() != null, ResultCode.PARAM_INVALID, "agentSession.id is null");

        //修改时间必须更新
        agentSession.setModifyTime(new Date());
        AgentSessionDO data = AgentSessionConverter.vo2DO(agentSession);
        //逻辑删除标过滤
        data.setDeleted(false);
        int n = agentSessionDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新AgentSession失败，影响行数:" + n);
    }

    @Override
    public List<AgentSessionVO> queryAgentSessionList(AgentSessionQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AgentSessionQueryDO queryDO = new AgentSessionQueryDO();
        BeanUtils.copyProperties(query, queryDO);

        List<AgentSessionDO> list = agentSessionDAO.queryAgentSessionList(queryDO);
        return AgentSessionConverter.doList2VOList(list);
    }

    @Override
    public Long queryAgentSessionCount(AgentSessionQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AgentSessionExample example = AgentSessionConverter.query2Example(query);
        return agentSessionDAO.countByExample(example);
    }

    /**
     * 带条件分页查询
     */
    @Override
    public PageInfo<AgentSessionVO> queryAgentSessionByPage(AgentSessionQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                && query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
                + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<AgentSessionVO> page = new PageInfo<>();

        AgentSessionExample example = AgentSessionConverter.query2Example(query);
        long totalCount = agentSessionDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<AgentSessionDO> list = agentSessionDAO.selectByExample(example);
        page.setList(AgentSessionConverter.doList2VOList(list));
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Override
    public void syncStatus(AgentSessionVO agentSessionVO) {
        log.info("[syncStatus]异步执行状态同步开始,agentSessionId={}, agentSessionType={}", agentSessionVO.getId(), agentSessionVO.getSessionType());
        if (!weakLockHelper.lock(WeakLockHelper.WeakType.BATCH, agentSessionVO.getId())) {
            // 3秒内已执行过同步，直接返回，减少数据库压力
            log.info("3秒内任务已经进行过同步，直接返回当前数据，不需要同步状态,id={},uid={}", agentSessionVO.getId(),
                    agentSessionVO.getOperatorId());
            return;
        }

        long t1 = System.currentTimeMillis();

        String lockKey = LOCK_KEY_PREFIX + agentSessionVO.getId();
        boolean lock = tairService.acquireLock(lockKey, LOCK_EXPIRE_TIME);

        if (!lock) {
            log.info("【agentSession】任务正在处理中，直接返回当前数据，不需要同步状态,id={},uid={}", agentSessionVO.getId(), agentSessionVO.getOperatorId());
            return;
        }

        // 分布式锁之后，再查一遍数据库
        AgentSessionVO target = selectById(agentSessionVO.getId());
        try {
            // 分布式锁之后，再查一遍数据库
            AssertUtil.assertNotNull(target, ResultCode.PARAM_INVALID, "data is null");
            if (target.getStatus().isEnd()) {
                log.info("【agentSession】任务已经结束，无需同步,id={},uid={}", target.getId(), target.getOperatorId());
                return;
            }

            // 查询第一个未完成状态的子任务
            AgentSessionTaskVO firstUnfinishedTask = queryFirstUnfinishedTaskBySessionId(agentSessionVO.getId());
            if (Objects.isNull(firstUnfinishedTask)){
                log.info("【agentSession】没有需要调度的任务，无需同步,id={},uid={}", target.getId(), target.getOperatorId());
                return;
            }

            // 调度未完成任务
            String taskType = firstUnfinishedTask.getTaskType();
            AssertUtil.assertTrue(!StringUtils.isBlank(taskType), ResultCode.BIZ_FAIL, "任务类型为空，直接跳出...");

            // 工厂类进行任务分发，分发至对应的实现类
            AgentTaskHandler<?> taskHandler = agentTaskHandlerFactory.getTaskHandler(taskType);
            if (taskHandler != null) {
                taskHandler.handleTask(firstUnfinishedTask);
            } else {
                log.warn("未找到对应的任务处理器, taskType={}", taskType);
            }


        } catch (Exception e) {
            log.error("syncStatus异常,batchId=" + agentSessionVO.getId(), e);
        } finally {
            // 释放锁
            tairService.releaseLock(lockKey);

            long t2 = System.currentTimeMillis();
            log.info("syncStatus耗时：{} ms, id:{}", t2 - t1, agentSessionVO.getId());
        }

    }

    /**
     * 查询会话中第一个未完成的任务
     *
     * @param sessionId 会话ID
     * @return 第一个未完成的任务
     */
    private AgentSessionTaskVO queryFirstUnfinishedTaskBySessionId(Integer sessionId) {
        // 创建查询条件
        AgentSessionTaskExample example = new AgentSessionTaskExample();
        // 添加查询条件：会话ID、未删除、未完成状态
        example.createCriteria()
                .andSessionIdEqualTo(sessionId)
                .andDeletedByRollbackEqualTo(0)
                .andStatusNotIn(Arrays.asList(
                        AgentSessionStatusEnum.FINISHED.getCode(),
                        AgentSessionStatusEnum.FAILED.getCode(),
                        AgentSessionStatusEnum.CANCELED.getCode()
                ));
        // 按排序号升序排列
        example.setOrderByClause("order_num asc");
        // 限制只返回第一条记录
        example.setRows(1);

        List<AgentSessionTaskDO> taskList = agentSessionTaskDAO.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isNotEmpty(taskList)) {
            return AgentSessionTaskConverter.do2VO(taskList.getFirst());
        }
        return null;
    }

    @Override
    public List<AgentSessionVO> queryActiveSessionsWithUnfinishedTasks() {
        List<AgentSessionDO> list = agentSessionDAO.queryActiveSessionsWithUnfinishedTasks();
        return AgentSessionConverter.doList2VOList(list);
    }
}