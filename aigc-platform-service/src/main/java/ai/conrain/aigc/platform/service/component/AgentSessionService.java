package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.AgentSessionQuery;
import ai.conrain.aigc.platform.service.model.vo.AgentSessionVO;

import java.util.List;

/**
 *  Service定义
 *
 * <AUTHOR>
 * @version AgentSessionService.java
 */
public interface AgentSessionService {
	
	/**
	 * 查询对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	AgentSessionVO selectById(Integer id);

	/**
	 * 删除对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加对象
	 * @param agentSession 对象参数
	 * @return 返回结果
	 */
	AgentSessionVO insert(AgentSessionVO agentSession);

	/**
	 * 修改对象
	 * @param agentSession 对象参数
	 */
	void updateByIdSelective(AgentSessionVO agentSession);

	/**
	 * 带条件批量查询列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<AgentSessionVO> queryAgentSessionList(AgentSessionQuery query);

	/**
	 * 带条件查询数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryAgentSessionCount(AgentSessionQuery query);

	/**
	 * 带条件分页查询
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<AgentSessionVO> queryAgentSessionByPage(AgentSessionQuery query);

	/**
	 * 同步会话状态
	 * @param agentSessionVO 会话VO
	 */
	void syncStatus(AgentSessionVO agentSessionVO);

	/**
	 * 查询有未完成子任务的激活状态会话
	 * @return 会话列表
	 */
	List<AgentSessionVO> queryActiveSessionsWithUnfinishedTasks();
}