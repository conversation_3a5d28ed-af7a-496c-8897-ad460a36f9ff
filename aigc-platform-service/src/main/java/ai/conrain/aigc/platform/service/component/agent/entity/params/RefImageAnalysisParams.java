package ai.conrain.aigc.platform.service.component.agent.entity.params;

import lombok.Data;

import java.util.List;

/**
 * 参考图分析任务参数
 */
@Data
public class RefImageAnalysisParams {
    
    /**
     * 参考图片URL列表
     */
    private List<String> referenceImageList;
    
    /**
     * 单个参考图片URL（兼容旧版本）
     */
    private String refImageUrl;
    
    /**
     * 场景类型
     */
    private String sceneType;
    
    /**
     * 风格描述
     */
    private String styleDescription;
    
    /**
     * 是否需要详细分析
     */
    private Boolean needDetailAnalysis;
    
    /**
     * 会话ID
     */
    private Integer sessionId;
}