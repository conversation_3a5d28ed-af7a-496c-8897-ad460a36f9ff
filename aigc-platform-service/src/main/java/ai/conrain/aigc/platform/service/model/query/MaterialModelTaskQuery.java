package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * MaterialModelTaskQuery
 *
 * @version MaterialModelTaskService.java v 0.1 2024-05-27 08:44:21
 */
@Data
public class MaterialModelTaskQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** 模型训练任务id */
    private Integer id;

    /** 素材id */
    private Integer materialId;

    /** 素材名称 */
    private String materialName;

    /** 任务类型, lora训练 */
    private String taskType;

    /** 任务状态 */
    private String taskStatus;

    private List<String> statusList;

    /** 结果模型id */
    private Integer materialModelId;

    /** 归属主账号id */
    private Integer userId;

    /** 操作人账号id */
    private Integer operatorId;

    /** 自定义请求参数 */
    private String requestParams;

    /** ComfyUI返回的唯一标识 */
    private String promptId;

    /** 扩展 */
    private String extInfo;


    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** aigc请求报文 */
    private String aigcRequest;

    /** 结果详情 */
    private String retDetail;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

}
