package ai.conrain.aigc.platform.service.component.agent;

import ai.conrain.aigc.platform.service.component.agent.entity.params.RecommendedStyleSceneParams;
import ai.conrain.aigc.platform.service.enums.ClothShootGenreEnum;
import ai.conrain.aigc.platform.service.enums.agent.AgentSessionTaskTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.agent.StyleImageSearchRequest;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.vo.AgentSessionTaskVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 根据流派推荐风格场景任务处理器
 */
@Slf4j
@Component
public class RecommendedMoreByStyleTaskHandler extends AbstractStyleSceneTaskHandler<RecommendedStyleSceneParams> {

    @Override
    public String getSupportedTaskType() {
        return AgentSessionTaskTypeEnum.RECOMMENDED_MORE_BY_STYLE.getCode();
    }

    @Override
    public Class<RecommendedStyleSceneParams> getParameterType() {
        return RecommendedStyleSceneParams.class;
    }


    @Override
    protected StyleImageSearchRequest buildSearchRequest(AgentSessionTaskVO agentSessionTask, String clothesTaskId, String referenceTaskId, RecommendedStyleSceneParams params) {
        // 调用父类方法构建基础请求
        StyleImageSearchRequest request = buildBaseSearchRequest(agentSessionTask, clothesTaskId, referenceTaskId);

        // 设置特定的搜索配置
        configureSearchOptions(request, agentSessionTask, params);


        if (Objects.isNull(params.getSpecifiedSearchId()) ||
                StringUtils.isBlank(params.getSpecifiedGenre())) {
            throw new BizException("参数不完整...");
        }
        request.setSpecifiedSearchId(params.getSpecifiedSearchId());

        ClothShootGenreEnum clothShootGenreEnum = ClothShootGenreEnum.getByCode(params.getSpecifiedGenre());
        if (Objects.isNull(clothShootGenreEnum)) {
            throw new BizException("参数不完整...");
        }
        request.setSpecifiedGenre(clothShootGenreEnum);


        return request;
    }

}