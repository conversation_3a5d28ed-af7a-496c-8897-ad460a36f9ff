package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.AgentSessionTaskDO;
import ai.conrain.aigc.platform.dal.example.AgentSessionTaskExample;
import ai.conrain.aigc.platform.service.enums.agent.AgentSessionStatusEnum;
import ai.conrain.aigc.platform.service.model.query.AgentSessionTaskQuery;
import ai.conrain.aigc.platform.service.model.vo.AgentSessionTaskVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Objects;

/**
 * AgentSessionTaskConverter
 *
 * @version AgentSessionTaskService.java
 */
public class AgentSessionTaskConverter {

    /**
     * DO -> VO
     */
    public static AgentSessionTaskVO do2VO(AgentSessionTaskDO from) {
        AgentSessionTaskVO to = new AgentSessionTaskVO();
        to.setId(from.getId());
        to.setSessionId(from.getSessionId());
        to.setBatchId(from.getBatchId());
        to.setPreTaskId(from.getPreTaskId());
        to.setPreTaskIds(from.getPreTaskIds());
        to.setUserId(from.getUserId());
        to.setTaskType(from.getTaskType());
        to.setMessageFrom(from.getMessageFrom());
        to.setStatus(AgentSessionStatusEnum.getByCode(from.getStatus()));
        to.setOrderNum(from.getOrderNum());
        to.setDeletedByRollback(from.getDeletedByRollback());
        to.setRollbackToTaskId(from.getRollbackToTaskId());
        to.setFinishTime(from.getFinishTime());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setResultInfo(CommonUtil.parseObject(from.getResultInfo()));
        to.setExtInfo(CommonUtil.parseObject(from.getExtInfo()));

        return to;
    }

    /**
     * VO -> DO
     */
    public static AgentSessionTaskDO vo2DO(AgentSessionTaskVO from) {
        AgentSessionTaskDO to = new AgentSessionTaskDO();
        to.setId(from.getId());
        to.setSessionId(from.getSessionId());
        to.setBatchId(from.getBatchId());
        to.setPreTaskId(from.getPreTaskId());
        to.setPreTaskIds(from.getPreTaskIds());
        to.setUserId(from.getUserId());
        to.setTaskType(from.getTaskType());
        to.setMessageFrom(from.getMessageFrom());
        if (Objects.nonNull(from.getStatus())){
            to.setStatus(from.getStatus().getCode());
        }
        to.setOrderNum(from.getOrderNum());
        to.setDeletedByRollback(from.getDeletedByRollback());
        to.setRollbackToTaskId(from.getRollbackToTaskId());
        to.setFinishTime(from.getFinishTime());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setResultInfo(CommonUtil.toJSONString(from.getResultInfo()));
        to.setExtInfo(CommonUtil.toJSONString(from.getExtInfo()));

        return to;
    }

    /**
     * Query -> Example
     */
    public static AgentSessionTaskExample query2Example(AgentSessionTaskQuery from) {
        AgentSessionTaskExample to = new AgentSessionTaskExample();
        AgentSessionTaskExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getSessionId())) {
            c.andSessionIdEqualTo(from.getSessionId());
        }
        if (!ObjectUtils.isEmpty(from.getBatchId())) {
            c.andBatchIdEqualTo(from.getBatchId());
        }
        if (!ObjectUtils.isEmpty(from.getPreTaskId())) {
            c.andPreTaskIdEqualTo(from.getPreTaskId());
        }
        if (!ObjectUtils.isEmpty(from.getPreTaskIds())) {
            c.andPreTaskIdsEqualTo(from.getPreTaskIds());
        }
        if (!ObjectUtils.isEmpty(from.getUserId())) {
            c.andUserIdEqualTo(from.getUserId());
        }
        if (!ObjectUtils.isEmpty(from.getTaskType())) {
            c.andTaskTypeEqualTo(from.getTaskType());
        }
        if (!ObjectUtils.isEmpty(from.getMessageFrom())) {
            c.andMessageFromEqualTo(from.getMessageFrom());
        }
        if (!ObjectUtils.isEmpty(from.getStatus())) {
            c.andStatusEqualTo(from.getStatus());
        }
        if (!ObjectUtils.isEmpty(from.getOrderNum())) {
            c.andOrderNumEqualTo(from.getOrderNum());
        }
        if (!ObjectUtils.isEmpty(from.getDeletedByRollback())) {
            c.andDeletedByRollbackEqualTo(from.getDeletedByRollback());
        }
        if (!ObjectUtils.isEmpty(from.getRollbackToTaskId())) {
            c.andRollbackToTaskIdEqualTo(from.getRollbackToTaskId());
        }
        if (!ObjectUtils.isEmpty(from.getFinishTime())) {
            c.andFinishTimeEqualTo(from.getFinishTime());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.setOffset((from.getPageNum() - 1) * from.getPageSize());
            to.setRows(from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<AgentSessionTaskVO> doList2VOList(List<AgentSessionTaskDO> list) {
        return CommonUtil.listConverter(list, AgentSessionTaskConverter::do2VO);
    }
}