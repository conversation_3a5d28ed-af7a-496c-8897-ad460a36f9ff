package ai.conrain.aigc.platform.service.model.query;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * CaptionAttributeQuery
 *
 * @version CaptionAttributeService.java v 0.1 2025-08-14 07:49:02
 */
@Data
public class CaptionAttributeQuery implements Serializable {
    /** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** 主键ID，唯一标识一个属性 */
    private Integer id;

    private List<Integer> ids;

    /** 属性键值（英文标识），用于程序中引用，如 color, style, material */
    private String key;

    /** 属性名称（中文标识），用于前端展示或内部识别 */
    private String name;

    /** 前端展示名称，如“颜色”、“风格” */
    private String displayName;

    /** 枚举值列表（JSONB格式），用于下拉选择，格式: [{"value": "red", "label": "红色"}] */
    private String enumerations;

    /** 国际化枚举展示名（JSONB），如 {"en": "Red", "zh": "红色"}，可选 */
    private String displayEnumerations;

    /** 是否启用：true 表示该属性可用，false 表示停用（软删除） */
    private Boolean isActive;

    /** 排序权重，数值越小越靠前 */
    private Integer sortOrder;

    /** 记录创建时间，自动填充 */
    private Date createTime;

    /** 最后修改时间，更新时自动刷新 */
    private Date modifyTime;

    /** 属性类型，用于区分应用场景，如 clothing（服装）、background（背景）、accessory（配饰）等 */
    private String type;

    /** 值格式类型，默认 string；可选：string, number, boolean, date, enum */
    private String format;

    /** 是否禁用：true 表示禁用（不显示），与 is_active 配合使用，用于灰度控制 */
    private Boolean disabled;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

}