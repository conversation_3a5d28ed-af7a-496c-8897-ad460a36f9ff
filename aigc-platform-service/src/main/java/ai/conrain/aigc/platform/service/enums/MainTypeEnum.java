/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.enums;

import java.util.Arrays;
import java.util.List;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 主子类型枚举
 *
 * <AUTHOR>
 * @version : MainTypeEnum.java, v 0.1 2025/5/8 23:59 renxiao.wu Exp $
 */
@Getter
public enum MainTypeEnum {
    NORMAL("NORMAL", "普通"),
    MAIN("MAIN", "主模型"),
    SUB("SUB", "子模型"),
    ;

    /** 枚举码 */
    private final String code;

    /** 枚举描述 */
    private final String desc;

    /** 用户可见的主子类型列表 */
    public static final List<String> USER_VIEW_MAIN_TYPES = Arrays.asList(MAIN.getCode(), NORMAL.getCode());
    /** 需要轮询的主子类型列表 */
    public static final List<String> POLLING_MAIN_TYPES = Arrays.asList(SUB.getCode(), NORMAL.getCode());

    private MainTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据枚举码获取枚举
     *
     * @param code 枚举码
     * @return 对应枚举
     */
    public static MainTypeEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }

        for (MainTypeEnum item : values()) {
            if (StringUtils.equals(item.getCode(), code)) {
                return item;
            }
        }

        return null;
    }
}
