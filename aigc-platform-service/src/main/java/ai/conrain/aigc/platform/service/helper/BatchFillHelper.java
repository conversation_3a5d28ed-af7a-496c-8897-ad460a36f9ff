/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.helper;

import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.service.component.CreativeBatchService;
import ai.conrain.aigc.platform.service.component.CreativeTaskService;
import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.component.dispatch.FileDispatch;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.enums.ModelTypeEnum;
import ai.conrain.aigc.platform.service.model.common.UserNickClz;
import ai.conrain.aigc.platform.service.model.vo.*;
import ai.conrain.aigc.platform.service.util.ComfyUIUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.FileUtils;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.*;

/**
 * 批量填充帮助类
 *
 * <AUTHOR>
 * @version : BatchFillHelper.java, v 0.1 2024/6/27 16:48 renxiao.wu Exp $
 */
@Slf4j
@Component
public class BatchFillHelper {
    @Lazy
    @Autowired
    private UserService userService;

    @Lazy
    @Autowired
    private CreativeTaskService creativeTaskService;

    @Lazy
    @Autowired
    private MaterialModelService materialModelService;
    @Autowired
    private OssService ossService;
    @Autowired
    private FileDispatch fileDispatch;
    
    // 自注入，解决Spring AOP内部方法调用问题
    @Lazy
    @Autowired
    private BatchFillHelper self;

    @Lazy
    @Autowired
    private CreativeBatchService creativeBatchService;

    /**
     * 批量填充用户昵称
     *
     * @param list 目标列表
     */
    public void batchFillUserNick(List<? extends UserNickClz> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<Integer> userIds = list.stream().map(UserNickClz::getUserId).distinct().collect(Collectors.toList());
        List<UserVO> users = userService.batchQueryById(userIds);

        if (CollectionUtils.isEmpty(users)) {
            log.warn("通过userId列表批量查询user信息失败，返回的结果为空,userIds={}", userIds);
            return;
        }

        list.forEach(item -> {
            users.stream().filter(user -> user.getId().equals(item.getUserId())).findFirst().ifPresent(user -> {
                item.setUserNick(user.getNickName());
            });
        });
    }

    /**
     * 尝试获取原始的batch信息, 填充原始的 modelId, modelType, proportion,
     * @param url 图片url
     * @param batch 创作批次
     */
    public void fillOriginBatchInfo(String url,Integer taskId, CreativeBatchVO batch) {
        CreativeTaskVO task;
        String imageProportion;
        taskId = Objects.nonNull(taskId) ? taskId : FileUtils.getTaskIdByImageName( url );
        if (taskId != null) {
            task = creativeTaskService.selectById(taskId);
            if (Objects.isNull(task)) {
                log.warn("通过taskId查询创作任务失败，taskId={}", taskId);
                return;
            }
            imageProportion = task.getImageProportion();
            batch.addExtInfo(KEY_ORIGIN_TASK, task.getId());
            batch.setImageProportion(StringUtils.isNotBlank(imageProportion) ? imageProportion : "NONE");

            if (task.getModelId() != null) {
                MaterialModelVO model = materialModelService.selectById(task.getModelId());
                if (ObjectUtils.isEmpty(model)) {
                    return;
                }
                ModelTypeEnum modelType = model.getType() == ModelTypeEnum.SYSTEM ? null : model.getType();
                Integer modelId = model.getType() == ModelTypeEnum.SYSTEM ? null : task.getModelId();
                batch.setModelId(modelId);
                batch.setModelType(modelType);
            }

            CreativeBatchVO originBatch = creativeBatchService.selectById(task.getBatchId());
            if (ObjectUtils.isEmpty(originBatch)) {
                log.warn("通过batchId查询创作批次失败，batchId={}", task.getBatchId());
                return;
            }
            if (CreativeTypeEnum.CREATE_IMAGE.equals(originBatch.getType())) {
                CreativeBatchVO originInfo = new CreativeBatchVO();
                originInfo.setId(originBatch.getId());
                originInfo.setModelId(originBatch.getModelId());
                originInfo.setModelName(originBatch.getModelName());
                originInfo.setFaceName(originBatch.getFaceName());
                originInfo.setSceneName(originBatch.getSceneName());
                originInfo.setImageProportion(originBatch.getImageProportion());
                originInfo.setUserId(originBatch.getUserId());
                originInfo.setUserNick(originBatch.getUserNick());
                originInfo.setOperatorId(originBatch.getOperatorId());
                originInfo.setOperatorNick(originBatch.getOperatorNick());
                originInfo.addExtInfo(KEY_CAMERA_ANGLE, originBatch.getExtInfo(KEY_CAMERA_ANGLE, List.class));
                originInfo.addExtInfo(KEY_COLOR_INDEX, originInfo.getExtInfo(KEY_COLOR_INDEX, Integer.class));
                batch.addExtInfo(KEY_ORIGINAL_BATCH_INFO, originInfo);
            } else {
                batch.addExtInfo(KEY_ORIGINAL_BATCH_INFO, originBatch.getExtInfo(KEY_ORIGINAL_BATCH_INFO, CreativeBatchVO.class));
            }
        }
    }

    public CreativeTaskVO getTaskByUrl (String url) {
        Integer taskId = FileUtils.getTaskIdByImageName(url);
        if (Objects.isNull(taskId)) {
            return null;
        }
        return creativeTaskService.selectById(taskId);
    }

    /**
     * 上传文件到 comfyUI input下 - 强制转换为PNG
     * @param image 图片oss地址
     * @return 图片comfyUI地址
     */
    public String uploadImageForcePng(String image) throws IOException {
        String url = CommonUtil.getFilePathAndNameFromURL(image);
        String originImageName = CommonUtil.getFileNameWithoutExtension(url);
        String tmpUrl = ossService.downloadFile(url, "/tmp/", originImageName);
        BufferedImage originalImage = ImageIO.read(new File(tmpUrl));

        // 缩放，现在最大边为1920
        originalImage = FileUtils.resize(originalImage, MAX_INPUT_IMAGE_SIZE);
        //2.上传到comfyui的input下
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(originalImage, "png", baos);
        byte[] imageBytes = baos.toByteArray();
        String imageName = originImageName + ".png";

        String comfyUIPath = ComfyUIUtils.buildInputPath(OperationContextHolder.getMasterUserId()) + imageName;
        fileDispatch.uploadFile(comfyUIPath, new ByteArrayInputStream(imageBytes),
                OperationContextHolder.getMasterUserId());
        return comfyUIPath;
    }

    /**
     * 上传文件到 comfyUI input下 - 完全保留原格式和质量（无压缩）
     * @param image 图片oss地址
     * @param userId 主账号id, 在异步线程中上传图片时, 需要传
     * @param server 服务(可以不指定)
     * @return 图片comfyUI地址
     */
    public String uploadImageKeepFormat(String image, Integer userId, ServerVO server) throws IOException {
        String url = CommonUtil.getFilePathAndNameFromURL(image);
        String originImageName = CommonUtil.getFileNameWithoutExtension(url);
        userId = ObjectUtils.isEmpty(userId) ? OperationContextHolder.getMasterUserId() : userId;

        // 获取原文件扩展名
        String originalExtension = "jpg"; // 默认为jpg
        if (url.contains(".")) {
            originalExtension = url.substring(url.lastIndexOf('.') + 1).toLowerCase();
        }

        // 直接下载原始文件，保留完整文件名（包含扩展名）
        String originalFileName = originImageName + "." + originalExtension;
        String tmpUrl = ossService.downloadFile(url, "/tmp/", originImageName);

        // 直接读取原始文件的字节流，避免重新编码造成的质量损失
        File originalFile = new File(tmpUrl);
        if (!originalFile.exists()) {
            throw new IOException("下载的临时文件不存在: " + tmpUrl);
        }

        // 直接读取原始文件字节流（完全保留质量和格式）
        byte[] imageBytes = Files.readAllBytes(originalFile.toPath());

        // 上传到comfyUI的input下，保持原始文件名和格式
        String comfyUIPath = ComfyUIUtils.buildInputPath(OperationContextHolder.getMasterUserId()) + originalFileName;
        fileDispatch.uploadFile(comfyUIPath, new ByteArrayInputStream(imageBytes),
                userId, false, server);

        log.info("完全保留格式和质量上传完成: {} -> {}, 格式: {}, 文件大小: {} bytes（无压缩）",
                image, comfyUIPath, originalExtension, imageBytes.length);

        return comfyUIPath;
    }

    /**
     * 异步上传单个图片, 保留原格式（使用@Async注解）
     * @param image 图片oss地址
     * @return CompletableFuture包装的图片comfyUI地址
     */
    @Async
    public CompletableFuture<String> uploadImageAsync(String image, Integer userId) {
        long startTime = System.currentTimeMillis();
        
        // 提取图片文件名用于日志显示
        String fileName = image.substring(image.lastIndexOf('/') + 1, 
            image.indexOf('?') > 0 ? image.indexOf('?') : image.length());
        
        try {
            log.info("[图片并行上传] 开始异步上传图片: {}, 文件名: {}, 开始时间: {}, 线程: {}", 
                image, fileName, startTime, Thread.currentThread().getName());
            
            String result = uploadImageKeepFormat(image, userId, null);
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            log.info("[图片并行上传] 异步上传图片成功: {} -> {}, 文件名: {}, 开始时间: {}, 结束时间: {}, 耗时: {}ms, 线程: {}", 
                image, result, fileName, startTime, endTime, duration, Thread.currentThread().getName());
            
            return CompletableFuture.completedFuture(result);
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            log.error("[图片并行上传] 异步上传图片失败: {}, 文件名: {}, 开始时间: {}, 结束时间: {}, 耗时: {}ms, 线程: {}, 错误: {}", 
                image, fileName, startTime, endTime, duration, Thread.currentThread().getName(), e.getMessage(), e);
            
            CompletableFuture<String> future = new CompletableFuture<>();
            future.completeExceptionally(e);
            return future;
        }
    }

    /**
     * 并行上传多个图片到 comfyUI input下, 保留原格式
     * @param images 图片oss地址列表
     * @param userId 获取文件服务需要 userId, 如果是在同步线程里, 可以不传
     * @return 原始URL到comfyUI路径的映射
     */
    public Map<String, String> uploadImagesParallel(List<String> images, Integer userId) {
        if (CollectionUtils.isEmpty(images)) {
            log.warn("[图片并行上传] 图片列表为空，无需上传");
            return new ConcurrentHashMap<>();
        }

        log.info("[图片并行上传] 开始并行上传 {} 张图片", images.size());
        long startTime = System.currentTimeMillis();

        // 为每个图片创建异步上传任务（通过代理对象调用，确保@Async生效）
        List<CompletableFuture<AbstractMap.SimpleEntry<String, String>>> futures = images.stream()
                .filter(StringUtils::isNotBlank)
                .distinct() // 去重，避免重复上传
                .map(image -> 
                    self.uploadImageAsync(image, userId)  // 使用self代理调用，确保@Async生效
                        .thenApply(comfyUIPath -> new AbstractMap.SimpleEntry<>(image, comfyUIPath))
                        .exceptionally(throwable -> {
                            log.error("[图片并行上传] 上传图片失败: {}", image, throwable);
                            return new AbstractMap.SimpleEntry<>(image, null); // 失败时返回null值
                        })
                )
                .collect(Collectors.toList());

        // 等待所有异步任务完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0])
        );

        // 收集结果
        Map<String, String> resultMap = allFutures.thenApply(v -> 
            futures.stream()
                .map(CompletableFuture::join)
                .filter(entry -> entry.getValue() != null) // 过滤掉上传失败的
                .collect(Collectors.toConcurrentMap(
                    Map.Entry::getKey,
                    Map.Entry::getValue,
                    (existing, replacement) -> existing // 如果有重复key，保留第一个
                ))
        ).join();

        long endTime = System.currentTimeMillis();
        long totalDuration = endTime - startTime;
        
        log.info("[图片并行上传] 并行上传完成，成功: {}/{}, 总耗时: {}ms ({}秒)", 
                resultMap.size(), images.size(), totalDuration, totalDuration / 1000.0);
        
        if (resultMap.size() > 0) {
            log.info("[图片并行上传] 平均每张图片耗时: {}ms, 并发效率: {:.2f}张/秒", 
                    totalDuration / resultMap.size(), 
                    (double) resultMap.size() * 1000 / totalDuration);
        }

        return resultMap;
    }

    /**
     * 并行上传图片的便捷方法（可变参数版本）,保留原格式
     * @param images 可变参数的图片地址
     * @return 原始URL到comfyUI路径的映射
     */
    public Map<String, String> uploadImagesParallel(Integer userId, String... images) {
        return uploadImagesParallel(Arrays.asList(images), userId);
    }

}
