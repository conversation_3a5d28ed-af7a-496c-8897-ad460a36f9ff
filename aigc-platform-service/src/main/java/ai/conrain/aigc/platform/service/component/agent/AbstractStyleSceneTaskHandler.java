package ai.conrain.aigc.platform.service.component.agent;

import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisCaption;
import ai.conrain.aigc.platform.integration.ai.model.ImageSimpleCaption;
import ai.conrain.aigc.platform.service.component.AgentSessionTaskService;
import ai.conrain.aigc.platform.service.component.ImageSearchService;
import ai.conrain.aigc.platform.service.component.agent.entity.params.ClothAnalysisParams;
import ai.conrain.aigc.platform.service.component.agent.entity.params.ImageDetail;
import ai.conrain.aigc.platform.service.component.agent.entity.params.RecommendedStyleSceneParams;
import ai.conrain.aigc.platform.service.component.agent.entity.params.RefImageAnalysisParams;
import ai.conrain.aigc.platform.service.component.agent.entity.response.StyleSceneRecommendedResponse;
import ai.conrain.aigc.platform.service.enums.*;
import ai.conrain.aigc.platform.service.enums.agent.AgentSessionStatusEnum;
import ai.conrain.aigc.platform.service.model.biz.agent.ClothImgItem;
import ai.conrain.aigc.platform.service.model.biz.agent.ImageMatchScope;
import ai.conrain.aigc.platform.service.model.biz.agent.StyleImageSearchRequest;
import ai.conrain.aigc.platform.service.model.biz.agent.UserUploadStyleImg;
import ai.conrain.aigc.platform.service.model.vo.AgentSessionTaskVO;
import ai.conrain.aigc.platform.service.model.vo.StyleImageRecommendation;
import ai.conrain.aigc.platform.service.model.vo.StyleImageSearchResult;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

import static ai.conrain.aigc.platform.service.component.agent.constants.AgentConstants.*;

/**
 * 风格场景推荐任务处理器抽象基类
 * 提供通用的推荐逻辑，子类只需实现特定的业务逻辑
 */
@Slf4j
public abstract class AbstractStyleSceneTaskHandler<T> implements AgentTaskHandler<T> {

    @Autowired
    protected AgentSessionTaskService agentSessionTaskService;

    @Autowired
    protected ImageSearchService imageSearchService;

    @Override
    public final void handleTask(AgentSessionTaskVO agentSessionTask) {
        log.info("【agentTask】{}::handleTask::开始处理任务, agentTaskId={}", 
                 this.getClass().getSimpleName(), agentSessionTask.getId());

        try {
            // 1. 提取和验证任务参数
            RecommendedStyleSceneParams params = extractAndValidateParameters(agentSessionTask);

            // 2. 子类特定的参数处理（钩子方法）
            processSpecificParams(agentSessionTask, params);

            // 3. 获取任务ID
            String clothesTaskId = params.getClothesTaskId() != null ? params.getClothesTaskId().toString() : null;
            String referenceTaskId = params.getReferenceTaskId() != null ? params.getReferenceTaskId().toString() : null;

            if (clothesTaskId == null && referenceTaskId == null) {
                log.warn("【agentTask】{}::handleTask::未找到服装分析或参考图分析任务ID, taskId={}", 
                         this.getClass().getSimpleName(), agentSessionTask.getId());
                throw new IllegalArgumentException("缺少必要的分析任务ID，任务终止...");
            }

            log.info("【agentTask】{}::handleTask::提取参数成功, clothesTaskId={}, referenceTaskId={}", 
                     this.getClass().getSimpleName(), clothesTaskId, referenceTaskId);

            // 4. 构建搜索请求参数
            StyleImageSearchRequest searchRequest = buildSearchRequest(agentSessionTask, clothesTaskId, referenceTaskId, params);

            // 5. 执行搜索推荐
            StyleImageSearchResult searchResult = imageSearchService.searchAndRecommend(searchRequest);

            // 6. 转换搜索结果为指定格式并存储
            agentSessionTask.addExtInfo(KEY_STYLE_SCENE_RECOMMENDED_FULL_RESPONSE, searchResult);
            List<StyleSceneRecommendedResponse> convertedResults = convertSearchResultToResponse(searchResult);
            agentSessionTask.addExtInfo(KEY_STYLE_SCENE_RECOMMENDED_RESULT, convertedResults);

            // 7. 子类特定的后处理（钩子方法）
            postProcessResults(agentSessionTask, convertedResults, searchResult);

            // 8. 完成任务
            completeTask(agentSessionTask);

        } catch (Exception e) {
            handleTaskFailure(agentSessionTask, e);
        }
    }

    /**
     * 钩子方法：子类特定的参数处理
     * 子类可以重写此方法来处理特定的参数逻辑
     */
    protected void processSpecificParams(AgentSessionTaskVO agentSessionTask, RecommendedStyleSceneParams params) {
        // 默认实现为空，子类可以重写
    }

    /**
     * 钩子方法：子类特定的结果后处理
     * 子类可以重写此方法来进行额外的结果处理
     */
    protected void postProcessResults(AgentSessionTaskVO agentSessionTask, 
                                    List<StyleSceneRecommendedResponse> convertedResults,
                                    StyleImageSearchResult searchResult) {
        // 默认实现为空，子类可以重写
    }

    /**
     * 提取和验证任务参数
     */
    protected RecommendedStyleSceneParams extractAndValidateParameters(AgentSessionTaskVO agentSessionTask) {
        JSONObject componentContentData = getJsonObject(agentSessionTask);

        // 将 componentContentData 转换为 RecommendedStyleSceneParams 对象
        RecommendedStyleSceneParams params;
        try {
            params = JSONObject.parseObject(componentContentData.toJSONString(), RecommendedStyleSceneParams.class);
        } catch (Exception e) {
            throw new IllegalArgumentException("【agentTask】" + this.getClass().getSimpleName() + "::extractAndValidateParameters::无法解析RecommendedStyleSceneParams参数", e);
        }

        if (params == null) {
            throw new IllegalArgumentException("【agentTask】" + this.getClass().getSimpleName() + "::extractAndValidateParameters::解析后的参数对象为空");
        }

        log.info("【agentTask】{}::extractAndValidateParameters::参数解析成功, clothesTaskId={}, referenceTaskId={}", 
                 this.getClass().getSimpleName(), params.getClothesTaskId(), params.getReferenceTaskId());

        return params;
    }

    @NotNull
    private static JSONObject getJsonObject(AgentSessionTaskVO agentSessionTask) {
        JSONObject resultInfo = agentSessionTask.getResultInfo();
        if (resultInfo == null) {
            throw new IllegalArgumentException("【agentTask】任务resultInfo为空");
        }

        JSONObject componentContentData = resultInfo.getJSONObject(KEY_COMPONENT_CONTENT_DATA);
        if (componentContentData == null) {
            throw new IllegalArgumentException("【agentTask】componentContentData为空");
        }
        return componentContentData;
    }

    /**
     * 构建搜索请求参数 - 抽象方法，子类必须实现
     * 子类可以调用父类提供的通用方法来处理大部分逻辑，只需要处理特定的参数
     */
    protected abstract StyleImageSearchRequest buildSearchRequest(AgentSessionTaskVO agentSessionTask,
                                                                String clothesTaskId,
                                                                String referenceTaskId,
                                                                RecommendedStyleSceneParams params);

    /**
     * 构建基础搜索请求参数 - 提供默认实现供子类调用
     * 处理用户信息、服装数据、参考图数据等通用逻辑
     */
    protected StyleImageSearchRequest buildBaseSearchRequest(AgentSessionTaskVO agentSessionTask,
                                                           String clothesTaskId,
                                                           String referenceTaskId) {
        log.info("【agentTask】{}::buildBaseSearchRequest::构建基础搜索请求, taskId={}", 
                 this.getClass().getSimpleName(), agentSessionTask.getId());

        StyleImageSearchRequest request = new StyleImageSearchRequest();

        // 设置用户相关信息
        request.setUserId(agentSessionTask.getUserId());
        request.setUserSessionId(agentSessionTask.getSessionId());
        request.setUserSessionTaskId(agentSessionTask.getId());

        // 处理服装分析数据
        if (clothesTaskId != null) {
            processClothAnalysisData(request, clothesTaskId);
        }

        // 处理参考图分析数据
        if (referenceTaskId != null) {
            processReferenceAnalysisData(request, referenceTaskId);
        }

        return request;
    }

    /**
     * 处理服装分析数据
     */
    protected void processClothAnalysisData(StyleImageSearchRequest request, String clothesTaskId) {
        try {
            Integer taskId = Integer.valueOf(clothesTaskId);
            AgentSessionTaskVO clothTask = agentSessionTaskService.selectById(taskId);

            if (clothTask == null || clothTask.getExtInfo() == null) {
                log.warn("【agentTask】{}::processClothAnalysisData::服装分析任务不存在或数据为空, taskId={}", 
                         this.getClass().getSimpleName(), taskId);
                return;
            }

            // 从服装分析任务的resultInfo.componentContentData中提取并转换为ClothAnalysisParams对象
            JSONObject componentContentData = clothTask.getResultInfo().getJSONObject(KEY_COMPONENT_CONTENT_DATA);
            if (componentContentData == null) {
                log.warn("【agentTask】{}::processClothAnalysisData::服装分析任务无componentContentData, taskId={}", 
                         this.getClass().getSimpleName(), taskId);
                return;
            }

            // 将componentContentData转换为ClothAnalysisParams对象
            ClothAnalysisParams clothParams = JSONObject.parseObject(componentContentData.toJSONString(), ClothAnalysisParams.class);
            if (clothParams == null) {
                log.warn("【agentTask】{}::processClothAnalysisData::无法解析ClothAnalysisParams, taskId={}", 
                         this.getClass().getSimpleName(), taskId);
                return;
            }

            // 获取最终分析结果
            JSONObject finalResult = clothTask.getExtInfo().getJSONObject(KEY_FINAL_RESULT);

            // 获取预标注结果
            JSONObject preLabelResult = clothTask.getExtInfo().getJSONObject(KEY_PRE_LABEL_TASK_RESULT);

            if (finalResult != null && clothParams.getClothesImage() != null) {
                ClothImgItem clothImgItem = new ClothImgItem();
                clothImgItem.setClothImgUrl(clothParams.getClothesImage());

                // 使用ClothAnalysisParams对象中的属性设置服装属性
                clothImgItem.setClothType(Optional.ofNullable(clothParams.getClothesType())
                        .map(ClothTypeEnum::valueOf)
                        .orElse(ClothTypeEnum.OnePiece));
                clothImgItem.setClothGender(Optional.ofNullable(clothParams.getGender())
                        .map(String::toUpperCase)
                        .map(ClothGenderEnum::valueOf)
                        .orElse(ClothGenderEnum.FEMALE));
                clothImgItem.setAgeGroup(Optional.ofNullable(clothParams.getAgeRange())
                        .map(String::toUpperCase)
                        .map(AgeGroupEnum::valueOf)
                        .orElse(AgeGroupEnum.ADULT));

                // 从finalResult中提取analysis部分作为服装分析结果
                JSONObject analysisData = finalResult.getJSONObject(KEY_ANALYSIS);
                if (analysisData != null) {
                    clothImgItem.setClothAnalysis(JSONObject.parseObject(analysisData.toJSONString(), ImageAnalysisCaption.class));
                }

                // 设置预标注结果
                if (preLabelResult != null) {
                    JSONObject preCaptionData = preLabelResult.getJSONObject(KEY_PRE_CAPTION);
                    if (preCaptionData != null) {
                        clothImgItem.setPreCaption(JSONObject.parseObject(preCaptionData.toJSONString(), ImageSimpleCaption.class));
                    }
                }

                request.setClothImgItem(clothImgItem);

                log.info("【agentTask】{}::processClothAnalysisData::已设置服装分析数据, clothUrl={}, clothesType={}, gender={}, ageRange={}, clothesName={}",
                        this.getClass().getSimpleName(), clothParams.getClothesImage(), clothParams.getClothesType(), 
                        clothParams.getGender(), clothParams.getAgeRange(), clothParams.getClothesName());
            }

        } catch (Exception e) {
            log.error("【agentTask】{}::processClothAnalysisData::处理服装分析数据失败", this.getClass().getSimpleName(), e);
        }
    }

    /**
     * 处理参考图分析数据
     */
    protected void processReferenceAnalysisData(StyleImageSearchRequest request, String referenceTaskId) {
        try {
            Integer taskId = Integer.valueOf(referenceTaskId);
            AgentSessionTaskVO refTask = agentSessionTaskService.selectById(taskId);

            if (refTask == null || refTask.getExtInfo() == null) {
                log.warn("【agentTask】{}::processReferenceAnalysisData::参考图分析任务不存在或数据为空, taskId={}", 
                         this.getClass().getSimpleName(), taskId);
                return;
            }

            // 从参考图任务的resultInfo.componentContentData中提取并转换为RefImageAnalysisParams对象
            JSONObject componentContentData = refTask.getResultInfo().getJSONObject(KEY_COMPONENT_CONTENT_DATA);
            if (componentContentData == null) {
                log.warn("【agentTask】{}::processReferenceAnalysisData::参考图任务无componentContentData, taskId={}", 
                         this.getClass().getSimpleName(), taskId);
                return;
            }

            // 将componentContentData转换为RefImageAnalysisParams对象
            RefImageAnalysisParams refParams = JSONObject.parseObject(componentContentData.toJSONString(), RefImageAnalysisParams.class);
            if (refParams == null) {
                log.warn("【agentTask】{}::processReferenceAnalysisData::无法解析RefImageAnalysisParams, taskId={}", 
                         this.getClass().getSimpleName(), taskId);
                return;
            }

            // 从RefImageAnalysisParams中获取参考图URL
            String refImageUrl = extractRefImageUrl(refParams);
            if (refImageUrl == null) {
                log.warn("【agentTask】{}::processReferenceAnalysisData::未找到参考图URL, taskId={}", 
                         this.getClass().getSimpleName(), taskId);
                return;
            }

            // 获取最终分析结果
            JSONObject finalResult = refTask.getExtInfo().getJSONObject(KEY_FINAL_RESULT);

            if (finalResult != null) {
                // 从finalResult中提取analysis部分作为参考图分析结果
                JSONObject analysisData = finalResult.getJSONObject(KEY_ANALYSIS);
                if (analysisData != null) {
                    UserUploadStyleImg userUploadStyleImg = new UserUploadStyleImg();

                    // 1. 设置图片URL
                    userUploadStyleImg.setImgUrl(refImageUrl);

                    // 2. 设置图片分析结果（从finalResult的analysis中获取）
                    userUploadStyleImg.setImgAnalysis(JSONObject.parseObject(analysisData.toJSONString(),
                            ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisCaption.class));

                    // 3. 设置图像匹配范围列表
                    List<ImageMatchScope> imageMatchScopeList = new ArrayList<>();
                    // 设置为全局匹配，系统会自动扩展为背景、配饰、模特面部、模特姿态等子维度
                    imageMatchScopeList.add(ImageMatchScope.ALL);
                    userUploadStyleImg.setImageMatchScopeList(imageMatchScopeList);

                    // 4. 设置到搜索请求中
                    request.setUserUploadStyleImgs(Collections.singletonList(userUploadStyleImg));

                    log.info("【agentTask】{}::processReferenceAnalysisData::已处理参考图分析数据, taskId={}, refImageUrl={}, sceneType={}, styleDescription={}",
                            this.getClass().getSimpleName(), taskId, refImageUrl, refParams.getSceneType(), refParams.getStyleDescription());
                } else {
                    log.warn("【agentTask】{}::processReferenceAnalysisData::参考图finalResult中无analysis数据, taskId={}", 
                             this.getClass().getSimpleName(), taskId);
                }
            } else {
                log.warn("【agentTask】{}::processReferenceAnalysisData::参考图无finalResult数据, taskId={}", 
                         this.getClass().getSimpleName(), taskId);
            }

        } catch (Exception e) {
            log.error("【agentTask】{}::processReferenceAnalysisData::处理参考图分析数据失败", this.getClass().getSimpleName(), e);
        }
    }

    /**
     * 从RefImageAnalysisParams中提取参考图URL
     */
    protected String extractRefImageUrl(RefImageAnalysisParams refParams) {
        // 优先从referenceImageList中获取第一个URL
        if (refParams.getReferenceImageList() != null && !refParams.getReferenceImageList().isEmpty()) {
            return refParams.getReferenceImageList().getFirst();
        }

        // 如果referenceImageList为空，则使用refImageUrl
        if (refParams.getRefImageUrl() != null) {
            return refParams.getRefImageUrl();
        }

        return null;
    }

    /**
     * 配置搜索选项
     * 子类可以重写此方法来自定义搜索配置
     */
    protected void configureSearchOptions(StyleImageSearchRequest request, AgentSessionTaskVO agentSessionTask, RecommendedStyleSceneParams params) {
        // 设置聚类选项参数（参考测试方法）
        request.getClusterOptions().setBgGroupCountLimit(15); // 背景聚类组数量限制
        request.getClusterOptions().setBgSzCountLimit(30);     // 背景聚类大小限制
        request.getClusterOptions().setBgSzShow(2);            // 每个聚类显示图片数
        request.getClusterOptions().setMinBgClusterSz(1);      // 最小背景聚类大小

        // 设置搜索模式为风格搜索
        request.setSearchMode(SearchModeEnum.STYLE);

        log.info("【agentTask】{}::configureSearchOptions::已配置搜索选项, taskId={}", 
                 this.getClass().getSimpleName(), agentSessionTask.getId());
    }

    /**
     * 转换搜索结果为指定的响应格式
     * 将三层列表结构转换为两层结构（流派 -> 图片列表）
     */
    protected List<StyleSceneRecommendedResponse> convertSearchResultToResponse(StyleImageSearchResult searchResult) {
        List<StyleSceneRecommendedResponse> responseList = new ArrayList<>();

        if (searchResult == null || searchResult.getItems() == null) {
            log.warn("【agentTask】{}::convertSearchResultToResponse::搜索结果为空", this.getClass().getSimpleName());
            return responseList;
        }

        try {
            // 原始数据结构: List<List<List<StyleImageRecommendation>>> items
            // 第一层：流派分组，第二层：细分组，第三层：具体图片
            List<List<List<StyleImageRecommendation>>> items = searchResult.getItems();

            for (List<List<StyleImageRecommendation>> genreGroup : items) {
                if (genreGroup == null || genreGroup.isEmpty()) {
                    continue;
                }

                // 创建流派响应对象
                StyleSceneRecommendedResponse genreResponse = new StyleSceneRecommendedResponse();
                List<ImageDetail> allImagesInGenre = new ArrayList<>();

                ClothShootGenreEnum genreType = null;

                // 遍历当前流派下的所有细分组
                for (List<StyleImageRecommendation> subGroup : genreGroup) {
                    if (subGroup == null || subGroup.isEmpty()) {
                        continue;
                    }

                    // 遍历细分组下的所有图片
                    for (StyleImageRecommendation recommendation : subGroup) {
                        if (recommendation != null) {
                            ImageDetail imageDetail = convertToImageDetail(recommendation);
                            if (imageDetail != null) {
                                allImagesInGenre.add(imageDetail);

                                // 获取流派类型（从第一张图片中获取）
                                if (genreType == null && recommendation.getClothShootGenreEnum() != null) {
                                    genreType = recommendation.getClothShootGenreEnum();
                                }
                            }
                        }
                    }
                }

                // 如果当前流派有图片，则添加到结果列表
                if (!allImagesInGenre.isEmpty() && Objects.nonNull(genreType)) {
                    genreResponse.setClothShoot(genreType.getCode());
                    genreResponse.setClothShootName(genreType.getDisplayName());
                    genreResponse.setImageDetailList(allImagesInGenre);

                    // 设置搜索任务ID
                    if (searchResult.getSearchTaskVO() != null) {
                        genreResponse.setSearchId(searchResult.getSearchTaskVO().getId());
                    }

                    // 计算流派级别的相似度（取平均值）
                    double avgSimilarity = allImagesInGenre.stream()
                            .mapToDouble(img -> Double.parseDouble(img.getStyleSimilarity()))
                            .average()
                            .orElse(0.0);
                    genreResponse.setStyleSimilarity(String.format("%.6f", avgSimilarity));

                    // 生成推荐原因（可以后续通过GPT优化）
                    genreResponse.setReason("暂时未实现暂时未实现暂时未实现暂时未实现暂时未实现暂时未实现");

                    responseList.add(genreResponse);

                    log.info("【agentTask】{}::convertSearchResultToResponse::转换流派 {} 完成，包含 {} 张图片",
                            this.getClass().getSimpleName(), genreType, allImagesInGenre.size());
                }
            }

            log.info("【agentTask】{}::convertSearchResultToResponse::转换完成，共 {} 个流派", 
                     this.getClass().getSimpleName(), responseList.size());

        } catch (Exception e) {
            log.error("【agentTask】{}::convertSearchResultToResponse::转换搜索结果失败", this.getClass().getSimpleName(), e);
        }

        return responseList;
    }

    /**
     * 转换StyleImageRecommendation为ImageDetail
     */
    protected ImageDetail convertToImageDetail(StyleImageRecommendation recommendation) {
        try {
            ImageDetail imageDetail = new ImageDetail();

            imageDetail.setImageId(recommendation.getImageId());
            imageDetail.setImageUrl(recommendation.getImageUrl());
            imageDetail.setMatchScore(String.valueOf(recommendation.getMatchScore()));
            imageDetail.setBgClusterKey(recommendation.getBgClusterKey());
            imageDetail.setIdxInCluster(recommendation.getIdxInCluster());
            imageDetail.setImageCaptionId(recommendation.getImageCaptionId());
            imageDetail.setStyleSimilarity(String.valueOf(recommendation.getStyleSimilarity()));
            imageDetail.setClothShootGenreEnum(recommendation.getClothShootGenreEnum() != null ?
                    recommendation.getClothShootGenreEnum().name() : null);

            return imageDetail;
        } catch (Exception e) {
            log.error("【agentTask】{}::convertToImageDetail::转换图片详情失败", this.getClass().getSimpleName(), e);
            return null;
        }
    }


    /**
     * 完成任务
     */
    protected void completeTask(AgentSessionTaskVO agentSessionTask) {
        agentSessionTask.setStatus(AgentSessionStatusEnum.FINISHED);
        agentSessionTask.setFinishTime(new Date());
        agentSessionTaskService.updateByIdSelective(agentSessionTask);

        log.info("【agentTask】{}::completeTask::任务完成, agentTaskId={}", 
                 this.getClass().getSimpleName(), agentSessionTask.getId());
    }

    /**
     * 处理任务失败
     */
    protected void handleTaskFailure(AgentSessionTaskVO agentSessionTask, Exception e) {
        log.error("【agentTask】{}::handleTaskFailure::任务失败, agentTaskId={}", 
                  this.getClass().getSimpleName(), agentSessionTask.getId(), e);

        // 构建错误信息
        JSONObject errorResult = new JSONObject();
        errorResult.put("error", true);
        errorResult.put("errorMessage", e.getMessage());
        errorResult.put("timestamp", new Date());

        agentSessionTask.addExtInfo(KEY_ERROR_RESULT, errorResult);
        agentSessionTask.setStatus(AgentSessionStatusEnum.FAILED);
        agentSessionTaskService.updateByIdSelective(agentSessionTask);
    }
}