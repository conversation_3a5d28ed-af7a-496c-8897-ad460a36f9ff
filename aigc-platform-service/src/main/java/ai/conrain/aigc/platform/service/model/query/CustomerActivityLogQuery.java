package ai.conrain.aigc.platform.service.model.query;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * CustomerActivityLogQuery
 *
 * @version CustomerActivityLogService.java
 */
@Data
public class CustomerActivityLogQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

        /** id */
        private Integer id;

        /** 记录日期 */
        private String dt;

        /** 分类标题 */
        private String title;

        /** 用户id */
        private Integer userId;

        /** 客户名称 */
        private String customerName;

        /** 客户公司 */
        private String customerCorp;

        /** 用户分组 */
        private String userGroup;

        /** 销售id */
        private Integer salesId;

        /** 销售id */
        private Integer salesName;

        /** 销售区域 */
        private String salesArea;

        /** 首次充值时间 */
        private Date firstPayTime;

        /** 充值金额 */
        private BigDecimal payAmount;

        /** 消费金额 */
        private BigDecimal consumeAmount;

        /** 充值次数 */
        private Integer payTimes;

        /** 创建时间 */
        private Date createTime;

        /** 修改时间 */
        private Date modifyTime;

        /** 翻页参数：页面大小 */
        private Integer pageSize;

        /** 翻页参数：页数（从1开始） */
        private Integer pageNum;

        /** 排序指令（示例："id asc"） */
        private String orderBy;

}