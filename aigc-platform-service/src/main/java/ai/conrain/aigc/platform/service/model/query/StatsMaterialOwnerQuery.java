package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * StatsMaterialOwnerQuery
 *
 * @version StatsMaterialOwnerService.java v 0.1 2025-04-30 03:31:59
 */
@Data
public class StatsMaterialOwnerQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 统计类型：DAILY/WEEKLY/MONTHLY/TOTAL */
    private String statsType;

    /** 统计日期: 格式为yyyy-MM-dd */
    private String statsDate;

    /** 用户 id（为 0 时则是汇总） */
    private Integer userId;

    /** 用户名称 */
    private String nickname;

    /** 交付数量 */
    private Integer deliveryCount;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 扩展字段 */
    private String extInfo;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

}
