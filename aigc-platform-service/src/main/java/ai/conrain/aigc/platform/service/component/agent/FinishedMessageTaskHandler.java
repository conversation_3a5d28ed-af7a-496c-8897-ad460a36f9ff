package ai.conrain.aigc.platform.service.component.agent;

import ai.conrain.aigc.platform.service.component.agent.entity.params.DefaultParams;
import ai.conrain.aigc.platform.service.enums.agent.AgentSessionTaskTypeEnum;
import ai.conrain.aigc.platform.service.model.vo.AgentSessionTaskVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 完成提示消息任务处理器
 */
@Slf4j
@Component
public class FinishedMessageTaskHandler implements AgentTaskHandler<DefaultParams> {
    
    @Override
    public void handleTask(AgentSessionTaskVO agentSessionTask) {
        log.info("【agentTask】FinishedMessageTaskHandler::handleTask::提示消息任务无需额外逻辑处理，直接放行, taskId={}", agentSessionTask.getId());
    }
    
    @Override
    public String getSupportedTaskType() {
        return AgentSessionTaskTypeEnum.FINISHED_MESSAGE.getCode();
    }
    
    @Override
    public Class<DefaultParams> getParameterType() {
        return DefaultParams.class;
    }

}