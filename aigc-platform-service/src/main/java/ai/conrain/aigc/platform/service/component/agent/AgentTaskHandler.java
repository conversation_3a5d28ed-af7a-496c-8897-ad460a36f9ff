package ai.conrain.aigc.platform.service.component.agent;

import ai.conrain.aigc.platform.service.model.vo.AgentSessionTaskVO;
import com.alibaba.fastjson.JSONObject;

import static ai.conrain.aigc.platform.service.component.agent.constants.AgentConstants.KEY_COMPONENT_CONTENT_DATA;

/**
 * Agent任务处理器接口
 * @param <T> 任务参数类型
 */
public interface AgentTaskHandler<T> {
    
    /**
     * 处理任务
     * @param task 任务对象
     */
    void handleTask(AgentSessionTaskVO task);
    
    /**
     * 获取支持的任务类型
     * @return 任务类型
     */
    String getSupportedTaskType();

    /**
     * 获取任务参数类型
     * @return 参数类的Class对象
     */
    Class<T> getParameterType();


    /**
     * 从任务结果信息中提取参数对象
     * 默认实现：从resultInfo.componentContentData中提取参数
     *
     * @param task 任务对象
     * @return 参数对象，如果提取失败返回null
     */
    default T extractParameters(AgentSessionTaskVO task) {
        JSONObject resultInfo = task.getResultInfo();
        if (resultInfo == null) {
            return null;
        }
        
        // 检查是否包含componentContentData
        if (!resultInfo.containsKey(KEY_COMPONENT_CONTENT_DATA)) {
            return null;
        }
        
        // 提取componentContentData并转换为参数对象
        Object componentContentData = resultInfo.get(KEY_COMPONENT_CONTENT_DATA);
        if (componentContentData == null) {
            return null;
        }
        
        try {
            return JSONObject.parseObject(
                JSONObject.toJSONString(componentContentData),
                getParameterType()
            );
        } catch (Exception e) {
            // 参数解析失败
            return null;
        }
    }
}