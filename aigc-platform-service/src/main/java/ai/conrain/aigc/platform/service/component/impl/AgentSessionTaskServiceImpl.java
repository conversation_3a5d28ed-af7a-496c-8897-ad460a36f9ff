package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.dao.AgentSessionTaskDAO;
import ai.conrain.aigc.platform.dal.entity.AgentSessionTaskDO;
import ai.conrain.aigc.platform.dal.example.AgentSessionTaskExample;
import ai.conrain.aigc.platform.service.component.AgentSessionTaskService;
import ai.conrain.aigc.platform.service.component.agent.ClothAnalysisTaskHandler;
import ai.conrain.aigc.platform.service.component.agent.constants.AgentConstants;
import ai.conrain.aigc.platform.service.component.agent.translation.TranslationService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.AgentSessionTaskConverter;
import ai.conrain.aigc.platform.service.model.query.AgentSessionTaskQuery;
import ai.conrain.aigc.platform.service.model.vo.AgentSessionTaskVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * AgentSessionTaskService实现
 *
 * <AUTHOR>
 * @version AgentSessionTaskService.java
 */
@Slf4j
@Service
public class AgentSessionTaskServiceImpl implements AgentSessionTaskService {

    /**
     * DAO
     */
    @Autowired
    private AgentSessionTaskDAO agentSessionTaskDAO;

    /**
     * 服装分析任务处理器
     */
    @Autowired
    private ClothAnalysisTaskHandler clothAnalysisTaskHandler;

    /**
     * 翻译服务
     */
    @Autowired
    private TranslationService translationService;

    @Override
    public AgentSessionTaskVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        AgentSessionTaskDO data = agentSessionTaskDAO.selectByPrimaryKey(id);
        if (null == data) {
            return null;
        }

        return AgentSessionTaskConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = agentSessionTaskDAO.deleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除AgentSessionTask失败");
    }

    @Override
    public AgentSessionTaskVO insert(AgentSessionTaskVO agentSessionTask) {
        AssertUtil.assertNotNull(agentSessionTask, ResultCode.PARAM_INVALID, "agentSessionTask is null");
        AssertUtil.assertTrue(agentSessionTask.getId() == null, ResultCode.PARAM_INVALID, "agentSessionTask.id is present");

        // 排序默认为 1
        Integer orderNum = 1;

        // 若前置任务 ID 不为空，则需要对前置任务进行数据正确性校验
        if (Objects.nonNull(agentSessionTask.getPreTaskId())) {
            orderNum = checkPreTask(agentSessionTask.getPreTaskId());
        }

        // 设置排序
        agentSessionTask.setOrderNum(orderNum);
        agentSessionTask.setDeletedByRollback(0);

        //创建时间、修改时间兜底
        if (agentSessionTask.getCreateTime() == null) {
            agentSessionTask.setCreateTime(new Date());
        }

        if (agentSessionTask.getModifyTime() == null) {
            agentSessionTask.setModifyTime(new Date());
        }

        AgentSessionTaskDO data = AgentSessionTaskConverter.vo2DO(agentSessionTask);
        Integer n = agentSessionTaskDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建AgentSessionTask失败");
        AssertUtil.assertNotNull(data.getId(), "新建AgentSessionTask返回id为空");
        agentSessionTask.setId(data.getId());
        return agentSessionTask;
    }


    @Override
    public void updateByIdSelective(AgentSessionTaskVO agentSessionTask) {
        AssertUtil.assertNotNull(agentSessionTask, ResultCode.PARAM_INVALID, "agentSessionTask is null");
        AssertUtil.assertTrue(agentSessionTask.getId() != null, ResultCode.PARAM_INVALID, "agentSessionTask.id is null");

        //修改时间必须更新
        agentSessionTask.setModifyTime(new Date());
        AgentSessionTaskDO data = AgentSessionTaskConverter.vo2DO(agentSessionTask);
        int n = agentSessionTaskDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新AgentSessionTask失败，影响行数:" + n);
    }

    @Override
    public List<AgentSessionTaskVO> queryAgentSessionTaskList(AgentSessionTaskQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        // 根据 orderNum 从小到大排序
        query.setOrderBy("order_num asc");

        AgentSessionTaskExample example = AgentSessionTaskConverter.query2Example(query);

        List<AgentSessionTaskDO> list = agentSessionTaskDAO.selectByExampleWithBLOBs(example);
        return AgentSessionTaskConverter.doList2VOList(list);
    }

    @Override
    public Long queryAgentSessionTaskCount(AgentSessionTaskQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AgentSessionTaskExample example = AgentSessionTaskConverter.query2Example(query);
        return agentSessionTaskDAO.countByExample(example);
    }

    /**
     * 带条件分页查询会话任务表：1个会话可包含多个子任务
     */
    @Override
    public PageInfo<AgentSessionTaskVO> queryAgentSessionTaskByPage(AgentSessionTaskQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                && query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
                + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<AgentSessionTaskVO> page = new PageInfo<>();

        AgentSessionTaskExample example = AgentSessionTaskConverter.query2Example(query);
        long totalCount = agentSessionTaskDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<AgentSessionTaskDO> list = agentSessionTaskDAO.selectByExample(example);
        page.setList(AgentSessionTaskConverter.doList2VOList(list));
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Override
    public List<AgentSessionTaskVO> queryAgentSessionTaskBySessionId(Integer id) {
        // 初始化条件
        AgentSessionTaskQuery agentSessionTaskQuery = new AgentSessionTaskQuery();

        agentSessionTaskQuery.setSessionId(id);
        agentSessionTaskQuery.setDeletedByRollback(0);


        return queryAgentSessionTaskList(agentSessionTaskQuery);
    }

    @Override
    public void makeSurePlan(List<Integer> idList) {
        AssertUtil.assertNotNull(idList, ResultCode.PARAM_INVALID, "idList不能为空");
        AssertUtil.assertTrue(CollectionUtils.isNotEmpty(idList), ResultCode.PARAM_INVALID, "idList不能为空列表");

        log.info("开始处理确认策划任务，任务ID列表: {}", idList);

        for (Integer taskId : idList) {
            try {
                // 1. 查询任务详情
                AgentSessionTaskVO task = selectById(taskId);
                if (task == null) {
                    log.warn("任务不存在，跳过处理: {}", taskId);
                    continue;
                }

                log.info("处理任务: {} (类型: {})", taskId, task.getTaskType());

                // 2. 根据任务类型进行处理
                switch (task.getTaskType()) {
                    case "CLOTH_ANALYSIS":
                        processClothAnalysisTask(task);
                        break;
                    case "REF_IMAGE_ANALYSIS":
                        processRefImageAnalysisTask(task);
                        break;
                    default:
                        log.info("任务类型 {} 无需处理", task.getTaskType());
                        break;
                }

            } catch (Exception e) {
                log.error("处理任务 {} 时发生异常", taskId, e);
            }
        }

        log.info("确认策划任务处理完成");
    }

    /**
     * 处理服装分析任务
     */
    private void processClothAnalysisTask(AgentSessionTaskVO task) {
        JSONObject extInfo = task.getExtInfo();
        if (extInfo == null) {
            log.warn("任务 {} 的扩展信息为空", task.getId());
            return;
        }

        // 2. 取出翻译信息和元数据信息
        JSONObject translationResult = extInfo.getJSONObject(AgentConstants.KEY_TRANSLATION_ANALYSIS_CLOTHES_TASK_RESULT);
        JSONObject originalResult = extInfo.getJSONObject(AgentConstants.KEY_ANALYSIS_CLOTHES_TASK_RESULT);

        if (translationResult == null) {
            log.warn("任务 {} 的翻译分析结果为空", task.getId());
            return;
        }

        if (originalResult == null) {
            log.warn("任务 {} 的原始分析结果为空", task.getId());
            return;
        }

        // 3. 解析出需要翻译处理的字段信息
        Map<String, String> fieldsToTranslate = extractFieldsToTranslate(translationResult);
        
        JSONObject finalResult;
        
        if (fieldsToTranslate.isEmpty()) {
            log.info("任务 {} 没有需要翻译的字段，使用原始结果", task.getId());
            // 没有变化则设置为originalResult的深拷贝，避免循环引用
            finalResult = JSONObject.parseObject(originalResult.toJSONString());
        } else {
            log.info("任务 {} 发现需要翻译的字段数量: {}", task.getId(), fieldsToTranslate.size());
            
            // 4. 对字段进行翻译处理
            Map<String, String> translatedFields = translateFields(fieldsToTranslate);
            
            // 5. 在元数据基础上替换变更的属性
            finalResult = replaceFieldsInOriginalResult(originalResult, translatedFields);
            
            log.info("任务 {} 翻译了 {} 个字段", task.getId(), translatedFields.size());
        }

        // 6. 存储处理完成的结果
        extInfo.put(AgentConstants.KEY_FINAL_RESULT, finalResult);
        task.setExtInfo(extInfo);
        updateByIdSelective(task);

        log.info("任务 {} 服装分析处理完成", task.getId());
    }

    /**
     * 处理参考图分析任务
     */
    private void processRefImageAnalysisTask(AgentSessionTaskVO task) {
        JSONObject extInfo = task.getExtInfo();
        if (extInfo == null) {
            log.warn("任务 {} 的扩展信息为空", task.getId());
            return;
        }

        // 2. 取出翻译信息和元数据信息
        JSONObject translationResult = extInfo.getJSONObject(AgentConstants.KEY_TRANSLATION_REF_ANALYSIS_TASK_RESULT);
        JSONObject originalResult = extInfo.getJSONObject(AgentConstants.KEY_REF_ANALYSIS_TASK_RESULT);

        if (translationResult == null) {
            log.warn("任务 {} 的翻译参考图分析结果为空", task.getId());
            return;
        }

        if (originalResult == null) {
            log.warn("任务 {} 的原始参考图分析结果为空", task.getId());
            return;
        }

        // 3. 解析出需要翻译处理的字段信息
        Map<String, String> fieldsToTranslate = extractFieldsToTranslate(translationResult);
        
        JSONObject finalResult;
        
        if (fieldsToTranslate.isEmpty()) {
            log.info("任务 {} 没有需要翻译的字段，使用原始结果", task.getId());
            // 没有变化则设置为originalResult的深拷贝，避免循环引用
            finalResult = JSONObject.parseObject(originalResult.toJSONString());
        } else {
            log.info("任务 {} 发现需要翻译的字段数量: {}", task.getId(), fieldsToTranslate.size());
            
            // 4. 对字段进行翻译处理
            Map<String, String> translatedFields = translateFields(fieldsToTranslate);
            
            // 5. 在元数据基础上替换变更的属性
            finalResult = replaceFieldsInOriginalResult(originalResult, translatedFields);
            
            log.info("任务 {} 翻译了 {} 个字段", task.getId(), translatedFields.size());
        }

        // 6. 存储处理完成的结果
        extInfo.put(AgentConstants.KEY_FINAL_RESULT, finalResult);
        task.setExtInfo(extInfo);
        updateByIdSelective(task);

        log.info("任务 {} 参考图分析处理完成", task.getId());
    }

    /**
     * 从翻译结果中提取需要翻译的字段（isUpdate为true的字段）
     *
     * @param translationResult 翻译结果对象
     * @return 字段路径到内容的映射
     */
    private Map<String, String> extractFieldsToTranslate(JSONObject translationResult) {
        Map<String, String> fieldsToTranslate = new HashMap<>();
        extractFieldsRecursive(translationResult, "", fieldsToTranslate);
        return fieldsToTranslate;
    }

    /**
     * 递归提取需要翻译的字段
     *
     * @param obj               当前对象
     * @param path              当前路径
     * @param fieldsToTranslate 收集的字段
     */
    private void extractFieldsRecursive(Object obj, String path, Map<String, String> fieldsToTranslate) {
        if (obj instanceof JSONObject) {
            JSONObject jsonObj = (JSONObject) obj;

            // 检查是否是最底层的字段对象（包含content和isUpdate）
            if (jsonObj.containsKey("content") && jsonObj.containsKey("isUpdate")) {
                Boolean isUpdate = jsonObj.getBoolean("isUpdate");
                if (Boolean.TRUE.equals(isUpdate)) {
                    String content = jsonObj.getString("content");
                    if (content != null && !content.trim().isEmpty()) {
                        fieldsToTranslate.put(path, content);
                        log.debug("发现需要翻译的字段: {} = {}", path, content);
                    }
                }
                return; // 已经是最底层，不再继续递归
            }

            // 继续递归处理子对象
            for (String key : jsonObj.keySet()) {
                Object value = jsonObj.get(key);
                String newPath = path.isEmpty() ? key : path + "." + key;
                extractFieldsRecursive(value, newPath, fieldsToTranslate);
            }
        }
    }

    /**
     * 对字段进行翻译处理
     *
     * @param fieldsToTranslate 需要翻译的字段映射
     * @return 翻译后的字段映射
     */
    private Map<String, String> translateFields(Map<String, String> fieldsToTranslate) {
        Map<String, String> translatedFields = new HashMap<>();

        for (Map.Entry<String, String> entry : fieldsToTranslate.entrySet()) {
            String fieldPath = entry.getKey();
            String chineseText = entry.getValue();

            try {
                // 使用翻译服务将中文翻译为英文
                String englishText = translationService.translateToEnglish(chineseText);
                translatedFields.put(fieldPath, englishText);
                log.debug("字段翻译完成: {} -> {}", chineseText, englishText);
            } catch (Exception e) {
                log.error("翻译字段 {} 失败: {}", fieldPath, chineseText, e);
                // 翻译失败时保持原文
                translatedFields.put(fieldPath, chineseText);
            }
        }

        return translatedFields;
    }

    /**
     * 在原始结果基础上替换变更的字段
     *
     * @param originalResult   原始结果
     * @param translatedFields 翻译后的字段
     * @return 替换后的最终结果
     */
    private JSONObject replaceFieldsInOriginalResult(JSONObject originalResult, Map<String, String> translatedFields) {
        // 深拷贝原始结果
        JSONObject finalResult = JSONObject.parseObject(originalResult.toJSONString());

        // 替换翻译后的字段
        for (Map.Entry<String, String> entry : translatedFields.entrySet()) {
            String fieldPath = entry.getKey();
            String translatedValue = entry.getValue();

            setFieldValue(finalResult, fieldPath, translatedValue);
            log.debug("替换字段: {} = {}", fieldPath, translatedValue);
        }

        return finalResult;
    }

    /**
     * 根据字段路径设置值
     *
     * @param result    结果对象
     * @param fieldPath 字段路径（如：analysis.Clothing.Top.Style）
     * @param value     要设置的值
     */
    private void setFieldValue(JSONObject result, String fieldPath, String value) {
        String[] pathParts = fieldPath.split("\\.");
        JSONObject current = result;

        // 导航到目标字段的父对象
        for (int i = 0; i < pathParts.length - 1; i++) {
            String part = pathParts[i];
            if (!current.containsKey(part)) {
                current.put(part, new JSONObject());
            }
            Object next = current.get(part);
            if (next instanceof JSONObject) {
                current = (JSONObject) next;
            } else {
                log.warn("字段路径 {} 中的 {} 不是JSONObject，无法设置值", fieldPath, part);
                return;
            }
        }

        // 设置最终值
        String finalKey = pathParts[pathParts.length - 1];
        current.put(finalKey, value);
    }


    // 检查前置任务是否足那
    private Integer checkPreTask(Integer preTaskId) {

        AgentSessionTaskVO agentSessionTaskVO = selectById(preTaskId);
        if (Objects.isNull(agentSessionTaskVO)) {
            log.error("【AgentTask】insert::checkPreTask::校验未通过，agentSessionTaskId:{}的记录不存在，新任务插入终止...", preTaskId);
            AssertUtil.assertNotNull(preTaskId, "agentSessionTaskId记录不存在，新任务插入终止...");
        }
        Integer orderNum = agentSessionTaskVO.getOrderNum();

        // 返回新任务的排序数值
        return ++orderNum;
    }

}