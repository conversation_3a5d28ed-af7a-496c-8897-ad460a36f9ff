package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;

@Getter
public enum ClothTypeEnum {
    Tops("Tops", "上装"),
    Bottoms("Bottoms", "下装"),
    TwoPiece("TwoPiece", "套装"),
    SwimSuit("SwimSuit", "泳衣"),
    OnePiece("OnePiece", "连体服装"),
    SexyLingerie("SexyLingerie", "情趣内衣"),
    ;

    private String code;
    private String desc;

    ClothTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ClothTypeEnum getByCode(String code) {
        for (ClothTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
