package ai.conrain.aigc.platform.service.component.agent.translation;

import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisCaption;
import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisResult;
import ai.conrain.aigc.platform.integration.ai.model.ImageSimpleCaption;
import ai.conrain.aigc.platform.integration.gpt.AIService;
import ai.conrain.aigc.platform.integration.gpt.GptOptions;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 图像分析结果翻译服务
 * 用于将英文的图像分析结果翻译成中文
 * 只翻译值，不翻译键名
 */
@Slf4j
@Service
public class TranslationService {

    @Autowired
    private AIService aiService;
    
    // 创建用于并行翻译的线程池
    private final ExecutorService executorService = Executors.newFixedThreadPool(2);

    /**
     * 翻译图像分析结果
     * 
     * @param analysisResult 原始分析结果
     * @return 翻译后的分析结果
     */
    public ImageAnalysisResult translateAnalysisResult(ImageAnalysisResult analysisResult) {
        if (analysisResult == null) {
            return null;
        }

        try {
            // 创建一个新的结果对象，避免修改原始数据
            ImageAnalysisResult translatedResult = JSON.parseObject(JSON.toJSONString(analysisResult), ImageAnalysisResult.class);

            // 创建并行翻译任务
            CompletableFuture<ImageAnalysisCaption> analysisFuture = null;

            // 如果有详细分析结果，创建翻译任务
            if (translatedResult.getAnalysis() != null) {
                analysisFuture = CompletableFuture.supplyAsync(() ->
                    translateAnalysisCaption(translatedResult.getAnalysis()), executorService);
            }

            // 等待并行任务完成并设置结果
            if (analysisFuture != null) {
                ImageAnalysisCaption translatedCaption = analysisFuture.get();
                translatedResult.setAnalysis(translatedCaption);
            }

            return translatedResult;
        } catch (Exception e) {
            log.error("翻译图像分析结果失败", e);
            // 如果翻译失败，返回原始结果
            return analysisResult;
        }
    }

    /**
     * 翻译详细分析标题
     */
    private ImageAnalysisCaption translateAnalysisCaption(ImageAnalysisCaption caption) {
        try {
            // 将对象转换为 JSON 字符串
            String jsonString = JSON.toJSONString(caption);
            
            // 提取所有需要翻译的值
            Map<String, String> valuesToTranslate = extractValuesFromJson(jsonString);
            
            if (valuesToTranslate.isEmpty()) {
                return caption;
            }

            // 构建翻译请求
            String translationPrompt = buildTranslationPrompt(valuesToTranslate);
            
            // 调用 GPT 进行翻译
            GptOptions options = GptOptions.builder()
                    .model("gpt-4o")
                    .temperature(0.1)
                    .maxTokens(2000)
                    .build();
            
            String translationResult = aiService.chat(translationPrompt, options);
            
            // 解析翻译结果并更新原始 JSON
            String translatedJson = applyTranslations(jsonString, translationResult);
            
            // 将翻译后的 JSON 转换回对象
            return JSON.parseObject(translatedJson, ImageAnalysisCaption.class);
            
        } catch (Exception e) {
            log.error("翻译 ImageAnalysisCaption 失败", e);
            return caption;
        }
    }

    /**
     * 翻译简单标题
     */
    private ImageSimpleCaption translateSimpleCaption(ImageSimpleCaption caption) {
        try {
            // 将对象转换为 JSON 字符串
            String jsonString = JSON.toJSONString(caption);
            
            // 提取所有需要翻译的值
            Map<String, String> valuesToTranslate = extractValuesFromJson(jsonString);
            
            if (valuesToTranslate.isEmpty()) {
                return caption;
            }

            // 构建翻译请求
            String translationPrompt = buildTranslationPrompt(valuesToTranslate);
            
            // 调用 GPT 进行翻译
            GptOptions options = GptOptions.builder()
                    .model("gpt-4o")
                    .temperature(0.1)
                    .maxTokens(1000)
                    .build();
            
            String translationResult = aiService.chat(translationPrompt, options);
            
            // 解析翻译结果并更新原始 JSON
            String translatedJson = applyTranslations(jsonString, translationResult);
            
            // 将翻译后的 JSON 转换回对象
            return JSON.parseObject(translatedJson, ImageSimpleCaption.class);
            
        } catch (Exception e) {
            log.error("翻译 ImageSimpleCaption 失败", e);
            return caption;
        }
    }

    /**
     * 从 JSON 字符串中提取所有字符串类型的值
     */
    private Map<String, String> extractValuesFromJson(String jsonString) {
        Map<String, String> values = new HashMap<>();
        JSONObject jsonObject = JSON.parseObject(jsonString);
        extractValuesRecursive(jsonObject, "", values);
        return values;
    }

    /**
     * 递归提取 JSON 对象中的所有字符串值
     */
    private void extractValuesRecursive(Object obj, String path, Map<String, String> values) {
        if (obj instanceof JSONObject) {
            JSONObject jsonObj = (JSONObject) obj;
            for (String key : jsonObj.keySet()) {
                Object value = jsonObj.get(key);
                String newPath = path.isEmpty() ? key : path + "." + key;
                
                if (value instanceof String && StringUtils.isNotBlank((String) value)) {
                    values.put(newPath, (String) value);
                } else if (value instanceof JSONObject) {
                    extractValuesRecursive(value, newPath, values);
                }
            }
        }
    }

    /**
     * 构建翻译提示词
     */
    private String buildTranslationPrompt(Map<String, String> values) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请将以下英文内容翻译成中文。这些是服装和拍摄相关的描述信息。\n");
        prompt.append("请保持专业术语的准确性，确保翻译符合电商和时尚行业的表达习惯。\n");
        prompt.append("请以 JSON 格式返回翻译结果，格式为：{\"key\": \"翻译后的中文\"}\n\n");
        prompt.append("需要翻译的内容：\n");
        
        JSONObject valuesToTranslate = new JSONObject();
        valuesToTranslate.putAll(values);
        prompt.append(JSON.toJSONString(valuesToTranslate, true));
        
        return prompt.toString();
    }

    /**
     * 将翻译结果应用到原始 JSON
     */
    private String applyTranslations(String originalJson, String translationResult) {
        try {
            // 解析翻译结果
            JSONObject translations = JSON.parseObject(translationResult);
            JSONObject originalObject = JSON.parseObject(originalJson);
            
            // 应用翻译
            for (String key : translations.keySet()) {
                String translatedValue = translations.getString(key);
                applyTranslationToPath(originalObject, key, translatedValue);
            }
            
            return JSON.toJSONString(originalObject);
        } catch (Exception e) {
            log.error("应用翻译结果失败", e);
            return originalJson;
        }
    }

    /**
     * 根据路径应用翻译值
     */
    private void applyTranslationToPath(JSONObject obj, String path, String value) {
        String[] parts = path.split("\\.");
        JSONObject current = obj;
        
        for (int i = 0; i < parts.length - 1; i++) {
            if (current.containsKey(parts[i])) {
                Object next = current.get(parts[i]);
                if (next instanceof JSONObject) {
                    current = (JSONObject) next;
                } else {
                    return;
                }
            } else {
                return;
            }
        }
        
        // 设置最终的值
        if (parts.length > 0) {
            current.put(parts[parts.length - 1], value);
        }
    }

    /**
     * 将中文文本翻译为英文
     * @param chineseText 中文文本
     * @return 英文翻译结果
     */
    public String translateToEnglish(String chineseText) {
        if (StringUtils.isBlank(chineseText)) {
            return chineseText;
        }

        try {
            // 构建翻译提示词
            String translationPrompt = buildChineseToEnglishPrompt(chineseText);
            
            // 调用 GPT 进行翻译
            GptOptions options = GptOptions.builder()
                    .model("gpt-4o")
                    .temperature(0.1)
                    .maxTokens(500)
                    .build();
            
            String translationResult = aiService.chat(translationPrompt, options);
            
            // 清理翻译结果，移除可能的引号和额外格式
            String cleanResult = cleanTranslationResult(translationResult);
            
            log.debug("中文翻译为英文: [{}] -> [{}]", chineseText, cleanResult);
            return StringUtils.isNotBlank(cleanResult) ? cleanResult : chineseText;
            
        } catch (Exception e) {
            log.error("中文翻译为英文失败: {}", chineseText, e);
            // 如果翻译失败，返回原始文本
            return chineseText;
        }
    }

    /**
     * 直接生成带状态标识的翻译结果
     * @param originalData 原始数据
     * @param modifiedData 修改后的数据
     * @return 带状态标识和英文翻译的完整结果
     */
    public JSONObject generateResultWithStatusAndTranslation(JSONObject originalData, JSONObject modifiedData) {
        try {
            // 构建提示词，让GPT直接生成带状态标识的结果
            String prompt = buildStatusAndTranslationPrompt(originalData, modifiedData);
            
            // 调用 GPT 进行处理
            GptOptions options = GptOptions.builder()
                    .model("gpt-4o")
                    .temperature(0.1)
                    .maxTokens(4000)
                    .build();
            
            String gptResult = aiService.chat(prompt, options);
            
            // 解析GPT返回的JSON结果
            JSONObject result = JSON.parseObject(gptResult);
            
            log.info("GPT直接生成带状态标识的翻译结果成功");
            return result;
            
        } catch (Exception e) {
            log.error("GPT生成带状态标识的翻译结果失败", e);
            // 降级处理：返回基本的结果结构
            return createFallbackResult(modifiedData);
        }
    }

    /**
     * 构建状态标识和翻译的提示词
     */
    private String buildStatusAndTranslationPrompt(JSONObject originalData, JSONObject modifiedData) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("你是一个专业的服装分析数据处理助手。请按照以下要求处理数据：\n\n");
        prompt.append("任务：\n");
        prompt.append("1. 比较原始数据和修改后数据，识别哪些字段被修改了\n");
        prompt.append("2. 为每个字段添加状态标识：'original'（未修改）或 'modified'（已修改）\n");
        prompt.append("3. 将修改后的中文字段翻译为英文\n");
        prompt.append("4. 返回完整的JSON结果\n\n");
        
        prompt.append("数据格式要求：\n");
        prompt.append("- 每个字段应该包装为：{\"value\": \"实际值\", \"status\": \"original|modified\", \"translation\": \"英文翻译\"}\n");
        prompt.append("- 如果字段未修改，translation字段可为空\n");
        prompt.append("- 如果字段已修改，必须提供英文翻译\n");
        prompt.append("- 保持原有的数据层级结构\n\n");
        
        prompt.append("翻译要求：\n");
        prompt.append("- 这是服装和时尚相关的内容\n");
        prompt.append("- 保持专业术语的准确性\n");
        prompt.append("- 符合电商和时尚行业的表达习惯\n\n");
        
        prompt.append("原始数据：\n");
        prompt.append(JSON.toJSONString(originalData, true));
        prompt.append("\n\n修改后数据：\n");
        prompt.append(JSON.toJSONString(modifiedData, true));
        prompt.append("\n\n请返回处理后的JSON结果：");
        
        return prompt.toString();
    }

    /**
     * 创建降级处理的结果
     */
    private JSONObject createFallbackResult(JSONObject modifiedData) {
        JSONObject fallbackResult = new JSONObject();
        
        // 简单地将所有字段标记为修改状态，不提供翻译
        addFallbackStatusToFields("", modifiedData, fallbackResult);
        
        return fallbackResult;
    }

    /**
     * 递归为字段添加降级状态标识
     */
    private void addFallbackStatusToFields(String prefix, Object data, JSONObject result) {
        if (data instanceof JSONObject) {
            JSONObject dataObj = (JSONObject) data;
            JSONObject resultObj = new JSONObject();
            
            for (String key : dataObj.keySet()) {
                Object value = dataObj.get(key);
                
                if (value instanceof JSONObject) {
                    JSONObject nestedResult = new JSONObject();
                    addFallbackStatusToFields(prefix.isEmpty() ? key : prefix + "." + key, value, nestedResult);
                    resultObj.put(key, nestedResult);
                } else {
                    // 创建降级的字段对象
                    JSONObject fieldWithStatus = new JSONObject();
                    fieldWithStatus.put("value", value);
                    fieldWithStatus.put("status", "modified"); // 降级情况下都标记为修改
                    fieldWithStatus.put("translation", ""); // 降级情况下不提供翻译
                    resultObj.put(key, fieldWithStatus);
                }
            }
            
            if (prefix.isEmpty()) {
                result.putAll(resultObj);
            } else {
                result.put(getLastKey(prefix), resultObj);
            }
        }
    }

    /**
     * 获取路径的最后一个键
     */
    private String getLastKey(String path) {
        int lastDotIndex = path.lastIndexOf('.');
        return lastDotIndex >= 0 ? path.substring(lastDotIndex + 1) : path;
    }

    /**
     * 构建中文到英文的翻译提示词
     */
    private String buildChineseToEnglishPrompt(String chineseText) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("Please translate the following Chinese text to English. ");
        prompt.append("This is fashion and clothing related content. ");
        prompt.append("Please maintain professional terminology accuracy and ensure the translation follows ");
        prompt.append("e-commerce and fashion industry expression standards.\n");
        prompt.append("Only return the translated English text, no additional formatting or explanations.\n\n");
        prompt.append("Chinese text to translate:\n");
        prompt.append(chineseText);
        
        return prompt.toString();
    }

    /**
     * 清理翻译结果
     */
    private String cleanTranslationResult(String result) {
        if (StringUtils.isBlank(result)) {
            return result;
        }
        
        // 移除首尾的引号
        String cleaned = result.trim();
        if (cleaned.startsWith("\"") && cleaned.endsWith("\"")) {
            cleaned = cleaned.substring(1, cleaned.length() - 1);
        }
        
        // 移除可能的换行符和多余空白
        cleaned = cleaned.replaceAll("\\n+", " ").trim();
        
        return cleaned;
    }
}