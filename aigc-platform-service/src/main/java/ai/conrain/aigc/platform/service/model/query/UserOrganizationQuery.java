package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;
import java.util.Date;

import java.io.Serializable;
import java.util.List;

/**
 * UserOrganizationQuery
 *
 * @version UserOrganizationService.java v 0.1 2024-07-12 03:53:58
 */
@Data
public class UserOrganizationQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 用户（主账号或操作员）id */
    private Integer userId;

    /** 组织id */
    private Integer orgId;

    /** 创建人操作员账号id */
    private Integer creatorOperatorUserId;

    /** 修改人操作员账号id */
    private Integer modifierOperatorUserId;

    /** 扩展信息 */
    private String extInfo;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

    /**
     * 用户 id 列表
     */
    private List<Integer> userIdList;

}
