package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;
import java.util.Date;

import java.io.Serializable;
import java.util.List;

/**
 * StatsUserOperateQuery
 *
 * @version StatsUserOperateService.java v 0.1 2025-04-25 02:47:41
 */
@Data
public class StatsUserOperateQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 统计类型：DAILY/WEEKLY/MONTHLY/TOTAL */
    private String statsType;

    /** 统计日期: 格式为yyyy-MM-dd */
    private String statsDate;

    /** 用户 id */
    private Integer userId;

    /** 服装 id */
    private Integer materialId;

    /** 用户类型（主账户：MASTER  子账号：SUB） */
    private String userType;

    /** 用户出图量 */
    private Integer createCount;

    /** 图片下载量 */
    private Integer downloadCount;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date modifyTime;

    /** 扩展字段 */
    private String extInfo;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

    /** 开始时间 */
    private String startDate;

    /** 结束时间 */
    private String endDate;

    /** 父级用户id */
    private Integer parentId;

    /** 用户 id 列表 */
    private List<Integer> userIdList;
}
