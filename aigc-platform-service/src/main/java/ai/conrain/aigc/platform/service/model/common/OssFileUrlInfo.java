/**
 * 解析自 OSS/CDN 访问 URL 的结构化结果模型。
 * <p>
 * 搭配 {@code OssFileUrlParseUtil#parse(String)} 使用，承载一次 URL 解析后的全部维度信息：
 * 协议、域名、bucket、对象完整路径、目录、文件名（含/不含扩展名）、扩展名（带点）、查询串等。
 *
 * 字段约定：
 * - schema：协议，如 "http"、"https"。
 * - domain：主机名（Host）。
 * - bucketName：
 *   - 当域名以 aliyuncs.com 结尾时，从子域名前缀提取（如 aigc-platform-dev.oss-... → aigc-platform-dev）。
 *   - 当域名为 cdn.musegate.tech 时，固定为 "aigc-platform-online"。
 * - ossObjectFullName：对象完整路径（URL Path 去掉首个斜杠）。
 * - ossObjectDir：对象目录（不含文件名；若无目录则为空字符串）。
 * - ossObjectSimpleNameNoExtension：不含扩展名的文件名。
 * - fileExtensionStartingWithDot：带点号的扩展名；若无扩展名则为空字符串；若原始文件名以点结尾，则该字段为 "."。
 * - urlQuery：查询字符串（不含问号；若无查询参数则为空字符串）。
 *
 * 示例1（OSS 源站）
 * 输入：
 *   https://aigc-platform-dev.oss-cn-zhangjiakou.aliyuncs.com/202506/100003/product_106750_1750228776_5600686_0_STJNm.jpg?Expires=123&Signature=xxx
 * 输出：
 *   schema                          = "https"
 *   domain                          = "aigc-platform-dev.oss-cn-zhangjiakou.aliyuncs.com"
 *   bucketName                      = "aigc-platform-dev"
 *   ossObjectFullName               = "202506/100003/product_106750_1750228776_5600686_0_STJNm.jpg"
 *   ossObjectDir                    = "202506/100003"
 *   ossObjectSimpleNameNoExtension  = "product_106750_1750228776_5600686_0_STJNm"
 *   fileExtensionStartingWithDot    = ".jpg"
 *   urlQuery                        = "Expires=123&Signature=xxx"
 *
 * 示例2（CDN 域名）
 * 输入：
 *   https://cdn.musegate.tech/202509/5/product_4857548_1758628261_6890032_0_hIGCv.jpg
 * 输出：
 *   schema                          = "https"
 *   domain                          = "cdn.musegate.tech"
 *   bucketName                      = "aigc-platform-online"
 *   ossObjectFullName               = "202509/5/product_4857548_1758628261_6890032_0_hIGCv.jpg"
 *   ossObjectDir                    = "202509/5"
 *   ossObjectSimpleNameNoExtension  = "product_4857548_1758628261_6890032_0_hIGCv"
 *   fileExtensionStartingWithDot    = ".jpg"
 *   urlQuery                        = ""
 */
package ai.conrain.aigc.platform.service.model.common;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class OssFileUrlInfo {
    /** 协议，如 "http"、"https" */
    private String schema;
    /** 域名（Host），如 "aigc-platform-dev.oss-cn-zhangjiakou.aliyuncs.com" 或 "cdn.musegate.tech" */
    private String domain;
    /** Bucket 名称：阿里云 OSS 从子域提取；CDN 域名固定为 "aigc-platform-online" */
    private String bucketName;
    /** 对象完整路径（URL Path 去掉首个斜杠），如 "202509/5/file.jpg" */
    private String ossObjectFullName;
    /** 目录部分（不含文件名；若无目录则为空字符串），如 "202509/5" */
    private String ossObjectDir;
    /** 文件名（不含扩展名），如 "file"；若原始文件名以点结尾，则不含该末尾点 */
    private String ossObjectSimpleNameNoExtension;
    /** 扩展名（带点），如 ".jpg"；若无扩展名则为空字符串；若原始文件名以点结尾则为 "." */
    private String fileExtensionStartingWithDot;
    /** 查询字符串（不含问号；若无查询参数则为空字符串） */
    private String urlQuery;
}