package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * StatsWarningInfoQuery
 *
 * @version StatsWarningInfoService.java v 0.1 2025-05-19 04:36:33
 */
@Data
public class StatsWarningInfoQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 统计类型：DAILY/WEEKLY/MONTHLY/TOTAL */
    private String statsType;

    /** 统计日期: 格式为yyyy-MM-dd */
    private String statsDate;

    /** 周内不消耗客户 */
    private String weeklyNoConsumptionRate;

    /** 月内不消耗客户 */
    private String monthlyNoConsumptionRate;

    /** 用户退款率大于百分之 x的客户数量 */
    private Integer customerRefundRateCount;

    /** 交付超过 20 小时的服装量 */
    private Integer deliveryTimeoutCount;

    /** 客户余额预警（缪斯点小于 2000 或 小于累计充值金额为基础的30%） */
    private Integer customerBalanceAlertCount;

    /** 客户入库时间超过 60 天未转化数量 */
    private Integer customerNotConvertCount;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date modifyTime;

    /** 扩展字段 */
    private String extInfo;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

}
