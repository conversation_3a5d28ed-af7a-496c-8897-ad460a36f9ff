package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * EventTrackingRecordQuery
 *
 * @version EventTrackingRecordService.java v 0.1 2024-12-07 02:51:15
 */
@Data
public class EventTrackingRecordQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** 埋点记录主键 */
    private Integer id;

    /** 关联用户 Id（为空时则说明用户尚未登录） */
    private Integer userId;

    /** 临时uuid（唯一 UUID） */
    private String tempUserUuid;

    /** ip 地址 */
    private String ipAddress;

    /** 会话 ID，用于标记一次用户的会话操作 */
    private String sessionId;

    /** 事件类型 枚举记录 （1.进入页面 2.离开页面 3.按钮点击 4.鼠标悬停 ....） */
    private String eventType;

    /** 具体事件内容(点击登录按钮、点击发送验证码、鼠标悬停在视频上面...) */
    private String eventContent;

    /** 网页标题 */
    private String pageTitle;

    /** 当前页面的 URL */
    private String pageUrl;

    /** 用户留存时间 （单位：秒） 默认为0 */
    private Integer userRetentionTime;

    /** 操作系统 */
    private String os;

    /** 浏览器信息 */
    private String browser;

    /** 请求来源（1.PC  2.App 3.小程序 4.抖音....） */
    private String requestResource;

    /** 上一个页面的 url */
    private String preReferrer;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 存储其他额外的事件数据（JSON 格式存储） */
    private String additionalData;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

}
