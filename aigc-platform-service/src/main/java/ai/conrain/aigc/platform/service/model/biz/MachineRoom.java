/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.biz;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 机房信息
 *
 * <AUTHOR>
 * @version : MachineRoom.java, v 0.1 2025/3/8 11:09 renxiao.wu Exp $
 */
@Data
public class MachineRoom implements Serializable {
    private static final long serialVersionUID = -814550343800753192L;
    /** 机房编码 */
    private String id;
    /** 机房名称 */
    private String name;
    /** 机器列表 */
    private List<MachineInfo> machines;
}
