package ai.conrain.aigc.platform.service.component.agent;

import ai.conrain.aigc.platform.integration.ai.imageAnalysis.ImageAnalysisService;
import ai.conrain.aigc.platform.integration.ai.imageAnalysis.SearchUtil;
import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisResult;
import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisTaskResponse;
import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.service.component.AgentSessionTaskService;
import ai.conrain.aigc.platform.service.component.agent.entity.params.ClothAnalysisParams;
import ai.conrain.aigc.platform.service.component.agent.translation.TranslationService;
import ai.conrain.aigc.platform.service.enums.agent.AgentSessionStatusEnum;
import ai.conrain.aigc.platform.service.enums.agent.AgentSessionTaskTypeEnum;
import ai.conrain.aigc.platform.service.model.vo.AgentSessionTaskVO;
import com.alibaba.fastjson.JSONObject;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

import static ai.conrain.aigc.platform.service.component.agent.constants.AgentConstants.*;

/**
 * 服装分析任务处理器
 * 采用异步任务模式，第一次创建任务，后续调度查询结果
 * 使用缓存机制提高处理效率
 */
@Slf4j
@Component
public class ClothAnalysisTaskHandler implements AgentTaskHandler<ClothAnalysisParams> {

    @Autowired
    private ImageAnalysisService imageAnalysisService;

    @Autowired
    private TairService tairService;

    @Autowired
    private AgentSessionTaskService agentSessionTaskService;

    @Autowired
    private TranslationService translationService;

    // 缓存有效期：24小时
    private static final int CACHE_EXPIRE_SECONDS = 60 * 60 * 24;

    @Override
    public void handleTask(AgentSessionTaskVO agentSessionTask) {
        log.info("【agentTask】ClothAnalysisTaskHandler::handleTask::开始处理服装分析任务, agentTaskId={}", agentSessionTask.getId());

        try {
            // 1. 提取和验证参数
            ClothAnalysisParams params = extractAndValidateParameters(agentSessionTask);
            String clothUrl = params.getClothesImage();

            // 2. 检查或创建分析任务
            if (!hasExistingTasks(agentSessionTask)) {
                createAnalysisTasks(agentSessionTask, clothUrl);
                return;
            }

            // 3. 查询分析结果
            AnalysisResults results = queryAnalysisResults(agentSessionTask, clothUrl);
            if (!results.isCompleted()) {
                log.info("【agentTask】ClothAnalysisTaskHandler::handleTask::分析任务未完成，等待下次调度, taskId={}, detailCompleted={}, preCaptionCompleted={}",
                        agentSessionTask.getId(), results.isDetailCompleted(), results.isPreCaptionCompleted());
                return;
            }

            // 4. 执行翻译
            performTranslation(agentSessionTask, results);

            // 5. 转换数据结构为编辑模式
            transformToEditableStructure(agentSessionTask);

            // 6. 完成任务
            completeTask(agentSessionTask, results);

        } catch (Exception e) {
            handleTaskFailure(agentSessionTask, e);
        }
    }

    /**
     * 提取和验证任务参数
     */
    private ClothAnalysisParams extractAndValidateParameters(AgentSessionTaskVO agentSessionTask) {
        ClothAnalysisParams params = extractParameters(agentSessionTask);

        if (params == null) {
            throw new IllegalArgumentException("ClothAnalysisTaskHandler::handleTask::任务参数不完整，无法提取参数");
        }

        if (StringUtils.isBlank(params.getClothesImage())) {
            throw new IllegalArgumentException("ClothAnalysisTaskHandler::handleTask::服装图片URL为空，任务终止...");
        }

        log.info("【agentTask】ClothAnalysisTaskHandler::handleTask::提取到服装图片URL: {}", params.getClothesImage());
        return params;
    }

    /**
     * 检查是否已有现存的分析任务
     */
    private boolean hasExistingTasks(AgentSessionTaskVO agentSessionTask) {
        String analysisClothesTaskId = agentSessionTask.getStringFromExtInfo(KEY_ANALYSIS_CLOTHES_TASK_ID);
        String preLabelTaskId = agentSessionTask.getStringFromExtInfo(KEY_PRE_LABEL_TASK_ID);
        return StringUtils.isNotBlank(analysisClothesTaskId) || StringUtils.isNotBlank(preLabelTaskId);
    }

    /**
     * 创建分析任务
     */
    private void createAnalysisTasks(AgentSessionTaskVO agentSessionTask, String clothUrl) {
        log.info("【agentTask】ClothAnalysisTaskHandler::handleTask::创建分析任务, taskId={}, clothUrl={}", agentSessionTask.getId(), clothUrl);

        // 创建详细分析任务（如果还未达到重试上限）
        if (shouldCreateDetailAnalysisTask(agentSessionTask)) {
            String analysisClothesTaskId = createDetailAnalysisTask(clothUrl);
            if (StringUtils.isNotBlank(analysisClothesTaskId)) {
                agentSessionTask.addExtInfo(KEY_ANALYSIS_CLOTHES_TASK_ID, analysisClothesTaskId);
            }
        }

        // 创建预标注分析任务（如果还未达到重试上限）
        if (shouldCreatePreLabelTask(agentSessionTask)) {
            String preLabelTaskId = createPreCaptionTask(clothUrl);
            if (StringUtils.isNotBlank(preLabelTaskId)) {
                agentSessionTask.addExtInfo(KEY_PRE_LABEL_TASK_ID, preLabelTaskId);
            }
        }

        // 更新任务状态
        agentSessionTask.setStatus(AgentSessionStatusEnum.PROCESSING);

        // 更新任务信息
        agentSessionTaskService.updateByIdSelective(agentSessionTask);

        String analysisClothesTaskId = agentSessionTask.getStringFromExtInfo(KEY_ANALYSIS_CLOTHES_TASK_ID);
        String preLabelTaskId = agentSessionTask.getStringFromExtInfo(KEY_PRE_LABEL_TASK_ID);
        
        log.info("【agentTask】ClothAnalysisTaskHandler::handleTask::分析任务已创建，等待下次调度查询结果, taskId={}, analysisClothesTaskId={}, preLabelTaskId={}",
                agentSessionTask.getId(), analysisClothesTaskId, preLabelTaskId);
    }

    /**
     * 查询分析结果
     */
    private AnalysisResults queryAnalysisResults(AgentSessionTaskVO agentSessionTask, String clothUrl) {
        String analysisClothesTaskId = agentSessionTask.getStringFromExtInfo(KEY_ANALYSIS_CLOTHES_TASK_ID);
        String preLabelTaskId = agentSessionTask.getStringFromExtInfo(KEY_PRE_LABEL_TASK_ID);

        log.info("【agentTask】ClothAnalysisTaskHandler::handleTask::查询分析结果, taskId={}, analysisClothesTaskId={}, preLabelTaskId={}",
                agentSessionTask.getId(), analysisClothesTaskId, preLabelTaskId);

        ImageAnalysisResult detailResult = null;
        ImageAnalysisResult preCaptionResult = null;

        // 查询详细分析结果
        if (StringUtils.isNotBlank(analysisClothesTaskId)) {
            detailResult = queryAnalysisResultWithRetry(agentSessionTask, analysisClothesTaskId, clothUrl, true);
        }

        // 查询预标注结果
        if (StringUtils.isNotBlank(preLabelTaskId)) {
            preCaptionResult = queryAnalysisResultWithRetry(agentSessionTask, preLabelTaskId, clothUrl, false);
        }

        return new AnalysisResults(detailResult, preCaptionResult);
    }

    /**
     * 执行翻译
     */
    private void performTranslation(AgentSessionTaskVO agentSessionTask, AnalysisResults results) {
        String translationStatus = agentSessionTask.getStringFromExtInfo(KEY_TRANSLATION_STATUS);

        if (!TRANSLATION_STATUS_COMPLETED.equals(translationStatus)) {
            log.info("【agentTask】ClothAnalysisTaskHandler::handleTask::开始翻译分析结果, taskId={}", agentSessionTask.getId());

            try {
                // 标记翻译状态为进行中
                agentSessionTask.addExtInfo(KEY_TRANSLATION_STATUS, TRANSLATION_STATUS_IN_PROGRESS);
                agentSessionTaskService.updateByIdSelective(agentSessionTask);

                // 创建一个合并的结果对象用于翻译
                ImageAnalysisResult combinedResult = new ImageAnalysisResult();
                combinedResult.setAnalysis(results.getDetailResult().getAnalysis());

                // 执行翻译
                ImageAnalysisResult translatedResult = translationService.translateAnalysisResult(combinedResult);

                // 存储翻译结果
                storeTranslationResults(agentSessionTask, translatedResult, results);

                // 标记翻译完成
                agentSessionTask.addExtInfo(KEY_TRANSLATION_STATUS, TRANSLATION_STATUS_COMPLETED);

                log.info("【agentTask】ClothAnalysisTaskHandler::handleTask::翻译完成, taskId={}", agentSessionTask.getId());

            } catch (Exception e) {
                log.error("【agentTask】ClothAnalysisTaskHandler::handleTask::翻译失败，使用原始结果, taskId={}", agentSessionTask.getId(), e);
                agentSessionTask.addExtInfo(KEY_TRANSLATION_STATUS, TRANSLATION_STATUS_FAILED);
            }
        }
    }

    /**
     * 存储翻译结果
     */
    private void storeTranslationResults(AgentSessionTaskVO agentSessionTask, ImageAnalysisResult translatedResult, AnalysisResults results) {
        if (translatedResult != null && translatedResult.getAnalysis() != null) {
            // 创建详细分析的翻译结果对象
            ImageAnalysisResult translatedDetailResult = new ImageAnalysisResult();
            translatedDetailResult.setTaskId(results.getDetailResult().getTaskId());
            translatedDetailResult.setStatus(results.getDetailResult().getStatus());
            translatedDetailResult.setAnalysis(translatedResult.getAnalysis());
            agentSessionTask.addExtInfo(KEY_TRANSLATION_ANALYSIS_CLOTHES_TASK_RESULT, translatedDetailResult);
        }
    }

    /**
     * 转换数据结构为可编辑结构
     * 将analysis属性下的最底层字符串值转换为 {content: '', isUpdate: false} 的对象结构
     */
    private void transformToEditableStructure(AgentSessionTaskVO agentSessionTask) {
        log.info("【agentTask】ClothAnalysisTaskHandler::transformToEditableStructure::开始转换数据结构为编辑模式, taskId={}", agentSessionTask.getId());

        try {
            // 获取翻译后的分析结果
            Object translationResult = agentSessionTask.getExtInfo().get(KEY_TRANSLATION_ANALYSIS_CLOTHES_TASK_RESULT);
            if (translationResult instanceof ImageAnalysisResult analysisResult) {

                if (analysisResult.getAnalysis() != null) {
                    // 转换analysis对象
                    Object transformedAnalysis = transformObjectToEditableStructure(analysisResult.getAnalysis());
                    
                    // 直接封装为JSONObject存储
                    JSONObject editableResult = new JSONObject();
                    editableResult.put("taskId", analysisResult.getTaskId());
                    editableResult.put("status", analysisResult.getStatus());
                    editableResult.put("analysis", transformedAnalysis);
                    
                    // 存储可编辑结构的结果
                    agentSessionTask.addExtInfo(KEY_TRANSLATION_ANALYSIS_CLOTHES_TASK_RESULT, editableResult);
                    
                    log.info("【agentTask】ClothAnalysisTaskHandler::transformToEditableStructure::数据结构转换完成, taskId={}", agentSessionTask.getId());
                }
            }
            
            // 同时处理预标注结果（如果需要的话）
            transformPreCaptionToEditableStructure(agentSessionTask);
            
        } catch (Exception e) {
            log.error("【agentTask】ClothAnalysisTaskHandler::transformToEditableStructure::转换数据结构失败, taskId={}", agentSessionTask.getId(), e);
            // 转换失败不影响主流程，继续执行
        }
    }

    /**
     * 递归转换对象为可编辑结构
     */
    private Object transformObjectToEditableStructure(Object obj) {
        switch (obj) {
            case null -> {
                return null;
            }
            case JSONObject jsonObj -> {
                JSONObject result = new JSONObject();

                for (String key : jsonObj.keySet()) {
                    Object value = jsonObj.get(key);
                    result.put(key, transformObjectToEditableStructure(value));
                }

                return result;
            }
            case String s -> {
                // 将字符串转换为可编辑结构
                JSONObject editableField = new JSONObject();
                editableField.put("content", obj);
                editableField.put("isUpdate", false);
                return editableField;
            }
            default -> {
                // 对于其他复杂对象，先转换为JSONObject再处理
                try {
                    // 将POJO对象转换为JSONObject
                    JSONObject jsonObj = JSONObject.parseObject(JSONObject.toJSONString(obj));
                    return transformObjectToEditableStructure(jsonObj);
                } catch (Exception e) {
                    log.warn("【agentTask】transformObjectToEditableStructure::无法转换对象类型: {}, 保持原值", obj.getClass().getSimpleName());
                    // 如果转换失败，保持原值
                    return obj;
                }
            }
        }

    }

    /**
     * 转换预标注结果为可编辑结构（如果需要）
     */
    private void transformPreCaptionToEditableStructure(AgentSessionTaskVO agentSessionTask) {
        try {
            // 获取预标注结果
            Object preLabelResult = agentSessionTask.getExtInfo().get(KEY_PRE_LABEL_TASK_RESULT);
            if (preLabelResult instanceof ImageAnalysisResult result) {
                
                if (result.getPreCaption() != null) {
                    // 转换预标注对象
                    Object transformedPreCaption = transformObjectToEditableStructure(result.getPreCaption());
                    
                    // 直接封装为JSONObject存储
                    JSONObject editableResult = new JSONObject();
                    editableResult.put("taskId", result.getTaskId());
                    editableResult.put("status", result.getStatus());
                    editableResult.put("preCaption", transformedPreCaption);
                    
                    // 存储可编辑结构的预标注结果
                    agentSessionTask.addExtInfo(KEY_EDITABLE_PRE_LABEL_TASK_RESULT, editableResult);
                    
                    log.info("【agentTask】ClothAnalysisTaskHandler::transformPreCaptionToEditableStructure::预标注数据结构转换完成, taskId={}", agentSessionTask.getId());
                }
            }
        } catch (Exception e) {
            log.error("【agentTask】ClothAnalysisTaskHandler::transformPreCaptionToEditableStructure::转换预标注数据结构失败, taskId={}", agentSessionTask.getId(), e);
        }
    }

    /**
     * 完成任务
     */
    private void completeTask(AgentSessionTaskVO agentSessionTask, AnalysisResults results) {
        // 存储原始结果（只存储非空的结果）
        if (results.getDetailResult() != null) {
            agentSessionTask.addExtInfo(KEY_ANALYSIS_CLOTHES_TASK_RESULT, results.getDetailResult());
        }
        if (results.getPreCaptionResult() != null) {
            agentSessionTask.addExtInfo(KEY_PRE_LABEL_TASK_RESULT, results.getPreCaptionResult());
        }

        // 执行任务更新操作
        agentSessionTask.setStatus(AgentSessionStatusEnum.FINISHED);
        agentSessionTaskService.updateByIdSelective(agentSessionTask);

        log.info("【agentTask】ClothAnalysisTaskHandler::handleTask::服装分析任务完成, taskId={}, hasDetailResult={}, hasPreCaption={}",
                agentSessionTask.getId(),
                results.getDetailResult() != null && results.getDetailResult().getAnalysis() != null,
                results.getPreCaptionResult() != null && results.getPreCaptionResult().getPreCaption() != null);
    }

    /**
     * 处理任务失败
     */
    private void handleTaskFailure(AgentSessionTaskVO agentSessionTask, Exception e) {
        log.error("【agentTask】ClothAnalysisTaskHandler::handleTask::服装分析任务失败, taskId={}", agentSessionTask.getId(), e);

        // 构建错误信息
        JSONObject errorResult = new JSONObject();
        errorResult.put("error", true);
        errorResult.put("errorMessage", e.getMessage());
        errorResult.put("timestamp", new Date());

        agentSessionTask.addExtInfo(KEY_ERROR_RESULT, errorResult);

        // 执行任务更新操作
        agentSessionTask.setStatus(AgentSessionStatusEnum.FAILED);
        agentSessionTaskService.updateByIdSelective(agentSessionTask);
    }

    /**
     * 分析结果封装类
     */
    @Getter
    private static class AnalysisResults {
        private final ImageAnalysisResult detailResult;
        private final ImageAnalysisResult preCaptionResult;
        private final boolean detailCompleted;
        private final boolean preCaptionCompleted;

        public AnalysisResults(ImageAnalysisResult detailResult, ImageAnalysisResult preCaptionResult) {
            this.detailResult = detailResult;
            this.preCaptionResult = preCaptionResult;
            this.detailCompleted = detailResult != null &&
                    ("completed".equals(detailResult.getStatus()) || "failed".equals(detailResult.getStatus()));
            this.preCaptionCompleted = preCaptionResult != null &&
                    ("completed".equals(preCaptionResult.getStatus()) || "failed".equals(preCaptionResult.getStatus()));
        }

        public boolean isCompleted() {
            // 只有当两个任务都完成（或者其中一个彻底失败）才认为整体完成
            return detailCompleted && preCaptionCompleted;
        }
    }

    /**
     * 创建详细分析任务
     */
    private String createDetailAnalysisTask(String clothUrl) {
        return createAnalysisTask(clothUrl, "详细分析", () -> {
            // 检查缓存
            String cacheKey = DigestUtils.md5Hex(clothUrl);
            ImageAnalysisResult cachedResult = tairService.getObject(cacheKey, ImageAnalysisResult.class);

            if (isCachedResultValid(cachedResult)) {
                log.info("【agentTask】从缓存获取详细分析结果，无需创建任务, cacheKey={}", cacheKey);
                return "cached_" + cacheKey;
            }

            // 创建新任务
            ImageAnalysisTaskResponse task = imageAnalysisService.createAnalysisTask(
                    clothUrl, "gemini", true, true, null, false, false);
            return task.getTaskId();
        });
    }

    /**
     * 创建预标注任务
     */
    private String createPreCaptionTask(String clothUrl) {
        return createAnalysisTask(clothUrl, "预标注", () -> {
            // 检查缓存
            String cacheKey = DigestUtils.md5Hex(clothUrl + "pre");
            ImageAnalysisResult cachedResult = tairService.getObject(cacheKey, ImageAnalysisResult.class);

            if (cachedResult != null && cachedResult.getPreCaption() != null) {
                log.info("【agentTask】从缓存获取预标注结果，无需创建任务, cacheKey={}", cacheKey);
                return "cached_" + cacheKey;
            }

            // 创建新任务
            ImageAnalysisTaskResponse task = imageAnalysisService.createSimpleCaptionTask(clothUrl, true);
            return task.getTaskId();
        });
    }

    /**
     * 通用任务创建模板方法
     */
    private String createAnalysisTask(String clothUrl, String taskType, TaskCreator taskCreator) {
        try {
            String taskId = taskCreator.create();
            log.info("【agentTask】{}任务已创建, taskId={}", taskType, taskId);
            return taskId;
        } catch (Exception e) {
            log.error("【agentTask】创建{}任务失败, clothUrl={}", taskType, clothUrl, e);
            return null;
        }
    }

    /**
     * 检查缓存结果是否有效
     */
    private boolean isCachedResultValid(ImageAnalysisResult cachedResult) {
        return cachedResult != null
                && cachedResult.getAnalysis() != null
                && SearchUtil.isValidImageAnalysisCloth(cachedResult.getAnalysis());
    }

    /**
     * 任务创建器接口
     */
    @FunctionalInterface
    private interface TaskCreator {
        String create() throws Exception;
    }

    /**
     * 查询详细分析结果
     */
    private ImageAnalysisResult queryAnalysisResult(String taskId, String clothUrl) {
        return queryResult(taskId, clothUrl, "分析", clothUrl, (result, key) -> {
            // 详细分析结果的缓存逻辑
            if (result != null && "completed".equals(result.getStatus()) && result.getAnalysis() != null) {
                tairService.setObject(key, result, CACHE_EXPIRE_SECONDS);
                return true;
            }
            return false;
        });
    }

    /**
     * 查询预标注结果
     */
    private ImageAnalysisResult queryPreCaptionResult(String taskId, String clothUrl) {
        return queryResult(taskId, clothUrl, "预标注", clothUrl + "pre", (result, key) -> {
            // 预标注结果的缓存逻辑
            if (result != null && "completed".equals(result.getStatus())) {
                tairService.setObject(key, result, CACHE_EXPIRE_SECONDS);
                return true;
            }
            return false;
        });
    }

    /**
     * 通用结果查询模板方法
     */
    private ImageAnalysisResult queryResult(String taskId, String clothUrl, String resultType,
                                            String cacheKeySuffix, CacheStrategy cacheStrategy) {
        try {
            // 处理缓存标识
            if (taskId.startsWith("cached_")) {
                return getCachedResult(taskId);
            }

            // 查询分析结果
            ImageAnalysisResult result = imageAnalysisService.getAnalysisResult(taskId);

            // 应用缓存策略
            if (cacheStrategy.shouldCache(result, DigestUtils.md5Hex(cacheKeySuffix))) {
                log.info("【agentTask】{}结果已缓存, taskId={}", resultType, taskId);
            }

            return result;

        } catch (Exception e) {
            log.error("【agentTask】查询{}结果失败, taskId={}", resultType, taskId, e);
            return null;
        }
    }

    /**
     * 获取缓存结果
     */
    private ImageAnalysisResult getCachedResult(String taskId) {
        String cacheKey = taskId.substring(7);
        ImageAnalysisResult cachedResult = tairService.getObject(cacheKey, ImageAnalysisResult.class);
        if (cachedResult != null) {
            cachedResult.setStatus("completed");
        }
        return cachedResult;
    }

    /**
     * 缓存策略接口
     */
    @FunctionalInterface
    private interface CacheStrategy {
        boolean shouldCache(ImageAnalysisResult result, String cacheKey);
    }


    @Override
    public String getSupportedTaskType() {
        return AgentSessionTaskTypeEnum.CLOTH_ANALYSIS.getCode();
    }

    @Override
    public Class<ClothAnalysisParams> getParameterType() {
        return ClothAnalysisParams.class;
    }

    /**
     * 工具方法：如果值不为空则添加到结果中
     */
    private void addIfNotBlank(JSONObject resultInfo, String key, String value) {
        if (StringUtils.isNotBlank(value)) {
            resultInfo.put(key, value);
        }
    }

    /**
     * 处理字段修改并生成最终结果
     * @param agentSessionTask 代理会话任务
     * @param modifiedData 修改后的数据
     */
    public void processFieldModifications(AgentSessionTaskVO agentSessionTask, JSONObject modifiedData) {
        log.info("【agentTask】开始处理字段修改，taskId={}", agentSessionTask.getId());
        
        try {
            // 1. 获取原始数据
            JSONObject originalData = getOriginalCombinedData(agentSessionTask);
            if (originalData == null) {
                log.warn("【agentTask】未找到原始数据，无法进行字段修改对比，taskId={}", agentSessionTask.getId());
                return;
            }

            // 2. 使用GPT直接生成带状态标识和翻译的结果
            JSONObject resultWithStatusAndTranslation = translationService.generateResultWithStatusAndTranslation(originalData, modifiedData);

            // 3. 生成最终版本结果
            JSONObject finalResult = generateSimplifiedFinalResult(resultWithStatusAndTranslation);

            // 4. 存储结果
            storeSimplifiedResults(agentSessionTask, resultWithStatusAndTranslation, finalResult);

            log.info("【agentTask】字段修改处理完成，taskId={}", agentSessionTask.getId());

        } catch (Exception e) {
            log.error("【agentTask】处理字段修改失败，taskId={}", agentSessionTask.getId(), e);
            throw new RuntimeException("处理字段修改失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取原始合并数据
     */
    private JSONObject getOriginalCombinedData(AgentSessionTaskVO agentSessionTask) {
        JSONObject combinedData = new JSONObject();
        
        // 获取预标注结果
        Object preLabelResult = agentSessionTask.getExtInfo().get(KEY_PRE_LABEL_TASK_RESULT);
        if (preLabelResult instanceof ImageAnalysisResult) {
            ImageAnalysisResult result = (ImageAnalysisResult) preLabelResult;
            if (result.getPreCaption() != null) {
                combinedData.put("preCaption", result.getPreCaption());
            }
        }

        // 获取详细分析结果
        Object analysisResult = agentSessionTask.getExtInfo().get(KEY_TRANSLATION_ANALYSIS_CLOTHES_TASK_RESULT);
        if (analysisResult instanceof ImageAnalysisResult) {
            ImageAnalysisResult result = (ImageAnalysisResult) analysisResult;
            if (result.getAnalysis() != null) {
                combinedData.put("analysis", result.getAnalysis());
            }
        }

        return combinedData.isEmpty() ? null : combinedData;
    }

    /**
     * 生成简化的最终版本结果
     */
    private JSONObject generateSimplifiedFinalResult(JSONObject resultWithStatusAndTranslation) {
        JSONObject finalResult = new JSONObject();
        
        // 复制GPT生成的完整结果
        finalResult.put("resultWithStatusAndTranslation", resultWithStatusAndTranslation);
        
        // 提取修改的字段统计信息
        int modifiedCount = countModifiedFields(resultWithStatusAndTranslation);
        
        // 添加元数据
        JSONObject metadata = new JSONObject();
        metadata.put("lastModifiedTime", new Date());
        metadata.put("modifiedFieldsCount", modifiedCount);
        metadata.put("processingMethod", "gpt-direct");
        finalResult.put("metadata", metadata);
        
        return finalResult;
    }

    /**
     * 统计修改字段的数量
     */
    private int countModifiedFields(JSONObject data) {
        return countModifiedFieldsRecursive(data);
    }

    /**
     * 递归统计修改字段的数量
     */
    private int countModifiedFieldsRecursive(Object obj) {
        int count = 0;
        
        if (obj instanceof JSONObject) {
            JSONObject jsonObj = (JSONObject) obj;
            
            // 检查是否是字段对象（包含value和status）
            if (jsonObj.containsKey("value") && jsonObj.containsKey("status")) {
                String status = jsonObj.getString("status");
                if ("modified".equals(status)) {
                    count = 1;
                }
            } else {
                // 递归检查子对象
                for (String key : jsonObj.keySet()) {
                    Object value = jsonObj.get(key);
                    count += countModifiedFieldsRecursive(value);
                }
            }
        }
        
        return count;
    }

    /**
     * 存储简化的结果
     */
    private void storeSimplifiedResults(AgentSessionTaskVO agentSessionTask, 
                                      JSONObject resultWithStatusAndTranslation, 
                                      JSONObject finalResult) {
        
        // 存储带状态标识的结果到原有字段（保持兼容性）
        agentSessionTask.addExtInfo(KEY_PRE_LABEL_TASK_RESULT_WITH_STATUS, 
                extractPreLabelFromGptResult(resultWithStatusAndTranslation));
        agentSessionTask.addExtInfo(KEY_ANALYSIS_CLOTHES_TASK_RESULT_WITH_STATUS, 
                extractAnalysisFromGptResult(resultWithStatusAndTranslation));
        
        // 存储最终版本结果
        agentSessionTask.addExtInfo(KEY_FINAL_RESULT, finalResult);
        
        // 存储字段修改记录（从GPT结果中提取）
        JSONObject modificationRecord = extractModificationRecord(resultWithStatusAndTranslation);
        agentSessionTask.addExtInfo(KEY_FIELD_MODIFICATION_RECORD, modificationRecord);
        
        // 更新任务
        agentSessionTaskService.updateByIdSelective(agentSessionTask);
        
        log.info("【agentTask】最终结果存储完成，taskId={}", agentSessionTask.getId());
    }

    /**
     * 从GPT结果中提取预标注部分
     */
    private JSONObject extractPreLabelFromGptResult(JSONObject gptResult) {
        JSONObject preLabelPart = new JSONObject();
        preLabelPart.put("status", "completed");
        preLabelPart.put("taskId", "gpt_processed_task_id");
        
        Object preCaption = gptResult.get("preCaption");
        if (preCaption != null) {
            preLabelPart.put("preCaption", preCaption);
        }
        
        return preLabelPart;
    }

    /**
     * 从GPT结果中提取分析部分
     */
    private JSONObject extractAnalysisFromGptResult(JSONObject gptResult) {
        JSONObject analysisPart = new JSONObject();
        analysisPart.put("status", "completed");
        analysisPart.put("taskId", "gpt_processed_task_id");
        
        Object analysis = gptResult.get("analysis");
        if (analysis != null) {
            analysisPart.put("analysis", analysis);
        }
        
        return analysisPart;
    }

    /**
     * 从GPT结果中提取修改记录
     */
    private JSONObject extractModificationRecord(JSONObject gptResult) {
        JSONObject modificationRecord = new JSONObject();
        
        // 提取所有修改的字段
        Map<String, Object> modifiedFields = extractModifiedFieldsFromGptResult("", gptResult);
        
        modificationRecord.put("modifiedFields", new JSONObject(modifiedFields));
        modificationRecord.put("modificationTime", new Date());
        modificationRecord.put("modificationCount", modifiedFields.size());
        modificationRecord.put("processingMethod", "gpt-direct");
        
        return modificationRecord;
    }

    /**
     * 从GPT结果中递归提取修改的字段
     */
    private Map<String, Object> extractModifiedFieldsFromGptResult(String prefix, Object obj) {
        Map<String, Object> modifiedFields = new HashMap<>();
        
        if (obj instanceof JSONObject) {
            JSONObject jsonObj = (JSONObject) obj;
            
            // 检查是否是字段对象
            if (jsonObj.containsKey("value") && jsonObj.containsKey("status")) {
                String status = jsonObj.getString("status");
                if ("modified".equals(status)) {
                    Object value = jsonObj.get("value");
                    modifiedFields.put(prefix, value);
                }
            } else {
                // 递归检查子对象
                for (String key : jsonObj.keySet()) {
                    String fieldPath = prefix.isEmpty() ? key : prefix + "." + key;
                    Object value = jsonObj.get(key);
                    modifiedFields.putAll(extractModifiedFieldsFromGptResult(fieldPath, value));
                }
            }
        }
        
        return modifiedFields;
    }

    /**
     * 带重试机制的分析结果查询
     */
    private ImageAnalysisResult queryAnalysisResultWithRetry(AgentSessionTaskVO agentSessionTask, String taskId, String clothUrl, boolean isDetailAnalysis) {
        try {
            // 检查是否为缓存任务
            if (taskId.startsWith("cached_")) {
                return getCachedResult(taskId);
            }

            // 查询分析结果
            ImageAnalysisResult result = imageAnalysisService.getAnalysisResult(taskId);
            
            // 检查结果状态
            if (result != null && "failed".equals(result.getStatus())) {
                log.warn("【agentTask】任务失败，准备重试, taskId={}, analysisTaskId={}, isDetailAnalysis={}", 
                        agentSessionTask.getId(), taskId, isDetailAnalysis);
                
                // 处理任务失败，触发重试
                handleTaskFailureAndRetry(agentSessionTask, taskId, clothUrl, isDetailAnalysis);
                return null; // 返回null表示需要重新创建任务
            }

            // 如果任务完成，缓存结果
            if (result != null && "completed".equals(result.getStatus())) {
                cacheCompletedResult(result, clothUrl, isDetailAnalysis);
            }
            
            return result;
        } catch (Exception e) {
            log.error("【agentTask】查询分析结果失败, taskId={}, analysisTaskId={}", agentSessionTask.getId(), taskId, e);
            return null;
        }
    }

    /**
     * 处理任务失败并进行重试
     */
    private void handleTaskFailureAndRetry(AgentSessionTaskVO agentSessionTask, String failedTaskId, String clothUrl, boolean isDetailAnalysis) {
        String retryCountKey = isDetailAnalysis ? KEY_ANALYSIS_CLOTHES_RETRY_COUNT : KEY_PRE_LABEL_RETRY_COUNT;
        String failedTaskListKey = isDetailAnalysis ? KEY_FAILED_ANALYSIS_CLOTHES_TASK_ID_LIST : KEY_FAILED_PRE_LABEL_TASK_ID_LIST;
        String currentTaskIdKey = isDetailAnalysis ? KEY_ANALYSIS_CLOTHES_TASK_ID : KEY_PRE_LABEL_TASK_ID;

        // 获取当前重试次数
        Integer currentRetryCount = agentSessionTask.getIntegerFromExtInfo(retryCountKey);
        if (currentRetryCount == null) {
            currentRetryCount = 0;
        }

        // 添加失败的任务ID到失败列表
        List<String> failedTaskIds = getFailedTaskIdList(agentSessionTask, failedTaskListKey);
        if (!failedTaskIds.contains(failedTaskId)) {
            failedTaskIds.add(failedTaskId);
            agentSessionTask.addExtInfo(failedTaskListKey, failedTaskIds);
        }

        // 增加重试次数
        currentRetryCount++;
        agentSessionTask.addExtInfo(retryCountKey, currentRetryCount);

        // 检查是否超过重试上限
        if (currentRetryCount >= MAX_RETRY_COUNT) {
            String taskType = isDetailAnalysis ? "详细分析" : "预标注";
            log.error("【agentTask】{}任务重试次数已达上限，任务失败, taskId={}, retryCount={}", 
                    taskType, agentSessionTask.getId(), currentRetryCount);
            
            // 直接抛出异常，让外层处理任务失败
            throw new RuntimeException(taskType + "任务重试次数已达上限，任务失败");
        } else {
            log.info("【agentTask】准备重试任务, taskId={}, isDetailAnalysis={}, retryCount={}/{}", 
                    agentSessionTask.getId(), isDetailAnalysis, currentRetryCount, MAX_RETRY_COUNT);
            
            // 移除当前任务ID，以便重新创建
            agentSessionTask.getExtInfo().remove(currentTaskIdKey);
        }

        // 更新任务信息
        agentSessionTaskService.updateByIdSelective(agentSessionTask);
    }

    /**
     * 获取失败任务ID列表
     */
    @SuppressWarnings("unchecked")
    private List<String> getFailedTaskIdList(AgentSessionTaskVO agentSessionTask, String key) {
        Object failedListObj = agentSessionTask.getExtInfo().get(key);
        List<String> failedTaskIds;
        
        if (failedListObj instanceof List) {
            failedTaskIds = (List<String>) failedListObj;
        } else {
            failedTaskIds = new ArrayList<>();
        }
        
        return failedTaskIds;
    }

    /**
     * 缓存已完成的结果
     */
    private void cacheCompletedResult(ImageAnalysisResult result, String clothUrl, boolean isDetailAnalysis) {
        try {
            String cacheKey;
            if (isDetailAnalysis) {
                cacheKey = DigestUtils.md5Hex(clothUrl);
                if (result.getAnalysis() != null && SearchUtil.isValidImageAnalysisCloth(result.getAnalysis())) {
                    tairService.setObject(cacheKey, result, CACHE_EXPIRE_SECONDS);
                    log.info("【agentTask】详细分析结果已缓存, cacheKey={}", cacheKey);
                }
            } else {
                cacheKey = DigestUtils.md5Hex(clothUrl + "pre");
                if (result.getPreCaption() != null) {
                    tairService.setObject(cacheKey, result, CACHE_EXPIRE_SECONDS);
                    log.info("【agentTask】预标注结果已缓存, cacheKey={}", cacheKey);
                }
            }
        } catch (Exception e) {
            log.warn("【agentTask】缓存结果失败", e);
        }
    }

    /**
     * 判断是否应该创建详细分析任务
     */
    private boolean shouldCreateDetailAnalysisTask(AgentSessionTaskVO agentSessionTask) {
        // 检查是否已有任务ID
        String existingTaskId = agentSessionTask.getStringFromExtInfo(KEY_ANALYSIS_CLOTHES_TASK_ID);
        if (StringUtils.isNotBlank(existingTaskId)) {
            return false;
        }

        // 检查重试次数是否超限
        Integer retryCount = agentSessionTask.getIntegerFromExtInfo(KEY_ANALYSIS_CLOTHES_RETRY_COUNT);
        if (retryCount != null && retryCount >= MAX_RETRY_COUNT) {
            log.warn("【agentTask】详细分析任务重试次数已达上限，不再创建新任务, taskId={}, retryCount={}", 
                    agentSessionTask.getId(), retryCount);
            return false;
        }

        return true;
    }

    /**
     * 判断是否应该创建预标注任务
     */
    private boolean shouldCreatePreLabelTask(AgentSessionTaskVO agentSessionTask) {
        // 检查是否已有任务ID
        String existingTaskId = agentSessionTask.getStringFromExtInfo(KEY_PRE_LABEL_TASK_ID);
        if (StringUtils.isNotBlank(existingTaskId)) {
            return false;
        }

        // 检查重试次数是否超限
        Integer retryCount = agentSessionTask.getIntegerFromExtInfo(KEY_PRE_LABEL_RETRY_COUNT);
        if (retryCount != null && retryCount >= MAX_RETRY_COUNT) {
            log.warn("【agentTask】预标注任务重试次数已达上限，不再创建新任务, taskId={}, retryCount={}", 
                    agentSessionTask.getId(), retryCount);
            return false;
        }

        return true;
    }
}