package ai.conrain.aigc.platform.service.config;

import ai.conrain.aigc.platform.service.component.ImageCaptionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 缓存初始化配置类
 * 负责在应用启动后异步初始化各种缓存
 */
@Slf4j
@Component
public class CacheInitializationConfig {

    @Autowired
    private ImageCaptionService imageCaptionService;

    /**
     * 应用启动完成后异步初始化ImageCaption缓存
     */
    @EventListener(ApplicationReadyEvent.class)
    @Async
    public void initImageCaptionCache(ApplicationReadyEvent event) {
        log.info("应用启动完成，开始初始化ImageCaption缓存");
        try {
            imageCaptionService.initCacheOnStartup();
            log.info("ImageCaption缓存初始化任务已启动");
        } catch (Exception e) {
            log.error("ImageCaption缓存初始化失败", e);
        }
    }
}