package ai.conrain.aigc.platform.service.component.agent;

import ai.conrain.aigc.platform.service.component.agent.entity.params.DefaultParams;
import ai.conrain.aigc.platform.service.enums.agent.AgentSessionTaskTypeEnum;
import ai.conrain.aigc.platform.service.model.vo.AgentSessionTaskVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 推荐模特信息任务处理器
 */
@Slf4j
@Component
public class RecommendedFaceInfoTaskHandler implements AgentTaskHandler<DefaultParams> {
    
    @Override
    public void handleTask(AgentSessionTaskVO agentSessionTask) {
        log.info("【agentTask】RecommendedFaceInfoTaskHandler::handleTask::开始处理推荐模特任务,  agentTaskId={}", agentSessionTask.getId());

        // TODO: 实现具体的服装分析逻辑

        log.info("【agentTask】RecommendedFaceInfoTaskHandler::handleTask::推荐模特信息任务完成,  agentTaskId={}", agentSessionTask.getId());
    }
    
    @Override
    public String getSupportedTaskType() {
        return AgentSessionTaskTypeEnum.RECOMMENDED_FACE_INFO.getCode();
    }
    
    @Override
    public Class<DefaultParams> getParameterType() {
        return DefaultParams.class;
    }
}