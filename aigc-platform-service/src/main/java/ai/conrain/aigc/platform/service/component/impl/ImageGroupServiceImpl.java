package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.example.ImageExample;
import ai.conrain.aigc.platform.dal.pgsql.dao.ImageDAO;
import ai.conrain.aigc.platform.dal.pgsql.entity.ImageDO;
import ai.conrain.aigc.platform.service.model.converter.ImageConverter;
import ai.conrain.aigc.platform.service.model.query.ImageQuery;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;

import ai.conrain.aigc.platform.dal.example.ImageGroupExample;
import ai.conrain.aigc.platform.dal.pgsql.dao.ImageGroupDAO;
import ai.conrain.aigc.platform.dal.pgsql.entity.ImageGroupCaptionDO;
import ai.conrain.aigc.platform.dal.pgsql.entity.ImageGroupDO;
import ai.conrain.aigc.platform.integration.ai.model.QueueResult;
import ai.conrain.aigc.platform.service.component.CaptionAttributeService;
import ai.conrain.aigc.platform.service.component.CaptionUserService;
import ai.conrain.aigc.platform.service.component.ImageCaptionUserService;
import ai.conrain.aigc.platform.service.component.ImageGroupCaptionService;
import ai.conrain.aigc.platform.service.component.ImageGroupCaptionUserService;
import ai.conrain.aigc.platform.service.component.ImageGroupService;
import ai.conrain.aigc.platform.service.component.ImageService;
import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.enums.ImageTypeEnum;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.ImageGroupConverter;
import ai.conrain.aigc.platform.service.model.query.CaptionAttributeQuery;
import ai.conrain.aigc.platform.service.model.query.ImageCaptionUserQuery;
import ai.conrain.aigc.platform.service.model.query.ImageGroupExportReq;
import ai.conrain.aigc.platform.service.model.query.ImageGroupExportResponse;
import ai.conrain.aigc.platform.service.model.query.ImageGroupQuery;
import ai.conrain.aigc.platform.service.model.vo.CaptionAttributeVO;
import ai.conrain.aigc.platform.service.model.vo.CaptionUserVO;
import ai.conrain.aigc.platform.service.model.vo.ClothInfoVO;
import ai.conrain.aigc.platform.service.model.vo.ImageCaptionUserVO;
import ai.conrain.aigc.platform.service.model.vo.ImageGroupVO;
import ai.conrain.aigc.platform.service.model.vo.ImageVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.MapConvertUtil;
import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**   
 * ImageGroupService实现
 *
 * <AUTHOR>
 * @version ImageGroupService.java v 0.1 2025-07-30 08:19:30
 */
@Slf4j
@Service
public class ImageGroupServiceImpl implements ImageGroupService {

	/** DAO */
	@Autowired
	private ImageGroupDAO imageGroupDAO;

    @Autowired
    private ImageService imageService;

    @Autowired
    private ImageDAO imageDAO;

    @Autowired
    private ImageCaptionUserService imageCaptionUserService;

    @Autowired
    private MaterialModelService materialModelService;

    @Autowired
    private ImageGroupCaptionService imageGroupCaptionService;

    @Autowired
    private ImageGroupCaptionUserService imageGroupCaptionUserService;

    @Autowired
    private CaptionUserService captionUserService;

    @Autowired
    private CaptionAttributeService captionAttributeService;

	@Override
	public ImageGroupVO selectById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		ImageGroupDO data = imageGroupDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
		if (null == data) {
			return null;
		}

		return ImageGroupConverter.do2VO(data);
	}

	@Override
	public void deleteById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		int n = imageGroupDAO.logicalDeleteByPrimaryKey(id);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除ImageGroup失败");
	}

	@Override
	public ImageGroupVO insert(ImageGroupVO imageGroup) {
		AssertUtil.assertNotNull(imageGroup, ResultCode.PARAM_INVALID, "imageGroup is null");
		AssertUtil.assertTrue(imageGroup.getId() == null, ResultCode.PARAM_INVALID, "imageGroup.id is present");

		//创建时间、修改时间兜底
		if (imageGroup.getCreateTime() == null) {
			imageGroup.setCreateTime(new Date());
		}

		if (imageGroup.getModifyTime() == null) {
			imageGroup.setModifyTime(new Date());
		}

		ImageGroupDO data = ImageGroupConverter.vo2DO(imageGroup);
		//逻辑删除字段初始化（虽然一般表字段有默认值）
		data.setDeleted(false);
		Integer n = imageGroupDAO.insert(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建ImageGroup失败");
		AssertUtil.assertNotNull(data.getId(), "新建ImageGroup返回id为空");
		imageGroup.setId(data.getId());
		return imageGroup;
	}


	@Override
	public void updateByIdSelective(ImageGroupVO imageGroup) {
		AssertUtil.assertNotNull(imageGroup, ResultCode.PARAM_INVALID, "imageGroup is null");
    	AssertUtil.assertTrue(imageGroup.getId() != null, ResultCode.PARAM_INVALID, "imageGroup.id is null");

		//修改时间必须更新
		imageGroup.setModifyTime(new Date());
		ImageGroupDO data = ImageGroupConverter.vo2DO(imageGroup);
		//逻辑删除标过滤
		data.setDeleted(false);
		int n = imageGroupDAO.updateByPrimaryKeySelective(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新ImageGroup失败，影响行数:" + n);
	}

	@Override
	public List<ImageGroupVO> queryImageGroupList(ImageGroupQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		ImageGroupExample example = ImageGroupConverter.query2Example(query);

		List<ImageGroupDO> list = imageGroupDAO.selectByExample(example);
		return ImageGroupConverter.doList2VOList(list);
	}

	@Override
	public Long queryImageGroupCount(ImageGroupQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		ImageGroupExample example = ImageGroupConverter.query2Example(query);
		return imageGroupDAO.countByExample(example);
	}

	/**
	 * 带条件分页查询图像组，由多种图组成的pair对
	 */
	@Override
	public PageInfo<ImageGroupVO> queryImageGroupByPage(ImageGroupQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
			&& query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
			+ query.getPageNum() + ",pageSize:" + query.getPageSize());

		PageInfo<ImageGroupVO> page = new PageInfo<>();

		ImageGroupExample example = ImageGroupConverter.query2Example(query);
		long totalCount = imageGroupDAO.countByExample(example);
		if (totalCount == 0) {
			page.setList(new ArrayList<>());
			page.setSize(0);
			page.setTotalCount(0);
			page.setHasNextPage(false);

			return page;
		}

		List<ImageGroupDO> list = imageGroupDAO.selectByExample(example);
        List<ImageGroupVO> imageGroupVOS = ImageGroupConverter.doList2VOList(list);
        List<Integer> imageIds = imageGroupVOS.stream()
            .map(ImageGroupVO::getImageIds)
            .flatMap(Collection::stream)
            .distinct()
            .toList();
        ImageQuery imageQuery = new ImageQuery();
        imageQuery.setIds(imageIds);
        List<ImageVO> images = imageService.queryImageList(imageQuery);
        for (ImageGroupVO imageGroupVO : imageGroupVOS) {
            List<ImageVO> imageVOS = new ArrayList<>();
            imageGroupVO.getImageIds().forEach(id -> images.stream()
                    .filter(image -> image.getId().equals(id))
                    .forEach(imageVOS::add));
            imageGroupVO.setImages(imageVOS);
        }
        page.setList(imageGroupVOS);
		page.setSize(CollectionUtils.size(list));
		page.setTotalCount(totalCount);
		page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

		return page;
	}

    @Override
    public void updateByClothId(Integer imageGroupId, Integer clothId) {
        ImageGroupVO imageGroupVO = selectById(imageGroupId);
        AssertUtil.assertNotNull(imageGroupVO, ResultCode.PARAM_INVALID, "imageGroup is null");
        ClothInfoVO clothInfo = getClothInfoById(clothId);
        Integer imageId = imageService.saveClothInfo(clothInfo);
        List<Integer> imageIds = imageGroupVO.getImageIds();
        if (imageIds.size() > 1) {
            imageIds.set(1, imageId);
            ImageGroupQuery imageGroupQuery = new ImageGroupQuery();
            imageGroupQuery.setImageIds(imageIds);
            List<ImageGroupDO> existingImageGroups = imageGroupDAO.selectByExample(
                ImageGroupConverter.query2Example(imageGroupQuery));
            if (!existingImageGroups.isEmpty()) {
                throw new BizException(ResultCode.DUPLICATE_CLOTH_CONFIGURATION);
            }
        }
        imageIds.add(imageId);
        imageGroupVO.setModifyTime(new Date());
        updateByIdSelective(imageGroupVO);
    }

    @Override
    public ClothInfoVO getClothInfoById(@JsonArg @NotNull Integer id) {
        MaterialModelVO materialModelVO = materialModelService.selectById(id);
        AssertUtil.assertNotNull(materialModelVO, ResultCode.MATERIAL_NOT_FOUND, "未找到素材");

        if (materialModelVO.getClothLoraTrainDetail() == null
            || materialModelVO.getClothLoraTrainDetail().getLabel() == null
            || materialModelVO.getClothLoraTrainDetail().getLabel().getStatus() != QueueResult.QueueCodeEnum.COMPLETED) {
            throw new BizException(ResultCode.MATERIAL_NOT_TAGGED, "素材没有完成打标");
        }

        ClothInfoVO c = new ClothInfoVO();
        c.setId(materialModelVO.getId());
        c.setName(materialModelVO.getName());
        c.setFullBodyFrontViewImageUrl(materialModelService.queryDetailShowImage(id));
        c.setClothType(materialModelVO.getClothType());
        c.setClothTypeDesc(materialModelVO.getClothTypeDesc());

        return c;
    }

    @Override
    public List<ImageGroupExportResponse> export(ImageGroupExportReq query) {
        List<ImageGroupCaptionDO> imageGroupCaptionDOS = new ArrayList<>();
        imageGroupCaptionDOS.addAll(imageGroupDAO.selectAllByTotalScoreSame(query.getTag()));
        imageGroupCaptionDOS.addAll(imageGroupDAO.selectAllBySingleUser(query.getTag()));
        CaptionUserVO user = captionUserService.getOrCreateByUsername(query.getModel());
        CaptionAttributeQuery captionAttributeQuery = new CaptionAttributeQuery();
        List<CaptionAttributeVO> attributes = captionAttributeService.queryCaptionAttributeList(
            captionAttributeQuery)
            .stream()
            .filter(CaptionAttributeVO::getIsActive)
            .toList();

        List<ImageGroupExportResponse> list = imageGroupCaptionDOS.stream()
            .map(ig -> {
                ImageGroupExportResponse response = new ImageGroupExportResponse();
                List<Integer> imageIds = JSONArray.parseArray(ig.getImageIds(), Integer.class);
                for (Integer imageId : imageIds) {
                    ImageVO imageVO = imageService.selectById(imageId);
                    ImageCaptionUserQuery imageCaptionUserQuery = new ImageCaptionUserQuery();
                    imageCaptionUserQuery.setImageId(imageId);
                    imageCaptionUserQuery.setUserId(user.getId());
                    List<ImageCaptionUserVO> imageCaption = imageCaptionUserService.queryImageCaptionUserList(
                        imageCaptionUserQuery);
                    if (imageCaption.isEmpty()) {
                        return null;
                    }
                    if (ImageTypeEnum.SCENE.getCode().equals(imageVO.getType())) {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("id", imageId);
                        jsonObject.put("imagePath", imageVO.getImagePath());
                        jsonObject.put("url", imageVO.getUrl());
                        jsonObject.put("data",
                            MapConvertUtil.convertToListOfKeyValueGeneric(keepValidAttribute(imageCaption.getFirst().getCaption(), attributes)));
                        response.setScene(jsonObject);
                    }
                    if (ImageTypeEnum.CLOTH.getCode().equals(imageVO.getType())) {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("id", imageId);
                        jsonObject.put("imagePath", imageVO.getImagePath());
                        jsonObject.put("url", imageVO.getUrl());
                        jsonObject.put("data",
                            MapConvertUtil.convertToListOfKeyValueGeneric(keepValidAttribute(imageCaption.getFirst().getCaption(), attributes)));
                        response.setCloth(jsonObject);
                    }
                }
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("id", ig.getId());
                jsonObject.put("data", MapConvertUtil.convertToListOfKeyValueGeneric(keepValidAttribute(JSONObject.parseObject(ig.getCaption()), attributes)));
                response.setStyle(jsonObject);
                response.setId(ig.getId());
                return response;
            })
            .filter(Objects::nonNull)
            .toList();

        return list;
    }

    private JSONObject keepValidAttribute(JSONObject jsonObject, List<CaptionAttributeVO> attributes) {
        jsonObject.keySet().retainAll(attributes.stream().map(CaptionAttributeVO::getKey).collect(Collectors.toList()));
        return jsonObject;
    }

    @Override
    public PageInfo<ImageGroupVO> queryTryonByPage(ImageQuery query) {
        PageInfo<ImageGroupVO> page = new PageInfo<>();
        ImageExample example = ImageConverter.query2Example(query);
        example.getOredCriteria().getFirst().andGroupIdIsNotNull();
        long totalCount = imageGroupDAO.countTryonByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }
        List<ImageGroupDO> imageGroupDOS = imageGroupDAO.selectTryonByExample(example);
        List<ImageGroupVO> imageGroupVOS = ImageGroupConverter.doList2VOList(imageGroupDOS);
        List<Integer> imageIds = imageGroupVOS.stream().map(ImageGroupVO::getImageIds).flatMap(Collection::stream).toList();
        ImageQuery imageQuery = new ImageQuery();
        imageQuery.setIds(imageIds);
        List<ImageVO> imageVOS = imageService.queryImageList(imageQuery);
        imageGroupVOS.forEach(imageGroupVO -> {
            List<ImageVO> list = imageVOS.stream()
                .filter(imageVO -> imageGroupVO.getImageIds().contains(imageVO.getId()))
                .sorted(Comparator.comparing(ImageVO::getId))
                .toList();
            imageGroupVO.setGroupId(list.getFirst().getMetadata().getString("groupId"));
            imageGroupVO.setImages(list);
        });
        if (CollectionUtils.isEmpty(imageGroupVOS)) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }
        page.setList(imageGroupVOS);
        page.setSize(CollectionUtils.size(imageGroupVOS));
        page.setTotalCount(totalCount);
        page.setHasNextPage(false);
        return page;
    }

    @Override
    public void sampling(ImageGroupQuery query) {
        ImageExample example = ImageConverter.query2Example(new ImageQuery());
        example.getOredCriteria().getFirst().andGroupIdIsNotNull();
        List<ImageDO> imageDOS = imageDAO.selectByExample(example);

    }

    /**
     * 将 ImageDO 数据聚合到 ImageGroup 表
     * 根据 groupId 聚合，取第一条数据插入到 imageGroup
     * 转换 metadata 中的 label 数据结构
     */
    @Override
    public void aggregate() {
        log.info("开始聚合 ImageDO 数据到 ImageGroup 表");

        // 1. 查询所有有 groupId 的 ImageDO 数据
        ImageExample example = ImageConverter.query2Example(new ImageQuery());
        example.getOredCriteria().getFirst().andGroupIdIsNotNull();
        List<ImageDO> imageDOS = imageDAO.selectByExample(example);

        if (CollectionUtils.isEmpty(imageDOS)) {
            log.info("没有找到需要聚合的 ImageDO 数据");
            return;
        }

        log.info("找到 {} 条需要聚合的 ImageDO 数据", imageDOS.size());

        // 2. 按 groupId 分组
        Map<String, List<ImageDO>> groupedImages = imageDOS.stream()
            .filter(image -> {
                try {
                    JSONObject metadata = JSON.parseObject(image.getMetadata());
                    return metadata != null && StringUtils.isNotBlank(metadata.getString("groupId"));
                } catch (Exception e) {
                    log.warn("解析 ImageDO metadata 失败，id: {}, metadata: {}", image.getId(), image.getMetadata());
                    return false;
                }
            })
            .collect(Collectors.groupingBy(image -> {
                JSONObject metadata = JSON.parseObject(image.getMetadata());
                return metadata.getString("groupId");
            }));

        log.info("按 groupId 分组后，共有 {} 个组", groupedImages.size());

        // 3. 处理每个组，取第一条数据进行聚合
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);

        groupedImages.forEach((groupId, imageList) -> {
            // 检查该 groupId 是否已经存在于 ImageGroup 表中
            ImageGroupExample groupExample = ImageGroupConverter.query2Example(new ImageGroupQuery());
            groupExample.getOredCriteria().getFirst().andGroupIdEqualTo(groupId);
            long existingCount = imageGroupDAO.countByExample(groupExample);

            if (existingCount > 0) {
                log.debug("GroupId {} 已存在于 ImageGroup 表中，跳过聚合", groupId);
                return;
            }

            // 取第一条数据作为代表
            ImageDO firstImage = imageList.getFirst();

            // 创建 ImageGroupDO
            ImageGroupDO imageGroupDO = createImageGroupFromImage(firstImage, imageList);

            // 插入到数据库
            imageGroupDAO.insertSelective(imageGroupDO);
            successCount.incrementAndGet();

            log.debug("成功聚合 groupId: {}, 包含 {} 张图片", groupId, imageList.size());
        });

        log.info("聚合完成，成功: {} 个组，失败: {} 个组", successCount.get(), failCount.get());
    }

    @Override
    public void assignMarkers(ImageGroupQuery query) {
        List<ImageGroupVO> imageGroupVOS = queryImageGroupList(query);
        List<Integer> markers = query.getMarkers();
        if (CollectionUtils.isEmpty(markers)) {
            return;
        }

    }

    /**
     * 从 ImageDO 创建 ImageGroupDO
     * 转换 metadata 中的 label 数据结构
     */
    private ImageGroupDO createImageGroupFromImage(ImageDO firstImage, List<ImageDO> imageList) {
        ImageGroupDO imageGroupDO = new ImageGroupDO();

        // 设置基本信息
        imageGroupDO.setType(firstImage.getType());

        // 设置图片ID列表
        List<Integer> imageIds = imageList.stream()
            .map(ImageDO::getId)
            .collect(Collectors.toList());
        imageGroupDO.setImageIds(JSON.toJSONString(imageIds));

        // 转换 metadata
        JSONObject transformedMetadata = transformMetadata(firstImage.getMetadata());
        imageGroupDO.setMetadata(JSON.toJSONString(transformedMetadata));

        // 处理 extInfo
        imageGroupDO.setExtInfo(firstImage.getExtInfo());

        // 设置时间信息
        Date now = new Date();
        imageGroupDO.setCreateTime(firstImage.getCreateTime() != null ? firstImage.getCreateTime() : now);
        imageGroupDO.setModifyTime(now);
        imageGroupDO.setDeleted(false);

        return imageGroupDO;
    }

    /**
     * 转换 metadata 中的 label 数据结构
     * 从 {"flat.1": {"checked": true, "isReference": true}}
     * 转换为 {"label.flat.1": [], "reference.flat.1": []}
     */
    private JSONObject transformMetadata(String metadata) {
        JSONObject result = new JSONObject();

        if (StringUtils.isBlank(metadata)) {
            return result;
        }

        JSONObject originalMetadata = JSON.parseObject(metadata);

        // 复制非 label 字段
        originalMetadata.forEach((key, value) -> {
            if (!"label".equals(key)) {
                result.put(key, value);
            }
        });

        // 处理 label 字段
        JSONObject labelObj = originalMetadata.getJSONObject("label");
        if (labelObj != null) {
            labelObj.forEach((labelKey, labelValue) -> {
                if (labelValue instanceof JSONObject) {
                    JSONObject labelDetail = (JSONObject)labelValue;
                    Boolean checked = labelDetail.getBoolean("checked");
                    Boolean isReference = labelDetail.getBoolean("isReference");

                    // 创建 label.{key} 和 reference.{key} 数组
                    String labelArrayKey = "label." + labelKey;
                    String referenceArrayKey = "reference." + labelKey;

                    // 初始化数组
                    JSONArray labelArray = new JSONArray();
                    JSONArray referenceArray = new JSONArray();

                    // 根据 checked 和 isReference 的值决定数据放入哪个数组
                    if (Boolean.TRUE.equals(checked)) {
                        // checked 为 true 的数据放入 label 数组
                        labelArray.add(labelDetail);
                    }

                    if (Boolean.TRUE.equals(isReference)) {
                        // isReference 为 true 的数据放入 reference 数组
                        referenceArray.add(labelDetail);
                    }

                    result.put(labelArrayKey, labelArray);
                    result.put(referenceArrayKey, referenceArray);
                }
            });
        }

        return result;
    }
}