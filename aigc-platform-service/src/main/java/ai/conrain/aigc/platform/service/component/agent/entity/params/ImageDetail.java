package ai.conrain.aigc.platform.service.component.agent.entity.params;


import lombok.Data;


/**
 * 图片详情
 */
@Data
public class ImageDetail {

    /**
     * 图片 ID
     */
    private Integer imageId;

    /**
     * 图片地址
     */
    private String imageUrl;

    /**
     * 匹配分数
     */
    private String matchScore;

    /**
     * 集群 Key
     */
    private String bgClusterKey;

    /**
     * 集群 ID
     */
    private Integer idxInCluster;

    /**
     * 图片标题 ID
     */
    private Integer imageCaptionId;

    /**
     * 图片相似度
     */
    private String styleSimilarity;

    /**
     * 图片流派
     */
    private String clothShootGenreEnum;
}
