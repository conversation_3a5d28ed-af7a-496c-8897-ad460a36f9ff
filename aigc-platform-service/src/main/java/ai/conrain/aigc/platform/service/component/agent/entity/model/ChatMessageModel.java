package ai.conrain.aigc.platform.service.component.agent.entity.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChatMessageModel {

    /** 唯一chatId */
    private String chatId;

    /** 状态 */
    private String status;

    /** 前置任务 ID */
    private Integer preTaskId;

    /** 消息来源 */
    private String messageFrom;

    /** 是否含有前置任务 */
    private Boolean isHasPreTask;

    /** 是否含有前置组件 */
    private Boolean isHasPreComponent;

    /** 前置组件消息 */
    private String preComponentMessage;

    /** 组件类型 */
    private String componentType;

    /** 具体数据参数 */
    private Object componentContentData;

    /** 创建时间 */
    private LocalDateTime createTime;
}
