package ai.conrain.aigc.platform.service.component.agent.entity.params;


import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * 图片生成入参
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ImageGenerationParams extends ClothAnalysisParams {

    // 选择模特任务 ID
    private Integer faceTaskId;

    // 服装分析任务 ID
    private Integer clothesTaskId;

    // 参考图任务 ID
    private Integer referenceTaskId;

    // 流派信息
    private GenreInfo genreInfo;

    // 选中模特
    private CreativeElementVO selectFace;

    private List<SelectImage> selectedImages;

    @Data
    public static class GenreInfo {
        // 搜索 Id
        private Integer searchId;

        // 推荐理由
        private String reason;

        // 流派标识
        private String clothShoot;

        // 流派名称
        private String clothShootName;

        // 风格相似度
        private String styleSimilarity;
    }

    @Data
    public static class SelectImage{
        // 图片 Key
        private String imageKey;

        // 图片详情信息
        private ImageDetail imageDetail;
    }



}
