package ai.conrain.aigc.platform.service.component.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.dal.dao.ComfyuiTaskDAO;
import ai.conrain.aigc.platform.dal.entity.ComfyuiTaskDO;
import ai.conrain.aigc.platform.dal.example.ComfyuiTaskExample;
import ai.conrain.aigc.platform.integration.ai.ComfyUIService;
import ai.conrain.aigc.platform.integration.ai.LabelTaskService;
import ai.conrain.aigc.platform.integration.ai.model.FileVO;
import ai.conrain.aigc.platform.integration.ai.model.LabelApplyRequest;
import ai.conrain.aigc.platform.integration.ai.model.LabelStatusInfo;
import ai.conrain.aigc.platform.integration.ai.model.LabelStatusInfo.LabelStatusEnum;
import ai.conrain.aigc.platform.integration.ai.model.PromptResult;
import ai.conrain.aigc.platform.integration.ai.model.QueueResult;
import ai.conrain.aigc.platform.integration.ai.model.QueueResult.QueueCodeEnum;
import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.service.component.ComfyuiTaskService;
import ai.conrain.aigc.platform.service.component.ComfyuiWorkflowTemplateService;
import ai.conrain.aigc.platform.service.component.MaterialInfoService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.component.dispatch.FileDispatch;
import ai.conrain.aigc.platform.service.component.dispatch.ServerHelper;
import ai.conrain.aigc.platform.service.component.dispatch.TaskDispatch;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.constants.LoraActivateKeys;
import ai.conrain.aigc.platform.service.enums.CameraAngleEnum;
import ai.conrain.aigc.platform.service.enums.ComfyuiTaskTypeEnum;
import ai.conrain.aigc.platform.service.enums.CutoutTypeEnum;
import ai.conrain.aigc.platform.service.enums.LabelTypeEnum;
import ai.conrain.aigc.platform.service.enums.MaterialType;
import ai.conrain.aigc.platform.service.helper.DingTalkNoticeHelper;
import ai.conrain.aigc.platform.service.model.biz.ClothAngleDetail;
import ai.conrain.aigc.platform.service.model.biz.ClothColorDetail;
import ai.conrain.aigc.platform.service.model.biz.ClothMaterialDetail;
import ai.conrain.aigc.platform.service.model.biz.ClothMaterialImg;
import ai.conrain.aigc.platform.service.model.biz.ComfyuiTplInfo;
import ai.conrain.aigc.platform.service.model.biz.CutoutTaskRetDetail;
import ai.conrain.aigc.platform.service.model.biz.LabelExtTagsDetail;
import ai.conrain.aigc.platform.service.model.biz.LabelTaskRetDetail;
import ai.conrain.aigc.platform.service.model.biz.LoraTaskParams;
import ai.conrain.aigc.platform.service.model.biz.LoraTaskRetDetail;
import ai.conrain.aigc.platform.service.model.biz.UserClothMatchingPreference;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.ComfyuiTaskConverter;
import ai.conrain.aigc.platform.service.model.query.ComfyuiTaskQuery;
import ai.conrain.aigc.platform.service.model.vo.ComfyuiTaskVO;
import ai.conrain.aigc.platform.service.model.vo.ComfyuiWorkflowTemplateVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialInfoVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.ComfyUIUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.DateUtils;
import ai.conrain.aigc.platform.service.util.FreemarkerUtils;
import ai.conrain.aigc.platform.service.util.MaterialModelUtils;
import ai.conrain.aigc.platform.service.util.MaterialUploadUtil;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.MultiKeyMap;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_LABEL_TYPE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_MODEL_ID;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SERVER_URL;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TRY_TIMES;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.MAX_COLOR_NUM;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.YES;
import static ai.conrain.aigc.platform.service.constants.MaterialConstants.FILE_IMAGE_ANALYSIS;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.TRAIN_FLOW_CUTOUT_MASK_POSE;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.TRAIN_FOLDER_SYNC_SWITCH;
import static ai.conrain.aigc.platform.service.util.ComfyUIUtils.buildClientId;
import static ai.conrain.aigc.platform.service.util.MaterialModelUtils.buildClothColorDetail;
import static ai.conrain.aigc.platform.service.util.MaterialModelUtils.formatExtTags;

/**
 * ComfyuiTaskService实现
 *
 * <AUTHOR>
 * @version ComfyuiTaskService.java v 0.1 2024-05-30 04:05:20
 */
@Slf4j
@Service
public class ComfyuiTaskServiceImpl implements ComfyuiTaskService {

    @Autowired
    private SystemConfigService systemConfigService;

    @Value("${comfyui.lora.url}")
    private String comfyuiUrl;

    /**
     * DAO
     */
    @Autowired
    private ComfyuiTaskDAO comfyuiTaskDAO;

    @Autowired
    private ComfyUIService comfyUIService;

    @Autowired
    private OssService ossService;

    @Autowired
    private TaskDispatch loraTrainTaskDispatch;
    @Autowired
    private TaskDispatch loraPreProcessTaskDispatch;
    @Autowired
    private FileDispatch fileDispatch;
    @Autowired
    private ServerHelper serverHelper;

    @Autowired
    private TairService tairService;

    @Autowired
    private MaterialInfoService materialInfoService;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    @Autowired
    private ComfyuiWorkflowTemplateService comfyuiWorkflowTemplateService;
    @Autowired
    private LabelTaskService labelTaskService;

    private static final MultiKeyMap WORKFLOW_CFG_MAP = new MultiKeyMap();

    static {
        //衣服-预处理
        WORKFLOW_CFG_MAP.put(ComfyuiTaskTypeEnum.prepareView, MaterialType.cloth.name(), "TRAIN_FLOW_PREPARE_VIEW");
        WORKFLOW_CFG_MAP.put(ComfyuiTaskTypeEnum.cutout, MaterialType.cloth.name(), "TRAIN_FLOW_CUTOUT");
        WORKFLOW_CFG_MAP.put(ComfyuiTaskTypeEnum.label, MaterialType.cloth.name(), "TRAIN_FLOW_LABEL");

        //模特-预处理
        WORKFLOW_CFG_MAP.put(ComfyuiTaskTypeEnum.cutout, MaterialType.face.name(), "TRAIN_FLOW_CUTOUT_FACE");
        WORKFLOW_CFG_MAP.put(ComfyuiTaskTypeEnum.label, MaterialType.face.name(), "TRAIN_FLOW_LABEL_FACE");

        //场景-预处理
        WORKFLOW_CFG_MAP.put(ComfyuiTaskTypeEnum.prepareView, MaterialType.scene.name(),
            "TRAIN_FLOW_PREPARE_VIEW_SCENE");
        WORKFLOW_CFG_MAP.put(ComfyuiTaskTypeEnum.cutout, MaterialType.scene.name(), "TRAIN_FLOW_CUTOUT_SCENE");
        WORKFLOW_CFG_MAP.put(ComfyuiTaskTypeEnum.label, MaterialType.scene.name(), "TRAIN_FLOW_LABEL_SCENE");

        WORKFLOW_CFG_MAP.put(ComfyuiTaskTypeEnum.lora, CommonConstants.SDXL, "TRAIN_FLOW_LORA");
        WORKFLOW_CFG_MAP.put(ComfyuiTaskTypeEnum.lora, CommonConstants.FLUX, "TRAIN_FLOW_LORA_FLUX");
    }

    @Override
    public ComfyuiTaskVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        ComfyuiTaskDO data = comfyuiTaskDAO.selectByPrimaryKey(id);
        if (null == data) {
            return null;
        }

        return ComfyuiTaskConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = comfyuiTaskDAO.deleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除ComfyuiTask失败");
    }

    @Override
    public ComfyuiTaskVO insert(ComfyuiTaskVO comfyuiTask) {

        AssertUtil.assertNotNull(comfyuiTask, ResultCode.PARAM_INVALID, "comfyuiTask is null");
        AssertUtil.assertTrue(comfyuiTask.getId() == null, ResultCode.PARAM_INVALID, "comfyuiTask.id is present");

        //创建时间、修改时间兜底
        if (comfyuiTask.getCreateTime() == null) {
            comfyuiTask.setCreateTime(new Date());
        }

        if (comfyuiTask.getModifyTime() == null) {
            comfyuiTask.setModifyTime(new Date());
        }

        if (comfyuiTask.getTaskStatus() == null) {
            comfyuiTask.setTaskStatus(QueueResult.QueueCodeEnum.QUEUED);
        }

        ComfyuiTaskDO data = ComfyuiTaskConverter.vo2DO(comfyuiTask);
        int n = comfyuiTaskDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建ComfyuiTask失败");
        AssertUtil.assertNotNull(data.getId(), "新建ComfyuiTask返回id为空");
        comfyuiTask.setId(data.getId());

        return comfyuiTask;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public ComfyuiTaskVO createTask(ComfyuiTaskVO comfyuiTask) {
        comfyuiTask = insert(comfyuiTask);
        return invokePromptApi(comfyuiTask, false, false);
    }

    private ComfyuiTaskVO invokePromptApi(ComfyuiTaskVO task, boolean isRetry, boolean needNotify) {

        AssertUtil.assertNotNull(task.getReqParams(), "reqParams不能为null");

        String serverUrl = dispatch(task);
        if (StringUtils.isBlank(serverUrl)) {
            log.warn("调度comfyui失败，无空闲端口, taskId={}", task.getId());
            return task;
        }

        ComfyuiTaskVO target = new ComfyuiTaskVO();
        target.setId(task.getId());
        target.setExtInfo(task.getExtInfo());

        try {
            if (isRetry && needNotify) {
                countAndNotifyFailedTimes(task);
            }

            //请求同台机器，查看对应服务器上文件是否完成了同步
            String fileServerUrl = serverHelper.getFileServerUrl(serverUrl);
            AssertUtil.assertNotBlank(fileServerUrl, "根据serverUrl查找fileUrl失败:" + serverUrl);

            //如果没有完成图片同步，则退出
            LoraTaskParams params = task.getReqParams().toJavaObject(LoraTaskParams.class);

            Integer imgNum = comfyUIService.queryImageCnt(params.getClothDir(), null, fileServerUrl);

            MaterialInfoVO materialInfoVO = materialInfoService.selectById(params.getOriginalMaterialId());
            AssertUtil.assertNotNull(materialInfoVO, "模型训练详情中material id对应的素材不存在");
            AssertUtil.assertNotNull(materialInfoVO.getMaterialDetail(),
                "模型训练详情中material id对应的素材详情不存在");

            //上传图片，连衣裙和上下装，不传入下半身图片到lora抠图和训练
            int expectedImgNum = MaterialUploadUtil.getExpectedImgNum(materialInfoVO);

            if (imgNum == null || imgNum < expectedImgNum) {
                log.info("原始素材未完成上传，这里直接返回，下次重试.imgNum={},expectedImgNum={}", imgNum,
                    expectedImgNum);
                return task;
            }

            //调用prompt接口

            //场景类型，转换格式，防止comfyui在接收到的json无法解析
            JSONObject reqParams = task.getReqParams();
            if (StringUtils.equals(reqParams.toJavaObject(LoraTaskParams.class).getMaterialType(),
                MaterialType.scene.name())) {

                reqParams.forEach((k, v) -> {
                    String result = ComfyUIUtils.parseJsonStrNoEnd(k, v != null ? v.toString() : null);
                    result = result.replaceAll("\t", "\\\\t");
                    reqParams.put(k, result);
                });
            }

            JSONObject tplParams = new JSONObject();
            tplParams.putAll(reqParams);
            if (StringUtils.isNotBlank(params.getAppendInfo())) {
                String appendInfo = params.getAppendInfo();
                if (StringUtils.contains(appendInfo, "\\\"")) {
                    appendInfo = appendInfo.replaceAll("\\\\\"", "\"");
                }
                JSONObject appendInfoJsonObject = JSONObject.parseObject(appendInfo);
                if (appendInfoJsonObject != null) {
                    tplParams.putAll(appendInfoJsonObject);
                }
            }
            tplParams.put("clientId", buildClientId(task.getOperatorId()));
            //兼容性处理 @see ai.conrain.aigc.platform.service.model.biz.LoraTaskParams
            tplParams.putIfAbsent("maxTrainStep", "4000");
            tplParams.putIfAbsent("clothType", "");
            tplParams.putIfAbsent("cutoutKeyword", "");

            JSONObject trainExtInfo = JSONObject.parseObject(params.getTrainExtInfo());
            trainExtInfo = trainExtInfo != null ? trainExtInfo : new JSONObject();
            // TODO 兼容逻辑，如果是保留配饰的话 cutoutType变为 clothAndMannequin
            if (StringUtils.equals(YES, params.getReservedItems())) {
                trainExtInfo.put("cutoutType", "clothAndMannequin");
            } else {
                trainExtInfo.put("cutoutType", params.getCutoutType());
            }
            trainExtInfo.put("clothType", params.getClothType());
            // 保留配饰新版工作流需要设置检测阈值为0.25
            if (StringUtils.equals(params.getReservedItems(), YES)) {
                tplParams.putIfAbsent("detectThreshold", "0.25");
            } else {
                tplParams.putIfAbsent("detectThreshold", "0.3");
            }
            tplParams.putIfAbsent("multiColor", tplParams.getOrDefault("multiColors", CommonConstants.NO));
            tplParams.putIfAbsent("bgMultiColor", tplParams.getOrDefault("bgMultiColor", YES));
            tplParams.putIfAbsent("fileNames", "");
            trainExtInfo.entrySet().removeIf(entry -> StringUtils.isBlank(entry.getValue().toString()));

            tplParams.put("extInfo", ComfyUIUtils.parseParams(trainExtInfo.toJSONString()));

            String flowConfig = getTrainTaskWorkflowCfg(task, target, tplParams);
            String prompt = FreemarkerUtils.parse(flowConfig, tplParams);

            LabelTypeEnum labelType = LabelTypeEnum.getByCode(task.getExtInfo(KEY_LABEL_TYPE, String.class));
            if (labelType == LabelTypeEnum.STRUCTURAL && task.getTaskType() == ComfyuiTaskTypeEnum.label) {
                LabelStatusInfo result = labelTaskService.apply(buildLabelApplyRequest(task, params), fileServerUrl);
                AssertUtil.assertNotNull(result, "apply label task failed");

                target.setPromptId(result.getTaskId() + "");
                log.info("提交打标服务申请成功，返回taskId={},status={}", result.getTaskId(), result.getStatus());
            } else {
                PromptResult promptRet = comfyUIService.prompt(prompt, serverUrl);

                if (promptRet != null && promptRet.isSuccess()) {
                    target.setPromptId(promptRet.getPromptId());
                    log.info("提交prompt进行【{}】成功", task.getTaskType().getDesc());

                } else {
                    log.error("提交prompt请求失败，稍后由任务重试，task id={}，error={}", task.getId(),
                        promptRet != null ? promptRet.getError() : "SYS_ERROR");
                }
            }

        } catch (Exception e) {
            log.error("invokePromptApi error,taskId=" + task.getId() + ",serverUrl=" + serverUrl, e);
        } finally {
            updateByIdSelective(target);
        }

        return selectById(task.getId());
    }

    private String getTrainTaskWorkflowCfg(ComfyuiTaskVO t, ComfyuiTaskVO target, JSONObject tplParams) {
        AssertUtil.assertNotNull(t, "task为空");
        AssertUtil.assertNotNull(t.getTaskType(), "type为空");
        String key;

        AssertUtil.assertNotNull(t.getReqParams(), "reqParams为空");
        LoraTaskParams loraTaskParams = t.getReqParams().toJavaObject(LoraTaskParams.class);
        if (t.getTaskType() == ComfyuiTaskTypeEnum.cutout && StringUtils.equals(MaterialType.cloth.name(),
            loraTaskParams.getMaterialType())) {
            CutoutTypeEnum cutoutType = CutoutTypeEnum.getByCode(loraTaskParams.getCutoutType());
            if (cutoutType == null) {
                //兼容逻辑
                if (StringUtils.equals(YES, loraTaskParams.getCutoutOnlyUpscale())) {
                    cutoutType = CutoutTypeEnum.UNCUT;
                } else {
                    cutoutType = CutoutTypeEnum.DEFAULT;
                }
            }
            // bugfix, 如果是补丁任务抠图，让他只使用TRAIN_FLOW_CUTOUT 这个工作流，抠图类型cutoutType不变
            if (loraTaskParams.isPatchCutout()) {
                key = "TRAIN_FLOW_CUTOUT";
            } else {
                key = cutoutType.getFlowKey();
            }

            // 结构化打标默认使用mask+pose方案
            LabelTypeEnum labelType = LabelTypeEnum.getByCode(t.getExtInfo(KEY_LABEL_TYPE, String.class));
            if (labelType == LabelTypeEnum.STRUCTURAL) {
                key = TRAIN_FLOW_CUTOUT_MASK_POSE;
            }
        } else if (t.getTaskType() != null && t.getTaskType() == ComfyuiTaskTypeEnum.lora) {
            key = (String)WORKFLOW_CFG_MAP.get(t.getTaskType(), loraTaskParams.getLoraType());
        } else {
            key = (String)WORKFLOW_CFG_MAP.get(t.getTaskType(), loraTaskParams.getMaterialType());
        }

        String tplCfg = fetchComfyuiWorkflowTemplateConfig(target, key, tplParams, t.getUserId());
        AssertUtil.assertNotNull(tplCfg, "getTrainTaskWorkflowCfg,key:" + key + " not found");
        AssertUtil.assertTrue(CommonUtil.isValidJson(tplCfg),
            "getTrainTaskWorkflowCfg,not valid json workflow config for key:" + key);

        log.info("getTrainTaskWorkflowCfg by key:{} success", key);

        return tplCfg;
    }

    private String fetchComfyuiWorkflowTemplateConfig(ComfyuiTaskVO task, String flowKey, JSONObject tplParams,
                                                      Integer userId) {
        String flowConfig;
        ComfyuiWorkflowTemplateVO workflowTemplateVO = comfyuiWorkflowTemplateService.getActiveTemplateByKey(flowKey,
            userId);
        if (workflowTemplateVO != null) {
            log.info("使用模板和版本：{},{}", flowKey, workflowTemplateVO.getVersion());
            flowConfig = workflowTemplateVO.getTemplateData();

            //这里保存模板信息，用于问题排查
            task.setComfyuiRequest(
                JSON.toJSONString(new ComfyuiTplInfo(flowKey, workflowTemplateVO.getVersion(), tplParams)));
        } else {
            log.error("没有找到模板对应的激活版本，只能使用默认配置，{}", flowKey);
            flowConfig = systemConfigService.queryValueByKey(flowKey);
        }

        AssertUtil.assertNotBlank(flowConfig, ResultCode.SYS_ERROR, "创作提交失败，未找到对应的流程模板:" + flowKey);
        return flowConfig;
    }

    /**
     * 查询任务状态
     */
    @Override
    public ComfyuiTaskVO pollingTaskStatus(Integer taskId) {
        AssertUtil.assertNotNull(taskId, "taskId is null");

        ComfyuiTaskVO task = this.selectById(taskId);
        AssertUtil.assertNotNull(task, "task is null");

        ComfyuiTaskTypeEnum type = task.getTaskType();

        String promptId = task.getPromptId();
        if (StringUtils.isBlank(promptId)) {
            log.info("task.promptId为空，重试taskId={}", taskId);
            return this.retryTask(task, false);
        }

        Integer userId = task.getUserId();
        AssertUtil.assertNotNull(userId, "task.userId为空");

        //从queue中同步状态
        String serverUrl = task.getExtValue(KEY_SERVER_URL, String.class);
        //发布兼容逻辑 todo 删除
        if (StringUtils.isBlank(serverUrl)) {
            serverUrl = type == ComfyuiTaskTypeEnum.lora ? comfyuiUrl : "http://lq.lianqiai.cn:20316";
        }

        if (!serverHelper.isEnable(serverUrl)) {
            log.info("当前服务不可用，直接进行重试,url={}", serverUrl);
            return this.retryTask(task, true);
        }

        QueueResult queueRet = null;
        LabelTypeEnum labelType = LabelTypeEnum.getByCode(task.getExtInfo(KEY_LABEL_TYPE, String.class));

        if (labelType == LabelTypeEnum.STRUCTURAL && task.getTaskType() == ComfyuiTaskTypeEnum.label) {
            String fileServerUrl = serverHelper.getFileServerUrl(serverUrl);
            log.info("开始进行结构化打标结果轮询,url={},outTaskId={}", serverUrl, task.getPromptId());
            queueRet = queryStatusByStructural(task, fileServerUrl);
        } else {
            //兼容逻辑
            Map<String, QueueResult> queueMap = comfyUIService.queryStatusByQueue(Collections.singletonList(promptId),
                serverUrl);
            queueRet = queueMap.get(task.getPromptId());
        }

        log.info("调用comfyUI查询queue接口{}任务状态结果：{}", promptId, queueRet);

        //队列中没有，查历史
        if (queueRet == null || queueRet.getCode() == null || queueRet.getCode() == QueueResult.QueueCodeEnum.NONE) {
            String fileServerUrl = serverHelper.getFileServerUrl(serverUrl);
            queueRet = comfyUIService.queryStatusByHistory(promptId, serverUrl, fileServerUrl);
        }

        if (queueRet == null || queueRet.getCode() == null || queueRet.getCode() == QueueResult.QueueCodeEnum.NONE) {
            log.info("当前任务队列和历史中均查不到任务，进行重试");
            return this.retryTask(task, true);
        }

        //能查到任务状态（非NONE）
        ComfyuiTaskVO target = new ComfyuiTaskVO();
        target.setId(task.getId());
        target.setExtInfo(task.getExtInfo());

        if (queueRet.getCode() != QueueResult.QueueCodeEnum.UNKNOWN) {
            target.setTaskStatus(queueRet.getCode());
        }

        log.info("当前任务{} task id={} code={}", type.getDesc(), taskId, queueRet.getCode());

        switch (queueRet.getCode()) {
            case QUEUED: {
                target.addExtInfo(CommonConstants.KEY_QUEUE_SIZE, queueRet.getQueueSize());
                break;
            }
            case RUNNING: {
                if (task.getExtInfo() == null || !task.getExtInfo().containsKey(CommonConstants.KEY_START_RUN_TIME)) {
                    target.addExtInfo(CommonConstants.KEY_START_RUN_TIME, DateUtils.formatTime(new Date()));
                }
                break;
            }
            case COMPLETED: {
                String fileServerUrl = serverHelper.getFileServerUrl(serverUrl);

                LoraTaskParams params = task.getReqParams().toJavaObject(LoraTaskParams.class);
                if (task.getTaskType() == ComfyuiTaskTypeEnum.prepareView) {
                    JSONObject prepareViewRetDetail = new JSONObject();
                    prepareViewRetDetail.put(CommonConstants.KEY_FINISH_TIME, DateUtils.formatTime(new Date()));

                    String content = comfyUIService.fetchFileContent(params.getPrepareViewRetDir(), FILE_IMAGE_ANALYSIS,
                        fileServerUrl);
                    prepareViewRetDetail.put(CommonConstants.KEY_MAIN_IMAGES,
                        MaterialModelUtils.parseFromPreview(content));
                    prepareViewRetDetail.put(CommonConstants.KEY_GARMENT_CATEGORY,
                        MaterialModelUtils.parseGarmentFromPreview(content));
                    target.setRetDetail(JSONObject.toJSONString(prepareViewRetDetail));
                }

                //抠图成功
                if (task.getTaskType() == ComfyuiTaskTypeEnum.cutout) {
                    CutoutTaskRetDetail cutoutTaskRetDetail = new CutoutTaskRetDetail();
                    cutoutTaskRetDetail.setCutoutRetDir(params.getCutoutRetDir());
                    cutoutTaskRetDetail.setFinishTime(DateUtils.formatTime(new Date()));

                    List<FileVO> cutoutFiles = comfyUIService.viewFiles(params.getCutoutRetDir(),
                        new String[] {"img", "text"}, fileServerUrl);
                    AssertUtil.assertTrue(CollectionUtils.isNotEmpty(cutoutFiles),
                        "抠图任务成功但目录下没有获取到用于预览的文件" + params.getCutoutRetDir());
                    cutoutTaskRetDetail.setCutoutFiles(cutoutFiles);

                    String dirMd5 = comfyUIService.calcDirMd5(params.getCutoutRetDir(), fileServerUrl);
                    if (StringUtils.isNotBlank(dirMd5)) {
                        cutoutTaskRetDetail.setCutoutDirMd5(dirMd5);
                    }

                    target.setRetDetail(JSONObject.toJSONString(cutoutTaskRetDetail));
                }

                //打标成功
                if (task.getTaskType() == ComfyuiTaskTypeEnum.label) {
                    LabelTaskRetDetail retDetail = new LabelTaskRetDetail();
                    retDetail.setTagRetDir(params.getLabelRetDir());
                    retDetail.setFinishTime(DateUtils.formatTime(new Date()));

                    initExtTags(retDetail, fileServerUrl, task);

                    //激活词
                    String materialType = StringUtils.defaultIfBlank(params.getMaterialType(),
                        MaterialType.cloth.name());
                    retDetail.setTags(
                        String.format("(%s:1.3), ", LoraActivateKeys.getActivateKey(materialType, false)));

                    //补充特征，GPT搭配
                    UserClothMatchingPreference gptMatchSuggestion = ComfyUIUtils.parseFeaturesFromMatchSuggestion(
                        comfyUIService.fetchFileContent(params.getLabelRetDir(), "match_suggestion", fileServerUrl),
                        false);
                    if (gptMatchSuggestion != null && gptMatchSuggestion.getTransClothCollocation() != null) {
                        retDetail.setAiGenFeatures(gptMatchSuggestion.getTransClothCollocation());
                    }

                    //用户偏好
                    UserClothMatchingPreference userPreferMatchSuggestion
                        = ComfyUIUtils.parseFeaturesFromMatchSuggestion(
                        comfyUIService.fetchFileContent(params.getLabelRetDir(), "user_match", fileServerUrl), true);

                    if (userPreferMatchSuggestion != null) {
                        userPreferMatchSuggestion.setOriginalPreferDesc(params.getMatchPrefer());
                        retDetail.setUserPreferFeatures(userPreferMatchSuggestion);
                    }

                    //场景需要读取json文件
                    String[] fileTypes = MaterialUploadUtil.getLabelFileTypes(task);
                    List<FileVO> labelFiles = comfyUIService.viewFiles(params.getLabelRetDir(), fileTypes,
                        fileServerUrl);
                    AssertUtil.assertTrue(CollectionUtils.isNotEmpty(labelFiles),
                        "打标任务成功但目录下没有获取到用于预览的文件" + params.getLabelRetDir());
                    retDetail.setLabelFiles(labelFiles);

                    String labelFilesMd5 = comfyUIService.calcDirMd5(params.getLabelRetDir(), fileServerUrl);
                    if (StringUtils.isNotBlank(labelFilesMd5)) {
                        retDetail.setLabelFilesMd5(labelFilesMd5);
                    }

                    retDetail.setFullbodyFrontViewImgUrl(getFullBodyFrontImgUrlByLabelRet(labelFiles, params));
                    retDetail.setColorImages(getColorImgUrlsByLabelRet(labelFiles, params));

                    target.setRetDetail(JSONObject.toJSONString(retDetail));
                }

                //lora成功
                if (task.getTaskType() == ComfyuiTaskTypeEnum.lora) {
                    String loraRetFilePath = params.getLoraModelRetFilePath();
                    AssertUtil.assertNotBlank(loraRetFilePath, "loraRetFilePath为空");

                    if (!comfyUIService.checkFileExists(loraRetFilePath, fileServerUrl)) {
                        log.info("lora文件{}不存在，lora任务失败，进行重试", loraRetFilePath);
                        return this.retryTask(task, true);

                    } else {
                        LoraTaskRetDetail retDetail = new LoraTaskRetDetail();
                        retDetail.setLoraRetFilePath(loraRetFilePath);
                        retDetail.setFinishTime(DateUtils.formatTime(new Date()));

                        target.setRetDetail(JSONObject.toJSONString(retDetail));
                    }
                }

                //发送任务完成事件
                sendTaskCompletedEvent(task, fileServerUrl);

                release(target);

                break;
            }
            case FAILED: {

                //设置重试次数
                Integer retryTimes = task.getExtValue(KEY_TRY_TIMES, Integer.class);
                retryTimes = retryTimes == null ? 1 : retryTimes + 1;
                task.addExtInfo(KEY_TRY_TIMES, retryTimes);

                log.info("当前任务{}task id={}失败，进行重试", type.getDesc(), taskId);
                return this.retryTask(task, true);
            }
            case UNKNOWN: {
                log.info("当前任务{}task id={}状态未知，继续轮询", type.getDesc(), taskId);
                break;
            }
            default: {
                break;
            }
        }

        //更新任务
        this.updateByIdSelective(target);

        return this.selectById(taskId);
    }

    /**
     * 重试任务
     *
     * @param task
     * @param needNotify
     */
    @Override
    public ComfyuiTaskVO retryTask(ComfyuiTaskVO task, boolean needNotify) {
        log.info("开始执行重试 taskId={}", task.getId());
        return invokePromptApi(task, true, needNotify);
    }

    private void countAndNotifyFailedTimes(ComfyuiTaskVO task) {
        try {
            String retryCacheKey = "CF_RETRY_" + task.getId();
            String count = tairService.getString(retryCacheKey);

            int retryCount = 1;
            if (StringUtils.isNotBlank(count)) {
                retryCount = Integer.parseInt(count) + 1;
            }

            //缓存24小时
            tairService.setString(retryCacheKey, String.valueOf(retryCount), 24 * 60 * 60);

            String clothSubDir = null;
            if (task.getReqParams() != null && task.getReqParams().toJavaObject(LoraTaskParams.class) != null) {
                LoraTaskParams params = task.getReqParams().toJavaObject(LoraTaskParams.class);
                clothSubDir = params.getClothSubDir();
            }

            String serverUrl = task.getExtValue(KEY_SERVER_URL, String.class);
            Integer modelId = task.getExtValue(KEY_MODEL_ID, Integer.class);

            //发送钉钉通知
            if (retryCount % 10 == 9) {
                String materialType = task.getReqParams() != null ? task.getReqParams().toJavaObject(
                    LoraTaskParams.class).getMaterialType() : MaterialType.cloth.name();

                int needNotifyTimes = task.getTaskType() == ComfyuiTaskTypeEnum.cutout && StringUtils.equals(
                    materialType, MaterialType.cloth.name()) ? 39 : 9;

                if (retryCount >= needNotifyTimes) {
                    DingTalkNoticeHelper.sendMsg2DevGroup(String.format(
                        "训练任务发生重试\nComfyuiTask.id=%s, modelId=%s, type=%s, 素材类型=%s, 服装=%s, 重试次数=%s, "
                        + "traceId=%s\nserverUrl=%s", task.getId(), modelId, task.getTaskType().getDesc(), materialType,
                        StringUtils.defaultIfBlank(clothSubDir, "-"), retryCount, MDC.get("traceId"), serverUrl));
                }
            }

        } catch (Throwable t) {
            log.error("训练任务重试发通知异常，忽略", t);
        }
    }

    @Override
    public void updateByIdSelective(ComfyuiTaskVO comfyuiTask) {
        AssertUtil.assertNotNull(comfyuiTask, ResultCode.PARAM_INVALID, "comfyuiTask is null");
        AssertUtil.assertTrue(comfyuiTask.getId() != null, ResultCode.PARAM_INVALID, "comfyuiTask.id is null");

        //修改时间必须更新
        comfyuiTask.setModifyTime(new Date());
        ComfyuiTaskDO data = ComfyuiTaskConverter.vo2DO(comfyuiTask);
        int n = comfyuiTaskDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新ComfyuiTask失败，影响行数:" + n);
    }

    @Override
    public List<ComfyuiTaskVO> queryComfyuiTaskList(ComfyuiTaskQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        ComfyuiTaskExample example = ComfyuiTaskConverter.query2Example(query);

        List<ComfyuiTaskDO> list = comfyuiTaskDAO.selectByExample(example);
        return ComfyuiTaskConverter.doList2VOList(list);
    }

    @Override
    public Long queryComfyuiTaskCount(ComfyuiTaskQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        ComfyuiTaskExample example = ComfyuiTaskConverter.query2Example(query);
        return comfyuiTaskDAO.countByExample(example);
    }

    /**
     * 带条件分页查询comfyui任务
     */
    @Override
    public PageInfo<ComfyuiTaskVO> queryComfyuiTaskByPage(ComfyuiTaskQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                              && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
            "pageNum or pageSize is invalid,pageNum:" + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<ComfyuiTaskVO> page = new PageInfo<>();

        ComfyuiTaskExample example = ComfyuiTaskConverter.query2Example(query);
        long totalCount = comfyuiTaskDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<ComfyuiTaskDO> list = comfyuiTaskDAO.selectByExample(example);
        page.setList(ComfyuiTaskConverter.doList2VOList(list));
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Override
    public List<ComfyuiTaskVO> batchQueryByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return null;
        }

        ComfyuiTaskExample example = new ComfyuiTaskExample();
        example.createCriteria().andIdIn(ids);

        List<ComfyuiTaskDO> list = comfyuiTaskDAO.selectByExampleWithBLOBs(example);
        return ComfyuiTaskConverter.doList2VOList(list);
    }

    /**
     * 根据打标结果获取服装对应正面图url，作为服装高清封面使用
     *
     * @param task
     * @return
     */
    @Override
    public String getFullBodyFrontImgUrlByLabelRet(ComfyuiTaskVO task) {
        if (task == null || task.getTaskType() != ComfyuiTaskTypeEnum.label) {
            throw new IllegalArgumentException("getFullBodyFrontImgUrlByLabelRet入参非法:" + task);
        }

        if (!task.getTaskStatus().equals(QueueResult.QueueCodeEnum.COMPLETED)) {
            log.info("当前打标任务没有结束，无法计算正面图url");
            return null;
        }

        if (task.getReqParams() == null || task.getRetDetail() == null || !CommonUtil.isValidJson(
            task.getRetDetail())) {
            log.warn("必要参数为空，无法计算，直接返回");
            return null;
        }

        LoraTaskParams params = task.getReqParams().toJavaObject(LoraTaskParams.class);
        LabelTaskRetDetail retDetail = JSONObject.parseObject(task.getRetDetail(), LabelTaskRetDetail.class);
        AssertUtil.assertNotNull(retDetail, "retDetail is not json");

        if (retDetail != null && StringUtils.isNotBlank(retDetail.getFullbodyFrontViewImgUrl())) {
            return retDetail.getFullbodyFrontViewImgUrl();
        }

        if (retDetail != null && CollectionUtils.isNotEmpty(retDetail.getLabelFiles())) {
            String url = getFullBodyFrontImgUrlByLabelRet(retDetail.getLabelFiles(), params);
            if (url != null) {

                //保存
                retDetail.setFullbodyFrontViewImgUrl(url);
                task.setRetDetail(JSONObject.toJSONString(retDetail));
                updateByIdSelective(task);

                return url;
            }
        }

        log.error("打标任务未找到正面图，无法计算");
        return null;
    }

    @Override
    public List<LabelExtTagsDetail> loadExtTags(Integer id) {
        ComfyuiTaskVO task = selectById(id);
        AssertUtil.assertNotNull(task, ResultCode.BIZ_FAIL, "task未找到");

        LoraTaskParams params = task.getReqParams().toJavaObject(LoraTaskParams.class);

        String serverUrl = task.getExtValue(KEY_SERVER_URL, String.class);

        return loadExtTags(params.getLabelRetDir(), serverHelper.getFileServerUrl(serverUrl), task);
    }

    /**
     * 任务派发
     *
     * @param task 任务
     * @return 服务地址
     */
    private String dispatch(ComfyuiTaskVO task) {
        if (task.getTaskType() == ComfyuiTaskTypeEnum.lora) {
            return loraTrainTaskDispatch.dispatch(task);
        } else {
            return loraPreProcessTaskDispatch.dispatch(task);
        }
    }

    /**
     * 释放任务端口
     *
     * @param task 任务
     */
    private void release(ComfyuiTaskVO task) {
        if (task.getTaskType() == ComfyuiTaskTypeEnum.lora) {
            loraTrainTaskDispatch.release(task);
        } else {
            loraPreProcessTaskDispatch.release(task);
        }
    }

    /**
     * 初始化补充激活词
     *
     * @param retDetail     结果详情
     * @param fileServerUrl 文件服务地址
     * @param task          任务
     */
    private void initExtTags(LabelTaskRetDetail retDetail, String fileServerUrl, ComfyuiTaskVO task) {
        List<LabelExtTagsDetail> result = loadExtTags(retDetail.getTagRetDir(), fileServerUrl, task);
        retDetail.setExtTagsList(result);
    }

    /**
     * 加载补充激活词
     *
     * @param rootPath      根节点
     * @param fileServerUrl 文件服务地址
     * @param task          打标类型
     * @return 补充激活词
     */
    private List<LabelExtTagsDetail> loadExtTags(String rootPath, String fileServerUrl, ComfyuiTaskVO task) {
        LabelTypeEnum labelType = LabelTypeEnum.getByCode(task.getExtInfo(KEY_LABEL_TYPE, String.class));
        List<LabelExtTagsDetail> result = new ArrayList<>();
        Map<List<CameraAngleEnum>, String> angleConfig = CameraAngleEnum.getAngleConfig();

        angleConfig.forEach((angleList, anglePath) -> {
            String value = comfyUIService.fetchFileContent(rootPath, anglePath, fileServerUrl);

            if (StringUtils.isBlank(value)) {
                log.info("未找到对应的解析文件，直接跳过,rootPath={},anglePath={}", rootPath, anglePath);
                return;
            }

            //兼容老的代码，理论上10.15发布后可删除 todo
            if (!JSONObject.isValid(value)) {
                String extTags = formatExtTags(value, CameraAngleEnum.WHOLE_BODY);
                if (StringUtils.isNotBlank(extTags)) {
                    result.add(new LabelExtTagsDetail(angleList, extTags));
                }
                log.error("兼容老的代码，如果有则说明需要保留,rootPath={},value={}", rootPath, value);
            } else if (labelType == LabelTypeEnum.STRUCTURAL) {
                log.info("进入结构化打标后处理流程,taskId={}", task.getId());
                List<ClothColorDetail> colors = new ArrayList<>();
                JSONObject json = JSONObject.parseObject(value);
                ClothColorDetail detail = buildClothColorDetail(task, angleList, json);

                colors.add(detail);

                result.add(new LabelExtTagsDetail(angleList, colors, false));

            } else {
                JSONObject json = JSONObject.parseObject(value);
                CameraAngleEnum bodyPosition = CameraAngleEnum.getBodyPosition(angleList);
                AtomicReference<Boolean> includesBra = new AtomicReference<>(null);
                List<ClothColorDetail> colors = new ArrayList<>();
                json.forEach((k, v) -> {
                    //c1、c2、c3等格式，需要解析出后面的123
                    String index = k.substring(1);
                    JSONObject detail = (JSONObject)v;

                    if (StringUtils.isNumeric(index)) {
                        String extTags = formatExtTags(detail.getString("description"), bodyPosition);
                        String detailGarmentType = detail.getString("detail_garment_type");
                        String detailDescription = detail.getString("detail_description");
                        String garmentStatus = detail.getString("garment_states");
                        String views = detail.getString("views");

                        Boolean includesBraVal = detail.getBoolean("includes_bra");
                        if (includesBraVal != null) {
                            includesBra.set(includesBraVal);
                        }
                        if (StringUtils.isNotBlank(detailGarmentType) && StringUtils.isNotBlank(detailDescription)) {
                            extTags = null;
                        }
                        colors.add(new ClothColorDetail(Integer.parseInt(index), detail.getString("color"), extTags,
                            detail.getString("garment_type"), detailGarmentType, detailDescription,
                            bodyPosition.getExtTag(), garmentStatus, views));
                    }
                });
                result.add(new LabelExtTagsDetail(angleList, colors, includesBra.get()));
            }
        });

        return result;
    }

    /**
     * 发送任务完成事件
     *
     * @param task          任务
     * @param fileServerUrl 文件服务地址
     */
    private void sendTaskCompletedEvent(ComfyuiTaskVO task, String fileServerUrl) {
        // lora任务不处理，由文件同步模块处理
        if (task.getTaskType() == ComfyuiTaskTypeEnum.lora || !systemConfigService.queryBoolValue(
            TRAIN_FOLDER_SYNC_SWITCH, true)) {
            return;
        }

        JSONObject params = task.getReqParams();
        String folderPath = params.getString("clothDir") + "/" + task.getTaskType().getFolderName();

        log.info("发送lora子任务完成事件，进行文件夹同步，folder={},taskId={}", folderPath, task.getId());
        fileDispatch.notifyFolderSync(fileServerUrl, folderPath, null, true);
    }

    private String getFullBodyFrontImgUrlByLabelRet(List<FileVO> labelFiles, LoraTaskParams params) {
        if (CollectionUtils.isEmpty(labelFiles) || params == null || params.getOriginalMaterialId() == null) {
            return null;
        }

        try {
            for (FileVO file : labelFiles) {
                String textContent = file.getTextContent();
                String fileName = file.getFileName();

                boolean fullBodyFrontView = textContent != null && (textContent.contains("full body")
                                                                    || textContent.contains("mid shot"))
                                            && textContent.contains("front view");

                if (!fullBodyFrontView || StringUtils.isBlank(fileName)) {
                    continue;
                }

                String[] arr = fileName.split("_");
                if (arr.length <= 1 || !StringUtils.isNumeric(arr[0].trim())) {
                    continue;
                }

                int index = Integer.parseInt(arr[0].trim());
                if (index < 1) {
                    continue;
                }

                MaterialInfoVO materialInfoVO = materialInfoService.selectById(params.getOriginalMaterialId());
                if (materialInfoVO == null || materialInfoVO.getMaterialDetail() == null) {
                    continue;
                }

                List<ClothMaterialImg> imgs = MaterialUploadUtil.getClothMaterialImgs4Upload(
                    materialInfoVO.getMaterialDetail().toJavaObject(ClothMaterialDetail.class),
                    materialInfoVO.getSubType());

                if (index <= imgs.size()) {
                    return imgs.get(index - 1).getImgUrl();
                }
            }
        } catch (Throwable e) {
            log.error("根据打标结果判定正面上传图片失败，忽略", e);
        }

        return null;
    }

    /**
     * 获取有序的颜色图片的url列表
     *
     * @param labelFiles 打标文件
     * @param params     参数
     * @return 结果
     */
    private List<String> getColorImgUrlsByLabelRet(List<FileVO> labelFiles, LoraTaskParams params) {
        if (CollectionUtils.isEmpty(labelFiles)) {
            return null;
        }

        List<String> result = new ArrayList<>();
        for (int i = 1; i <= MAX_COLOR_NUM; i++) {
            String url = getColorImgUrlsByLabelRet(labelFiles, params, i);
            if (StringUtils.isBlank(url)) {
                continue;
            }
            result.add(url);
        }

        return result;
    }

    /**
     * 获取有序的颜色图片的url列表
     *
     * @param labelFiles 打标文件
     * @param params     参数
     * @param colorIdx   颜色序号
     * @return 结果
     */
    private String getColorImgUrlsByLabelRet(List<FileVO> labelFiles, LoraTaskParams params, int colorIdx) {

        try {
            List<FileVO> filter = labelFiles.stream().filter(e -> isLabelFileForColor(e.getFileName(), colorIdx))
                .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(filter)) {
                log.warn("根据打标结果获取颜色图片，失败跳过，未找到以'_c{}.txt'结尾的文件", colorIdx);
                return null;
            }

            //第一优先级取正面全身的图片
            List<FileVO> currFilter = filter.stream().filter(
                e -> StringUtils.isNotBlank(e.getTextContent()) && StringUtils.contains(e.getTextContent(),
                    "front view") && (StringUtils.contains(e.getTextContent(), "full body") || StringUtils.contains(
                    e.getTextContent(), "mid shot"))).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(currFilter)) {
                log.info("根据打标结果获取颜色图片，1未取到正面全身的打标文件,colorIdx={}", colorIdx);

                //第二优先级取正面半身的图片
                currFilter = filter.stream().filter(
                    e -> StringUtils.isNotBlank(e.getTextContent()) && StringUtils.contains(e.getTextContent(),
                        "front view")).collect(Collectors.toList());

                if (CollectionUtils.isEmpty(currFilter)) {
                    log.info("根据打标结果获取颜色图片，2未取到正面的打标文件,colorIdx={}", colorIdx);
                    //第三优先级取背面全身的图片
                    currFilter = filter.stream().filter(
                        e -> StringUtils.isNotBlank(e.getTextContent()) && StringUtils.contains(e.getTextContent(),
                            "back view") && (StringUtils.contains(e.getTextContent(), "full body")
                                             || StringUtils.contains(e.getTextContent(), "mid shot"))).collect(
                        Collectors.toList());

                    if (CollectionUtils.isEmpty(filter)) {
                        log.info("根据打标结果获取颜色图片，3未取到背面全身的打标文件,colorIdx={}", colorIdx);

                        //第四优先级取背面半身的图片
                        currFilter = filter.stream().filter(
                            e -> StringUtils.isNotBlank(e.getTextContent()) && StringUtils.contains(e.getTextContent(),
                                "back view")).collect(Collectors.toList());
                    }
                }
            }

            if (CollectionUtils.isEmpty(currFilter)) {
                log.warn("根据打标结果获取颜色图片，失败跳过，4未取到4个角度的图片,colorIdx={}", colorIdx);
                return null;
            }

            String fileName = currFilter.get(0).getFileName();
            String[] arr = StringUtils.split(fileName, "_");
            if (arr.length <= 1 || !StringUtils.isNumeric(arr[0].trim())) {
                log.error("根据打标结果获取颜色图片，失败跳过，图片文件名解析失败,fileName={},colorIdx={}", fileName,
                    colorIdx);
                return null;
            }

            int index = Integer.parseInt(arr[0].trim());

            MaterialInfoVO materialInfoVO = materialInfoService.selectById(params.getOriginalMaterialId());
            if (materialInfoVO == null || materialInfoVO.getMaterialDetail() == null) {
                log.error("根据打标结果获取颜色图片，失败跳过，获取不到materialInfo,materialInfo={},colorIdx={}",
                    params.getOriginalMaterialId(), colorIdx);
                return null;
            }

            List<ClothMaterialImg> imgs = MaterialUploadUtil.getClothMaterialImgs4Upload(
                materialInfoVO.getMaterialDetail().toJavaObject(ClothMaterialDetail.class),
                materialInfoVO.getSubType());

            log.info("根据打标结果获取颜色图片，成功={},colorIdx={}", index <= imgs.size(), colorIdx);

            if (index <= imgs.size()) {
                return imgs.get(index - 1).getImgUrl();
            }

        } catch (Throwable e) {
            log.error("根据打标结果获取颜色图片，异常忽略,colorIdx=" + colorIdx, e);
        }

        return null;
    }

    /**
     * 构建全身的主图结果，包含正面、背面
     *
     * @param retPath       结果地址
     * @param fileServerUrl 文件服务地址
     * @return 主图结果
     */
    private ClothAngleDetail buildMainFullImages(String retPath, String fileServerUrl) {
        String content = comfyUIService.fetchFileContent(retPath, "image_analysis.txt", fileServerUrl);
        return MaterialModelUtils.parseFromPreview(content);
    }

    /**
     * 判断是否是颜色图片
     *
     * @param fileName 文件名
     * @param colorIdx 颜色序号
     * @return 结果
     */
    private boolean isLabelFileForColor(String fileName, int colorIdx) {
        String regex = ".*_c" + colorIdx + "(_[^.]+)?\\.txt";
        return fileName != null && fileName.matches(regex);
    }

    /**
     * 查询结构化打标结果
     *
     * @param task          任务
     * @param fileServerUrl 文件服务地址
     * @return 打标结果
     */
    private QueueResult queryStatusByStructural(ComfyuiTaskVO task, String fileServerUrl) {
        LoraTaskParams params = task.getReqParams().toJavaObject(LoraTaskParams.class);
        MaterialType materialType = MaterialType.getByCode(params.getMaterialType());
        if (materialType == null) {
            materialType = MaterialType.cloth;
        }
        LabelStatusInfo query = labelTaskService.query(Integer.parseInt(task.getPromptId()),
            materialType.getLabelTaskType(), fileServerUrl);

        // 兼容处理
        if (query.getStatus() == LabelStatusEnum.COMPLETED) {
            return new QueueResult(QueueCodeEnum.COMPLETED);
        }

        if (query.getStatus() == LabelStatusEnum.FAILED) {
            return new QueueResult(QueueCodeEnum.FAILED);
        }

        if (query.getStatus() == LabelStatusEnum.APPLY || query.getStatus() == LabelStatusEnum.QUEUED) {
            return new QueueResult(QueueCodeEnum.QUEUED);
        }

        return new QueueResult(QueueCodeEnum.RUNNING);
    }

    /**
     * 构建打标请求request
     *
     * @param task   任务
     * @param params 训练参数
     */
    private static LabelApplyRequest buildLabelApplyRequest(ComfyuiTaskVO task, LoraTaskParams params) {
        Integer retryTimes = task.getExtValue(KEY_TRY_TIMES, Integer.class);
        retryTimes = retryTimes == null ? 0 : retryTimes;

        LabelApplyRequest request = new LabelApplyRequest();
        request.setOutBizNo(task.getId() + "-" + retryTimes);
        request.setTargetDir(params.getClothDir());
        LabelApplyRequest.LabelTypeEnum type = null;
        if (params.getMaterialType().equals("cloth")) {
            type = LabelApplyRequest.LabelTypeEnum.CLOTH_LABEL;
        } else if (params.getMaterialType().equals("face")) {
            type = LabelApplyRequest.LabelTypeEnum.MODEL_LABEL;
        } else {
            type = LabelApplyRequest.LabelTypeEnum.SCENE_LABEL;
        }
        request.setType(type);
        request.setClothType(params.getClothType());
        request.setGenderType(params.getClothStyleType());
        //request.setAgeRange(params.getAgeRange());
        request.setMultiColors(StringUtils.equals("Y", params.getMultiColors()));
        request.setUseShot(StringUtils.equals("Y", params.getUseShot()));
        request.setImageSize(
            StringUtils.isNotBlank(params.getImageSize()) ? Integer.parseInt(params.getImageSize()) : null);
        request.setCutoutType(params.getCutoutType());
        return request;
    }

    /**
     * 验证文件是否存在
     *
     * @param directory     目录路径
     * @param fileName      文件名
     * @param fileServerUrl 文件服务器URL
     * @return 文件是否存在
     */
    private boolean verifyFileExists(String directory, String fileName, String fileServerUrl) {
        if (StringUtils.isAnyBlank(directory, fileName, fileServerUrl)) {
            return false;
        }

        try {
            // 检查文件是否存在
            String filePath = directory + "/" + fileName;
            return comfyUIService.checkFileExists(filePath, fileServerUrl);
        } catch (Exception e) {
            log.error("验证文件存在性失败: {}/{}", directory, fileName, e);
            return false;
        }
    }

    /**
     * 更新工作流节点配置
     *
     * @param promptNodes  工作流节点配置
     * @param preciseRegex 精确的正则表达式
     * @param baseFileName 基础文件名
     * @return 修改的节点数量
     */
    private int updateWorkflowNodes(JSONObject promptNodes, String preciseRegex, String baseFileName) {
        int modifiedNodeCount = 0;

        // 定义节点更新策略
        Map<String, NodeUpdateStrategy> nodeStrategies = new HashMap<>();
        nodeStrategies.put("24", new NodeUpdateStrategy(preciseRegex, 1, "主抠图节点"));
        nodeStrategies.put("240", new NodeUpdateStrategy(preciseRegex, 1, "关键词生成节点"));
        nodeStrategies.put("169", new NodeUpdateStrategy(preciseRegex, 1, "特定处理节点"));

        // 更新指定节点
        for (Map.Entry<String, NodeUpdateStrategy> entry : nodeStrategies.entrySet()) {
            String nodeId = entry.getKey();
            NodeUpdateStrategy strategy = entry.getValue();

            JSONObject node = promptNodes.getJSONObject(nodeId);
            if (node != null && "LoadImagesFromDirList_LR".equals(node.getString("class_type"))) {
                JSONObject inputs = node.getJSONObject("inputs");
                if (inputs != null) {
                    inputs.put("regex", strategy.getRegex());
                    inputs.put("image_load_cap", strategy.getImageLoadCap());

                    modifiedNodeCount++;
                    log.info("修改节点{} ({}): regex={}, image_load_cap={}", nodeId, strategy.getDescription(),
                        strategy.getRegex(), strategy.getImageLoadCap());
                }
            }
        }

        return modifiedNodeCount;
    }

    /**
     * 节点更新策略
     */
    private static class NodeUpdateStrategy {
        private final String regex;
        private final int imageLoadCap;
        private final String description;

        public NodeUpdateStrategy(String regex, int imageLoadCap, String description) {
            this.regex = regex;
            this.imageLoadCap = imageLoadCap;
            this.description = description;
        }

        public String getRegex() {
            return regex;
        }

        public int getImageLoadCap() {
            return imageLoadCap;
        }

        public String getDescription() {
            return description;
        }
    }
}