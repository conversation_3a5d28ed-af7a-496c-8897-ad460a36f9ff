package ai.conrain.aigc.platform.service.component.agent;

import ai.conrain.aigc.platform.service.component.agent.entity.params.ImageGenerationParams;
import ai.conrain.aigc.platform.service.enums.agent.AgentSessionTaskTypeEnum;
import ai.conrain.aigc.platform.service.model.vo.AgentSessionTaskVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 图片生成任务处理器
 */
@Slf4j
@Component
public class ImageGenerationTaskHandler implements AgentTaskHandler<ImageGenerationParams> {

    @Override
    public void handleTask(AgentSessionTaskVO agentSessionTask) {
        log.info("【agentTask】ImageGenerationTaskHandler::handleTask::开始处理图片生成任务,  agentTaskId={}", agentSessionTask.getId());
        // 获取任务参数
        ImageGenerationParams imageGenerationParams = extractParameters(agentSessionTask);



        log.info("【agentTask】ImageGenerationTaskHandler::handleTask::开始图片生成任务完成,  agentTaskId={}", agentSessionTask.getId());
    }

    @Override
    public String getSupportedTaskType() {
        return AgentSessionTaskTypeEnum.IMAGE_GENERATION.getCode();
    }

    @Override
    public Class<ImageGenerationParams> getParameterType() {
        return ImageGenerationParams.class;
    }

}