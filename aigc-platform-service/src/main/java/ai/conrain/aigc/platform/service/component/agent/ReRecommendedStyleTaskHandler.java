package ai.conrain.aigc.platform.service.component.agent;

import ai.conrain.aigc.platform.service.component.SearchTaskService;
import ai.conrain.aigc.platform.service.component.agent.entity.params.RecommendedStyleSceneParams;
import ai.conrain.aigc.platform.service.enums.agent.AgentSessionTaskTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.agent.StyleImageSearchRequest;
import ai.conrain.aigc.platform.service.model.query.SearchTaskQuery;
import ai.conrain.aigc.platform.service.model.vo.AgentSessionTaskVO;
import ai.conrain.aigc.platform.service.model.vo.SearchTaskVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 重新推荐风格场景任务处理器
 */
@Slf4j
@Component
public class ReRecommendedStyleTaskHandler  extends AbstractStyleSceneTaskHandler<RecommendedStyleSceneParams> {

    @Autowired
    private SearchTaskService searchTaskService;

    @Override
    protected StyleImageSearchRequest buildSearchRequest(AgentSessionTaskVO agentSessionTask, String clothesTaskId, String referenceTaskId, RecommendedStyleSceneParams params) {
        // 调用父类方法构建基础请求
        StyleImageSearchRequest request = buildBaseSearchRequest(agentSessionTask, clothesTaskId, referenceTaskId);

        // 设置特定的搜索配置
        configureSearchOptions(request, agentSessionTask, params);

        // 如果 isReCommended 为 true 则说明需要排除掉之前的查询结果
        Boolean isReCommended = params.getIsReCommended();
        if (Objects.nonNull(isReCommended)) {
            SearchTaskQuery searchTaskQuery = new SearchTaskQuery();
            searchTaskQuery.setUserId(agentSessionTask.getUserId());
            searchTaskQuery.setUserSessionId(agentSessionTask.getSessionId());

            // 查询出该任务关联的所有的推荐列表
            List<SearchTaskVO> searchTaskVOList = searchTaskService.querySearchTaskList(searchTaskQuery);
            if (Objects.isNull(searchTaskVOList) || searchTaskVOList.isEmpty()) {
                return request;
            }

            // 提取 Id 集合
            List<Integer> searchIdList = searchTaskVOList.stream().map(SearchTaskVO::getId).toList();
            // 设置排除 id 集合
            request.setExcludeRetInSearchIds(searchIdList);
        }

        return request;
    }

    @Override
    public String getSupportedTaskType() {
        return AgentSessionTaskTypeEnum.RE_RECOMMENDED_STYLE_SCENE.getCode();
    }

    @Override
    public Class<RecommendedStyleSceneParams> getParameterType() {
        return RecommendedStyleSceneParams.class;
    }
}
