package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.AgentSessionDO;
import ai.conrain.aigc.platform.dal.example.AgentSessionExample;
import ai.conrain.aigc.platform.service.enums.agent.AgentSessionStatusEnum;
import ai.conrain.aigc.platform.service.model.query.AgentSessionQuery;
import ai.conrain.aigc.platform.service.model.vo.AgentSessionVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * AgentSessionConverter
 *
 * @version AgentSessionService.java
 */
public class AgentSessionConverter {

    /**
     * DO -> VO
     */
    public static AgentSessionVO do2VO(AgentSessionDO from) {
        AgentSessionVO to = new AgentSessionVO();
        to.setId(from.getId());
        to.setSessionType(from.getSessionType());
        to.setClothesName(from.getClothesName());
        to.setClothesType(from.getClothesType());
        to.setClothesThumbnail(from.getClothesThumbnail());
        to.setUserId(from.getUserId());
        to.setStatus(AgentSessionStatusEnum.getByCode(from.getStatus()));
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setLastInteractiveTime(from.getLastInteractiveTime());
        to.setOtherThumbnails(CommonUtil.parseObject(from.getOtherThumbnails()));
        to.setExtInfo(CommonUtil.parseObject(from.getExtInfo()));

        return to;
    }

    /**
     * VO -> DO
     */
    public static AgentSessionDO vo2DO(AgentSessionVO from) {
        AgentSessionDO to = new AgentSessionDO();
        to.setId(from.getId());
        to.setSessionType(from.getSessionType());
        to.setClothesName(from.getClothesName());
        to.setClothesType(from.getClothesType());
        to.setClothesThumbnail(from.getClothesThumbnail());
        to.setUserId(from.getUserId());
        to.setStatus(from.getStatus().getCode());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setLastInteractiveTime(from.getLastInteractiveTime());
        to.setOtherThumbnails(CommonUtil.toJSONString(from.getOtherThumbnails()));
        to.setExtInfo(CommonUtil.toJSONString(from.getExtInfo()));

        return to;
    }

    /**
     * Query -> Example
     */
    public static AgentSessionExample query2Example(AgentSessionQuery from) {
        AgentSessionExample to = new AgentSessionExample();
        AgentSessionExample.Criteria c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getSessionType())) {
            c.andSessionTypeEqualTo(from.getSessionType());
        }
        if (!ObjectUtils.isEmpty(from.getClothesName())) {
            c.andClothesNameEqualTo(from.getClothesName());
        }
        if (!ObjectUtils.isEmpty(from.getClothesType())) {
            c.andClothesTypeEqualTo(from.getClothesType());
        }
        if (!ObjectUtils.isEmpty(from.getClothesThumbnail())) {
            c.andClothesThumbnailEqualTo(from.getClothesThumbnail());
        }
        if (!ObjectUtils.isEmpty(from.getUserId())) {
            c.andUserIdEqualTo(from.getUserId());
        }
        if (!ObjectUtils.isEmpty(from.getStatus())) {
            c.andStatusEqualTo(from.getStatus());
        }
        if (!ObjectUtils.isEmpty(from.getOperatorId())) {
            c.andOperatorIdEqualTo(from.getOperatorId());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        if (!ObjectUtils.isEmpty(from.getLastInteractiveTime())) {
            c.andLastInteractiveTimeEqualTo(from.getLastInteractiveTime());
        }
        //逻辑删除过滤
        for (AgentSessionExample.Criteria each : to.getOredCriteria()) {
            each.andLogicalDeleted(false);
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.setOffset((from.getPageNum() - 1) * from.getPageSize());
            to.setRows(from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }

    /**
     * do list -> vo list
     */
    public static List<AgentSessionVO> doList2VOList(List<AgentSessionDO> list) {
        return CommonUtil.listConverter(list, AgentSessionConverter::do2VO);
    }
}