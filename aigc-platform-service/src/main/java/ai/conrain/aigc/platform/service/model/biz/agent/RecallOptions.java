package ai.conrain.aigc.platform.service.model.biz.agent;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 召回配置选项
 */
@Data
public class RecallOptions implements Serializable {

    // 是否启用流派并发查询模式
    private Boolean enableGenreConcurrency = true;

    // 款式相似度阈值
    private Double styleSimilarityThreshold = 0.5;

    // 款式匹配的图片数量
    private Integer styleRecallCount = 2000;

}