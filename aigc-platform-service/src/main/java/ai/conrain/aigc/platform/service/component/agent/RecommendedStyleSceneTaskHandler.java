package ai.conrain.aigc.platform.service.component.agent;

import ai.conrain.aigc.platform.service.component.agent.entity.params.RecommendedStyleSceneParams;
import ai.conrain.aigc.platform.service.enums.agent.AgentSessionTaskTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.agent.StyleImageSearchRequest;
import ai.conrain.aigc.platform.service.model.vo.AgentSessionTaskVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 推荐风格场景任务处理器
 * 根据服装分析和参考图分析结果，推荐匹配的风格场景图片
 */
@Slf4j
@Component
public class RecommendedStyleSceneTaskHandler extends AbstractStyleSceneTaskHandler<RecommendedStyleSceneParams> {

    @Override
    public String getSupportedTaskType() {
        return AgentSessionTaskTypeEnum.RECOMMENDED_STYLE_SCENE.getCode();
    }

    @Override
    public Class<RecommendedStyleSceneParams> getParameterType() {
        return RecommendedStyleSceneParams.class;
    }

    @Override
    protected StyleImageSearchRequest buildSearchRequest(AgentSessionTaskVO agentSessionTask,
                                                        String clothesTaskId,
                                                        String referenceTaskId,
                                                        RecommendedStyleSceneParams params) {
        // 调用父类方法构建基础请求
        StyleImageSearchRequest request = buildBaseSearchRequest(agentSessionTask, clothesTaskId, referenceTaskId);
        
        // 设置特定的搜索配置
        configureSearchOptions(request, agentSessionTask, params);
        
        return request;
    }
}
