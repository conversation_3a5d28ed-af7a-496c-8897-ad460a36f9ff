# 数据迁移转换示例

## 示例 1：checked 为 true，isReference 为 false

### 输入数据：
```json
{
  "tags": ["moncler"],
  "label": {
    "flat.1": {
      "checked": true,
      "isReference": false
    }
  },
  "groupId": "45bbd7fb-3d9f-45c3-9d22-f240e629b933"
}
```

### 输出数据：
```json
{
  "tags": ["moncler"],
  "groupId": "45bbd7fb-3d9f-45c3-9d22-f240e629b933",
  "label.flat.1": [
    {
      "checked": true,
      "isReference": false
    }
  ],
  "reference.flat.1": []
}
```

## 示例 2：checked 为 false，isReference 为 true

### 输入数据：
```json
{
  "tags": ["nike"],
  "label": {
    "person.1": {
      "checked": false,
      "isReference": true
    }
  },
  "groupId": "55bbd7fb-3d9f-45c3-9d22-f240e629b944"
}
```

### 输出数据：
```json
{
  "tags": ["nike"],
  "groupId": "55bbd7fb-3d9f-45c3-9d22-f240e629b944",
  "label.person.1": [],
  "reference.person.1": [
    {
      "checked": false,
      "isReference": true
    }
  ]
}
```

## 示例 3：checked 为 true，isReference 为 true

### 输入数据：
```json
{
  "tags": ["adidas"],
  "label": {
    "flat.1": {
      "checked": true,
      "isReference": true
    }
  },
  "groupId": "65bbd7fb-3d9f-45c3-9d22-f240e629b955"
}
```

### 输出数据：
```json
{
  "tags": ["adidas"],
  "groupId": "65bbd7fb-3d9f-45c3-9d22-f240e629b955",
  "label.flat.1": [
    {
      "checked": true,
      "isReference": true
    }
  ],
  "reference.flat.1": [
    {
      "checked": true,
      "isReference": true
    }
  ]
}
```

## 示例 4：多个 label 项

### 输入数据：
```json
{
  "tags": ["puma"],
  "label": {
    "flat.1": {
      "checked": true,
      "isReference": false
    },
    "person.1": {
      "checked": false,
      "isReference": true
    },
    "scene.1": {
      "checked": true,
      "isReference": true
    }
  },
  "groupId": "75bbd7fb-3d9f-45c3-9d22-f240e629b966"
}
```

### 输出数据：
```json
{
  "tags": ["puma"],
  "groupId": "75bbd7fb-3d9f-45c3-9d22-f240e629b966",
  "label.flat.1": [
    {
      "checked": true,
      "isReference": false
    }
  ],
  "reference.flat.1": [],
  "label.person.1": [],
  "reference.person.1": [
    {
      "checked": false,
      "isReference": true
    }
  ],
  "label.scene.1": [
    {
      "checked": true,
      "isReference": true
    }
  ],
  "reference.scene.1": [
    {
      "checked": true,
      "isReference": true
    }
  ]
}
```

## 转换规则总结

1. **label.{key} 数组**：包含所有 `checked: true` 的数据项
2. **reference.{key} 数组**：包含所有 `isReference: true` 的数据项
3. **同时满足条件**：数据会同时出现在两个数组中
4. **都不满足条件**：对应的数组为空数组 `[]`
5. **保持原始数据**：数组中存储的是完整的原始对象，包含 `checked` 和 `isReference` 字段
