-- 用户信息表
DROP TABLE IF EXISTS `user`;
CREATE TABLE IF NOT EXISTS `user`(
`id` INT UNSIGNED AUTO_INCREMENT COMMENT '用户id',
`nick_name` VARCHAR(64) NULL COMMENT '昵称',
`real_name` VARCHAR(32) NULL COMMENT '真实姓名',
`login_id` VARCHAR(64) NOT NULL COMMENT '登录id',
`pswd` VARCHAR(128) NULL COMMENT '登录密码',
`mobile` VARCHAR(16) NOT NULL COMMENT '手机号码',
`role_type` VARCHAR(32) NOT NULL COMMENT '角色类型',
`custom_role` VARCHAR(64) NULL COMMENT '自定义角色',
`user_type` VARCHAR(32) NOT NULL DEFAULT 'MASTER' COMMENT '用户类型，MASTER、SUB',
`master_id` INT UNSIGNED NULL COMMENT '主账号id',
`status` VARCHAR(32) NOT NULL DEFAULT 'ENABLED' COMMENT '状态，ENABLED、DISABLED',
`operator_id` INT NOT NULL COMMENT '操作者id',
`register_from` VARCHAR(64) NULL COMMENT '注册来源',
`memo` VARCHAR(256) NULL COMMENT '备注',
`login_fail_count` INT NOT NULL DEFAULT 0 COMMENT '登录失败次数',
`last_login_time` TIMESTAMP NULL COMMENT '最后登录时间',
`deleted` BOOLEAN NOT NULL COMMENT '是否删除，0未删除、1删除',
`create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
`modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRIMARY KEY ( `id` )
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
AUTO_INCREMENT = 100000
COMMENT '用户信息表';

-- ALTER TABLE `user` ADD COLUMN `custom_role` VARCHAR(64) DEFAULT NULL COMMENT '自定义角色';
ALTER TABLE `user` ADD COLUMN `corp_org_id` INT UNSIGNED DEFAULT NULL COMMENT '企业id';
ALTER TABLE `user` ADD COLUMN `corp_name` VARCHAR(64) DEFAULT NULL COMMENT '企业名称';
ALTER TABLE `user` ADD COLUMN `user_review_info` VARCHAR(1024) DEFAULT NULL COMMENT '审核信息';

ALTER TABLE `user` ADD INDEX idx_login_id_deleted (login_id, deleted);
ALTER TABLE `user` ADD INDEX idx_mobile (mobile);
ALTER TABLE `user` ADD INDEX idx_role_type (role_type);
ALTER TABLE `user` ADD INDEX idx_nick_name (nick_name);
ALTER TABLE `user` ADD INDEX idx_deleted (deleted);
ALTER TABLE `user` ADD INDEX idx_user_type (user_type);
ALTER TABLE `user` ADD INDEX idx_status (STATUS);
ALTER TABLE `user` ADD INDEX idx_master_id (master_id);
ALTER TABLE `user` ADD INDEX idx_user_id_deleted (id, deleted);

-- 用户算力点表
DROP TABLE IF EXISTS `user_point`;
CREATE TABLE IF NOT EXISTS `user_point`(
`id` INT UNSIGNED AUTO_INCREMENT COMMENT 'id',
`user_id` INT UNSIGNED NOT NULL COMMENT '用户id',
`point` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '算力点',
`give_point` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '赠送点',
`experience_point` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '体验算力点',
`create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
`modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRIMARY KEY ( `id` )
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
COMMENT '用户算力点表';

ALTER TABLE `user_point` ADD UNIQUE INDEX idx_user_id (user_id);
-- ALTER TABLE `user_point` ADD COLUMN `experience_point` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '体验算力点';
-- ALTER TABLE `user_point` ADD COLUMN `give_point` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '赠送点';

-- 用户算力流水表
DROP TABLE IF EXISTS `user_point_log`;
CREATE TABLE IF NOT EXISTS `user_point_log`(
`id` INT UNSIGNED AUTO_INCREMENT COMMENT 'id',
`user_id` INT UNSIGNED NOT NULL COMMENT '用户id',
`type` VARCHAR(32) NOT NULL COMMENT '类型，充值单、服装建模、套餐外创、创作退回作等',
`related_id` INT UNSIGNED NULL COMMENT '关联id',
`point` INT NULL COMMENT '算力点',
`give_point` INT NULL COMMENT '赠送点数',
`experience_point` INT NULL COMMENT '体验算力点',
`model_point` INT NULL COMMENT '服装套餐内算力点',
`operator_id` INT UNSIGNED NULL COMMENT '操作id',
`memo` VARCHAR(256) NULL COMMENT '备注',
`ext_info` JSON NULL COMMENT '扩展信息',
`create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
`modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRIMARY KEY ( `id` )
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
COMMENT '用户算力流水表';

ALTER TABLE `user_point_log` ADD INDEX idx_user_id (user_id);
ALTER TABLE `user_point_log` ADD INDEX idx_type (type);
ALTER TABLE `user_point_log` ADD INDEX idx_related (related_id);
ALTER TABLE `user_point_log` ADD INDEX idx_operator_id (operator_id);
-- ALTER TABLE `user_point_log` MODIFY COLUMN `point` INT NULL COMMENT '算力点';
-- ALTER TABLE `user_point_log` MODIFY COLUMN `model_point` INT NULL COMMENT '服装套餐内算力点';

-- 模型套餐积分表
DROP TABLE IF EXISTS `model_point`;
CREATE TABLE IF NOT EXISTS `model_point`(
`id` INT UNSIGNED AUTO_INCREMENT COMMENT 'id',
`user_id` INT UNSIGNED NOT NULL COMMENT '用户id',
`model_id` INT UNSIGNED NOT NULL COMMENT '服装模型id',
`point` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '图片点数',
`create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
`modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRIMARY KEY ( `id` )
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
COMMENT '模型套餐积分表';

ALTER TABLE `model_point` ADD UNIQUE INDEX idx_user_model (user_id, model_id);

-- 素材流水表
DROP TABLE IF EXISTS `material_info`;
CREATE TABLE IF NOT EXISTS `material_info`(
`id` INT UNSIGNED AUTO_INCREMENT COMMENT 'id',
`name` VARCHAR(64) NOT NULL COMMENT '名称',
`type` VARCHAR(32) NOT NULL COMMENT '类型',
`sub_type` VARCHAR(32) NOT NULL COMMENT '子类型',
`material_detail` TEXT NOT NULL COMMENT '素材详情，不同类型的素材类型不一样',
`ext_info` VARCHAR(1024) NULL COMMENT '扩展信息，是否生成背面照等',
`user_id` INT UNSIGNED NULL COMMENT '归属主账号id',
`operator_id` INT UNSIGNED NOT NULL COMMENT '操作者id',
`deleted` BOOLEAN NOT NULL COMMENT '是否删除，0未删除、1删除',
`create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
`modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRIMARY KEY ( `id` )
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
AUTO_INCREMENT = 100000
COMMENT '素材流水表';

drop table if exists `comfyui_task`;
create table `comfyui_task` (
`id` INT UNSIGNED AUTO_INCREMENT COMMENT '模型训练任务id',
`user_id` INT UNSIGNED NULL COMMENT '归属主账号id',
`operator_id` INT UNSIGNED NULL COMMENT '操作人账号id',
`task_type` VARCHAR(32) NOT NULL COMMENT '任务类型, cutout/mark-label/lora',
`task_status` VARCHAR(32) NOT NULL COMMENT '任务状态, QUEUED/RUNNING/COMPLETED/FAILED/UNKNOWN/NONE',
`req_params` varchar(8192) NULL COMMENT '自定义请求参数',
`comfyui_request` TEXT NULL COMMENT 'comfyui api请求报文',
`prompt_id` VARCHAR(64) NULL COMMENT 'ComfyUI返回的唯一标识',
`ret_detail` TEXT NULL COMMENT '结果详情',
`ext_info` varchar(1024) NULL COMMENT '扩展',
`create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
`modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRIMARY KEY ( `id` )
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
AUTO_INCREMENT = 100000
COMMENT 'comfyui任务表';

-- 素材模型表
DROP TABLE IF EXISTS `material_model`;
CREATE TABLE IF NOT EXISTS `material_model`(
`id` INT UNSIGNED AUTO_INCREMENT COMMENT 'id',
`name` VARCHAR(64) NOT NULL COMMENT '名称',
`type` VARCHAR(32) NOT NULL DEFAULT 'CUSTOM' COMMENT '类型，SYSTEM、CUSTOM',
`user_id` INT UNSIGNED NULL COMMENT '归属主账号id',
`show_image` VARCHAR(256) NULL COMMENT '展示图url',
`lora_name` VARCHAR(256) NULL COMMENT 'lora名称',
`tags` TEXT NULL COMMENT '标签列表',
`ext_info` JSON NULL COMMENT '扩展信息',
`status` VARCHAR(32) NOT NULL DEFAULT 'ENABLED' COMMENT '状态，ENABLED、DISABLED',
`operator_id` INT UNSIGNED NOT NULL COMMENT '操作者id',
`deleted` BOOLEAN NOT NULL COMMENT '是否删除，0未删除、1删除',
`create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
`modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRIMARY KEY ( `id` )
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
AUTO_INCREMENT = 100000
COMMENT '素材模型表';

ALTER TABLE `material_model` ADD INDEX idx_user_id_deleted (user_id, deleted);
ALTER TABLE `material_model` ADD INDEX idx_status (status);
ALTER TABLE `material_model` ADD COLUMN `train_detail` VARCHAR(4096) NULL COMMENT '训练详情';
ALTER TABLE `material_model` MODIFY COLUMN `show_image` VARCHAR(512) NULL COMMENT '图片url';
ALTER TABLE `material_model` MODIFY COLUMN `train_detail` JSON NULL COMMENT '训练详情';


-- 创作元素表，分级树形结构
DROP TABLE IF EXISTS `creative_element`;
CREATE TABLE IF NOT EXISTS `creative_element`(
`id` INT UNSIGNED AUTO_INCREMENT COMMENT '元素id',
`name` VARCHAR(64) NOT NULL COMMENT '名称',
`config_key` VARCHAR(64) NOT NULL COMMENT '配置关键字，同一级配置key一致，一级要求不一致,如FACE、SCENE',
`level` INT UNSIGNED NOT NULL COMMENT '层级',
`parent_id` INT UNSIGNED NULL COMMENT '父级id',
`show_image` VARCHAR(256) NULL COMMENT '图片url',
`status` VARCHAR(16) NOT NULL DEFAULT 'PROD' COMMENT '状态，测试、上线',
`order` INT UNSIGNED NOT NULL COMMENT '排序',
`tags` TEXT NULL COMMENT '标签列表',
`ext_tags` TEXT NULL COMMENT '扩展标签列表',
`ext_info` JSON NULL COMMENT '扩展信息',
`memo` VARCHAR(256) NULL COMMENT '备注',
`type` VARCHAR(32) NULL COMMENT '业务类型，如果有多个则用逗号隔开',
`belong` VARCHAR(16) NOT NULL DEFAULT 'SYSTEM' COMMENT '归属，SYSTEM、CUSTOM',
`user_id` INT UNSIGNED NULL COMMENT '归属主账号',
`operator_id` INT UNSIGNED NULL COMMENT '操作员id',
`deleted` BOOLEAN NOT NULL COMMENT '是否删除，0未删除、1删除',
`create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
`modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRIMARY KEY ( `id` )
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
COMMENT '创作元素表';

ALTER TABLE `creative_element` MODIFY `type` VARCHAR(2048) NULL COMMENT '业务类型，如果有多个则用逗号隔开';
-- ALTER TABLE `creative_element` ADD COLUMN `status` VARCHAR(16) NOT NULL DEFAULT 'PROD' COMMENT '状态，测试、上线';
-- ALTER TABLE `creative_element` ADD COLUMN `belong` VARCHAR(16) NOT NULL DEFAULT 'SYSTEM' COMMENT '归属，SYSTEM、CUSTOM';
-- ALTER TABLE `creative_element` ADD COLUMN `user_id` INT UNSIGNED NULL COMMENT '归属主账号';
-- ALTER TABLE `creative_element` ADD COLUMN `operator_id` INT UNSIGNED NULL COMMENT '操作员id';
ALTER TABLE `creative_element` MODIFY COLUMN `show_image` VARCHAR(512) NULL COMMENT '图片url';
ALTER TABLE `creative_element` MODIFY COLUMN `type` VARCHAR(128) NULL COMMENT '业务类型，如果有多个则用逗号隔开';

-- 创作批次表
DROP TABLE IF EXISTS `creative_batch`;
CREATE TABLE IF NOT EXISTS `creative_batch`(
`id` INT UNSIGNED AUTO_INCREMENT COMMENT '批次id',
`model_id` INT UNSIGNED NOT NULL COMMENT '模型id',
`model_type` VARCHAR(32) NOT NULL DEFAULT 'CUSTOM' COMMENT '模型类型，SYSTEM、CUSTOM',
`type` VARCHAR(32) NOT NULL DEFAULT 'CREATE_IMAGE' COMMENT '类型，CREATE_IMAGE、REPAIR_HANDS',
`user_id` INT UNSIGNED NULL COMMENT '归属主账号id',
`show_image` VARCHAR(256) NULL COMMENT '图片url',
`image_proportion` VARCHAR(16) NOT NULL COMMENT '图片比例，3:4、1:1等',
`batch_cnt` INT UNSIGNED NULL COMMENT '批次数量',
`aigc_request` JSON NULL COMMENT 'aigc请求参数',
`prompt_id` VARCHAR(64) NULL COMMENT 'ComfyUI返回的唯一标识',
`result_images` TEXT NULL COMMENT '结果图片url列表',
`result_path` VARCHAR(256) NULL COMMENT '结果图片路径',
`ext_info` JSON NULL COMMENT '扩展信息',
`status` VARCHAR(32) NOT NULL DEFAULT 'INIT' COMMENT '状态，INIT、QUEUE、PROCESSING、FINISHED、FAILED',
`operator_id` INT UNSIGNED NOT NULL COMMENT '操作者id',
`deleted` BOOLEAN NOT NULL COMMENT '是否删除，0未删除、1删除',
`create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
`modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRIMARY KEY ( `id` )
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
AUTO_INCREMENT = 100000
COMMENT '创作批次表';

ALTER TABLE `creative_batch` ADD INDEX idx_model_id_deleted (model_id, deleted);
ALTER TABLE `creative_batch` ADD INDEX idx_user_id_deleted (user_id, deleted);
ALTER TABLE `creative_batch` ADD INDEX idx_status_deleted (status, deleted);
ALTER TABLE `creative_batch` ADD INDEX idx_prompt_id_deleted (prompt_id, deleted);
ALTER TABLE `creative_batch` ADD INDEX idx_model_type (model_type);
-- ALTER TABLE `creative_batch` ADD COLUMN `model_type` VARCHAR(32) NOT NULL DEFAULT 'CUSTOM' COMMENT '类型，SYSTEM、CUSTOM';
-- ALTER TABLE `creative_batch` ADD COLUMN `type` VARCHAR(32) NOT NULL DEFAULT 'CREATE_IMAGE' COMMENT '类型，CREATE_IMAGE、REPAIR_HANDS';
ALTER TABLE `creative_batch` ADD INDEX idx_type (type);
ALTER TABLE `creative_batch` MODIFY COLUMN `show_image` VARCHAR(512) NULL COMMENT '图片url';
ALTER TABLE `creative_batch` MODIFY COLUMN `model_id` INT UNSIGNED NULL COMMENT '模型id';
ALTER TABLE `creative_batch` MODIFY COLUMN `model_type` VARCHAR(32) NULL DEFAULT 'CUSTOM' COMMENT '模型类型，SYSTEM、CUSTOM';
ALTER TABLE `creative_batch` ADD COLUMN `title` VARCHAR(32) NULL COMMENT '标题';


DROP TABLE IF EXISTS `creative_task`;
CREATE TABLE IF NOT EXISTS `creative_task`(
`id` INT UNSIGNED AUTO_INCREMENT COMMENT '任务id',
`batch_id` INT UNSIGNED NOT NULL COMMENT '批次id',
`user_id` INT UNSIGNED NULL COMMENT '归属主账号id',
`model_id` INT UNSIGNED NOT NULL COMMENT '模型id',
`type` VARCHAR(32) NOT NULL DEFAULT 'CREATE_IMAGE' COMMENT '类型，CREATE_IMAGE、REPAIR_HANDS',
`image_proportion` VARCHAR(16) NOT NULL COMMENT '图片比例，3:4、1:1等',
`batch_cnt` INT UNSIGNED NULL COMMENT '批次数量',
`aigc_request` JSON NULL COMMENT 'aigc请求参数',
`prompt_id` VARCHAR(64) NULL COMMENT 'ComfyUI返回的唯一标识',
`result_images` TEXT NULL COMMENT '结果图片url列表',
`result_path` VARCHAR(256) NULL COMMENT '结果图片路径',
`ext_info` JSON NULL COMMENT '扩展信息',
`status` VARCHAR(32) NOT NULL DEFAULT 'INIT' COMMENT '状态，INIT、QUEUE、PROCESSING、FINISHED、FAILED',
`operator_id` INT UNSIGNED NOT NULL COMMENT '操作者id',
`deleted` BOOLEAN NOT NULL COMMENT '是否删除，0未删除、1删除',
`create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
`modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRIMARY KEY ( `id` )
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
COMMENT '创作任务表';

ALTER TABLE `creative_task` ADD INDEX idx_batch_id_deleted (batch_id, deleted);
ALTER TABLE `creative_task` ADD INDEX idx_user_id_deleted (user_id, deleted);
ALTER TABLE `creative_task` ADD INDEX idx_status_deleted (status, deleted);
ALTER TABLE `creative_task` ADD INDEX idx_prompt_id (prompt_id);
-- ALTER TABLE `creative_task` ADD COLUMN `type` VARCHAR(32) NOT NULL DEFAULT 'CREATE_IMAGE' COMMENT '类型，CREATE_IMAGE、REPAIR_HANDS';
ALTER TABLE `creative_task` ADD INDEX idx_type (type);

-- 创作批次属性表
DROP TABLE IF EXISTS `creative_batch_elements`;
CREATE TABLE IF NOT EXISTS `creative_batch_elements`(
`id` INT UNSIGNED AUTO_INCREMENT COMMENT '批次id',
`batch_id` INT UNSIGNED NOT NULL COMMENT '模型id',
`element_id` INT UNSIGNED NOT NULL COMMENT '元素id',
`element_key` VARCHAR(64) NULL COMMENT '元素关键字',
`user_id` INT UNSIGNED NULL COMMENT '归属主账号id',
`operator_id` INT UNSIGNED NULL COMMENT '操作者账号id',
`create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
`modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRIMARY KEY ( `id` )
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
COMMENT '创作批次属性表';

ALTER TABLE `creative_batch_elements` ADD INDEX idx_batch_id (batch_id);
-- ALTER TABLE `creative_batch_elements` ADD COLUMN operator_id INT UNSIGNED NULL COMMENT '操作者账号id';
-- ALTER TABLE `creative_batch_elements` ADD COLUMN `element_key` VARCHAR(64) NULL COMMENT '元素关键字';
ALTER TABLE `creative_batch_elements` ADD INDEX idx_user_id (user_id);
ALTER TABLE `creative_batch_elements` ADD INDEX idx_operator_id (operator_id);
ALTER TABLE `creative_batch_elements` ADD INDEX idx_element_key (element_key);

-- 标签配置表
DROP TABLE IF EXISTS `tags`;
CREATE TABLE IF NOT EXISTS `tags`(
`id` INT UNSIGNED AUTO_INCREMENT COMMENT '标签id',
`type` VARCHAR(64) NOT NULL COMMENT '标签类型',
`name` VARCHAR(64) NOT NULL COMMENT '标签名称',
`detail` TEXT NULL COMMENT '标签详情',
`def_checked` BOOLEAN NOT NULL DEFAULT 0 COMMENT '是否默认选中，0不选中、1默认选中',
`create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
`modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRIMARY KEY ( `id` )
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
COMMENT '标签配置表';

-- 视图-----------------------------------------------------------------------------------------------------------------

-- 素材模型视图
CREATE OR REPLACE VIEW `material_model_view` AS
select t.*,
	u.nick_name AS `operator_nick`,
    v.nick_name AS `user_nick`
from (
	SELECT *
	FROM `material_model`
	WHERE deleted = 0
) t
LEFT JOIN `user` u ON t.`operator_id` = u.`id`
LEFT JOIN `user` v ON t.`user_id` = v.`id`

-- 创作批次视图
create or replace view `creative_batch_view` as
select
  c.*,
  u.nick_name as `operator_nick`,
  mu.nick_name as `user_nick`,
  m.`name` as `model_name`,
  m.`show_image` as `model_show_img`,
  m.`ext_info` as `model_ext_info`
from
  `creative_batch` c
  left join `material_model` m on c.`model_id` = m.`id`
  left join `user` u on c.`operator_id` = u.`id`
  left join `user` mu on c.`user_id` = mu.`id`
where
  c.deleted = 0
  and u.deleted = 0;

-- -------------其他系统性配置---------------

-- 权限配置表
DROP TABLE IF EXISTS `permission`;
CREATE TABLE IF NOT EXISTS `permission`(
`id` INT UNSIGNED AUTO_INCREMENT COMMENT 'id',
`action` VARCHAR(128) NOT NULL COMMENT '执行动作',
`name` VARCHAR(64) NOT NULL COMMENT '权限名称',
`config` VARCHAR(256) NULL COMMENT '权限配置',
`allowed_sub` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否允许子账号执行',
`memo` VARCHAR(256) NULL COMMENT '备注',
`create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
`modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRIMARY KEY ( `id` ),
UNIQUE KEY ( `action` )
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
COMMENT '权限配置表';

-- 系统配置表（需要调度）
DROP TABLE IF EXISTS `system_config`;
CREATE TABLE IF NOT EXISTS `system_config`(
`id` INT UNSIGNED AUTO_INCREMENT COMMENT 'id',
`conf_key` VARCHAR(128) NOT NULL COMMENT '配置key',
`conf_value` TEXT NOT NULL COMMENT '配置值，状态非0时直接取该值',
`conf_value_next` TEXT NULL COMMENT '执行变更后的配置值',
`status` VARCHAR(16) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态，INVALID失效、ACTIVE生效、PENDING_CHANGE待变更',
`effect_time` TIMESTAMP NOT NULL COMMENT '变更生效时间' DEFAULT CURRENT_TIMESTAMP,
`memo` VARCHAR(128) NULL COMMENT '备注',
`operator_id` INT NOT NULL COMMENT '操作者id',
`create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
`modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRIMARY KEY ( `id` ),
UNIQUE KEY ( `conf_key` )
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
COMMENT '系统配置表';

-- 用户操作记录表
DROP TABLE IF EXISTS `user_op_log`;
CREATE TABLE `user_op_log` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `master_user_id` int unsigned NOT NULL COMMENT '主账号用户id',
  `operator_user_id` int NOT NULL COMMENT '操作人用户id',
  `role_type` varchar(32) NOT NULL COMMENT '角色类型',
  `op_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作行为，新增、修改、废弃',
  `op_biz_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '商品、订单、赊账、收入、支出、还款、客户、子账号',
  `op_biz_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作目标id',
  `detail_before` json DEFAULT NULL COMMENT '变更前明细',
  `detail_after` json DEFAULT NULL COMMENT '变更后明细',
  `memo` varchar(128) DEFAULT NULL COMMENT '备注',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_user_id` (`operator_user_id`) USING BTREE,
  KEY `idx_op_biz_type` (`op_biz_type`) USING BTREE,
  KEY `idx_op_type` (`op_type`) USING BTREE,
  KEY `idx_master_uid` (`master_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户操作记录表'
;

-- 用户属性表
drop table if exists `user_profile`;
CREATE TABLE IF NOT EXISTS `user_profile`(
    `id` INT UNSIGNED AUTO_INCREMENT COMMENT 'id',
    `uid` INT UNSIGNED NOT NULL COMMENT '关联的用户id',
    `profile_key` VARCHAR(64) NOT NULL COMMENT '属性key',
    `profile_val` VARCHAR(1024) NULL COMMENT '属性val',
    `create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
    `modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uid_profile_key_unique` (`uid`, `profile_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
COMMENT '用户属性表';

-- 服务器配置表
drop table if exists `server`;
CREATE TABLE IF NOT EXISTS `server`(
    `id` INT UNSIGNED AUTO_INCREMENT COMMENT 'id',
    `name` VARCHAR(64) NOT NULL COMMENT '服务名称',
    `level` INT UNSIGNED NOT NULL COMMENT '等级，一级为服务器，二级为具体端口',
    `config` VARCHAR(128) NOT NULL COMMENT '配置值',
    `type` VARCHAR(16) NULL COMMENT '类型，生图、Lora训练、文件服务',
    `status` VARCHAR(16) NOT NULL COMMENT '状态，可用、禁用、空闲、繁忙',
    `parent_id` INT UNSIGNED NULL COMMENT '父节点id',
    `pipeline_id` INT UNSIGNED NULL COMMENT '管道id',
    `deleted` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',
    `create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
    `modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
COMMENT '服务器配置表';

ALTER table `server` MODIFY COLUMN `type` VARCHAR(16) NULL COMMENT '类型，生图、Lora训练、文件服务';
-- ALTER table `server` ADD COLUMN `status` VARCHAR(16) NOT NULL COMMENT '状态，可用、禁用、空闲、繁忙';
ALTER table `server` ADD COLUMN `config_alias` VARCHAR(128) NULL COMMENT '服务地址别名';

-- 服务管道表
drop table if exists `pipeline`;
CREATE TABLE IF NOT EXISTS `pipeline`(
    `id` INT UNSIGNED AUTO_INCREMENT COMMENT 'id',
    `name` VARCHAR(64) NOT NULL COMMENT '管道名称',
    `memo` VARCHAR(128) NULL COMMENT '备注',
    `user_relation` JSON NULL COMMENT '用户关联配置',
    `deleted` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',
    `create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
    `modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
COMMENT '服务管道表';

-- ALTER table `pipeline` ADD COLUMN `memo` VARCHAR(128) NULL COMMENT '备注';

drop table if exists `order_info`;
CREATE TABLE IF NOT EXISTS `order_info`(
    `id` INT UNSIGNED AUTO_INCREMENT COMMENT 'id',
    `order_no` VARCHAR(32) NOT NULL COMMENT '订单号',
    `master_user_id` INT UNSIGNED NOT NULL COMMENT '关联的主用户id',
    `master_user_nick` VARCHAR(32) NOT NULL COMMENT '关联的主用户昵称快照',
    `master_user_login_id` VARCHAR(32) NOT NULL COMMENT '关联的主用户登录账号快照',
    `operator_user_id` INT UNSIGNED NOT NULL COMMENT '关联的操作员用户id',
    `operator_user_nick` VARCHAR(32) NOT NULL COMMENT '关联的操作员用户昵称快照',
    `operator_user_login_id` VARCHAR(32) NOT NULL COMMENT '关联的操作员用户登录账号快照',
    `original_amount` DECIMAL(20,8) NOT NULL COMMENT '订单原始金额，不可修改（单位元）',
    `pay_amount` DECIMAL(20,8) NOT NULL COMMENT '订单支付金额',
    `pay_detail` VARCHAR(1024) NULL COMMENT '支付信息',
    `order_status` VARCHAR(32) NOT NULL COMMENT '订单状态',
    `product_code` VARCHAR(32) NOT NULL COMMENT '产品码',
    `product_detail` VARCHAR(4096) NULL COMMENT '产品详情',
    `finish_time` TIMESTAMP NULL COMMENT '订单完结时间',
    `ext_info` VARCHAR(1024) NULL COMMENT '扩展信息',
    `deleted` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',
    `create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
    `modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE INDEX `udx_order_no` (`order_no`),
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
COMMENT '订单表';

drop table if exists `invoice_info`;
create table if not exists `invoice_info`(
    `id` INT UNSIGNED AUTO_INCREMENT COMMENT 'id',
    `master_user_id` INT UNSIGNED NOT NULL COMMENT '关联的主用户id',
    `master_user_nick` VARCHAR(32) NOT NULL COMMENT '关联的主用户昵称快照',
    `master_user_login_id` VARCHAR(32) NOT NULL COMMENT '关联的主用户登录账号快照',
    `operator_user_id` INT UNSIGNED NOT NULL COMMENT '关联的操作员用户id',
    `operator_user_nick` VARCHAR(32) NOT NULL COMMENT '关联的操作员用户昵称快照',
    `operator_user_login_id` VARCHAR(32) NOT NULL COMMENT '关联的操作员用户登录账号快照',
    `invoice_type` VARCHAR(32) NOT NULL COMMENT '发票类型，普票｜专票',
    `subject_type` VARCHAR(32) NOT NULL COMMENT '发票抬头类型，个人｜企业',
    `subject_name` VARCHAR(32) NOT NULL COMMENT '发票抬头',
    `credit_code` VARCHAR(32) NULL COMMENT '统一社会信用代码',
    `business_address` VARCHAR(1024) NULL COMMENT '办公地址',
    `business_phone` VARCHAR(1024) NULL COMMENT '办公电话',
    `bank_name` VARCHAR(100) NULL COMMENT '开户银行',
    `bank_account` VARCHAR(100) NULL COMMENT '银行账号',
    `status` VARCHAR(32) NOT NULL COMMENT '发票状态，未开票｜开票中｜已开票',
    `apply_time` TIMESTAMP NOT NULL COMMENT '申请时间',
    `finish_time` TIMESTAMP NULL COMMENT '完成时间',
    `invoice_no` VARCHAR(32) NULL COMMENT '发票号',
    `amount_no_tax` DECIMAL(20,6) NOT NULL COMMENT '不含税发票金额',
    `tax_rate` DECIMAL(20,6) NOT NULL COMMENT '税率，小数形式',
    `tax_amount` DECIMAL(20,6) NOT NULL COMMENT '税额',
    `amount_with_tax` DECIMAL(20,6) NOT NULL COMMENT '含税发票金额',
    `invoice_task_third_platform` VARCHAR(32) NULL COMMENT '外部发票平台名称',
    `invoice_task_third_req_id` VARCHAR(64) NULL COMMENT '外部发票任务id',
    `invoice_task_detail` VARCHAR(4096) COMMENT '发票任务详情',
    `invoice_download_url` VARCHAR(2048) NULL COMMENT '发票文件下载地址',
    `memo` VARCHAR(100) NULL COMMENT '备注',
    `ext_info` VARCHAR(1024) NULL COMMENT '扩展信息',
    `deleted` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',
    `create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
    `modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE INDEX `udx_uid_invoice_no` (`master_user_id`, `invoice_no`),
    KEY `idx_operator_id` (`operator_user_id`) USING BTREE,
    KEY `idx_master_id` (`master_user_id`) USING BTREE,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
  COMMENT '发票记录';

drop table if exists `invoice_title`;
create table if not exists `invoice_title`(
    `id` INT UNSIGNED AUTO_INCREMENT COMMENT 'id',
    `master_user_id` INT UNSIGNED NOT NULL COMMENT '关联的主用户id',
    `operator_user_id` INT UNSIGNED NOT NULL COMMENT '关联的操作员用户id',
    `invoice_type` VARCHAR(32) NOT NULL COMMENT '发票类型，普票｜专票',
    `subject_type` VARCHAR(32) NOT NULL COMMENT '发票抬头类型，个人｜企业',
    `subject_name` VARCHAR(32) NOT NULL COMMENT '发票抬头',
    `credit_code` VARCHAR(32) NULL COMMENT '统一社会信用代码',
    `business_address` VARCHAR(1024) NULL COMMENT '办公地址',
    `business_phone` VARCHAR(1024) NULL COMMENT '办公电话',
    `bank_name` VARCHAR(100) NULL COMMENT '开户银行',
    `bank_account` VARCHAR(100) NULL COMMENT '银行账号',
    `ext_info` VARCHAR(1024) NULL COMMENT '扩展信息',
    `deleted` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',
    `create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
    `modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    KEY `idx_operator_id` (`operator_user_id`) USING BTREE,
    KEY `idx_master_id` (`master_user_id`) USING BTREE,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
  COMMENT '发票抬头';

drop table if exists `invoice_order` ;
create table if not exists `invoice_order`(
    `id` INT UNSIGNED AUTO_INCREMENT COMMENT 'id',
    `user_id` INT UNSIGNED NOT NULL COMMENT '关联的用户id',
    `operator_id` INT UNSIGNED NOT NULL COMMENT '关联的操作员用户id',
    `invoice_id` INT UNSIGNED NOT NULL COMMENT '关联的开票id',
    `order_id` INT UNSIGNED NOT NULL COMMENT '关联的订单id',
    `memo` VARCHAR(100) NULL COMMENT '备注',
    `ext_info` VARCHAR(1024) NULL COMMENT '扩展信息',
    `deleted` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',
    `create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
    `modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    KEY `idx_operator_id` (`operator_id`) USING BTREE,
    KEY `idx_user_id` (`user_id`) USING BTREE,
    KEY `idx_invoice_id` (`invoice_id`) USING BTREE,
    KEY `idx_order_id` (`order_id`) USING BTREE,
    UNIQUE INDEX `udx_order_invoice` (`invoice_id`, `order_id`),
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
  COMMENT '发票订单关联';

DROP TABLE IF EXISTS `distributor_customer`;
CREATE TABLE IF NOT EXISTS `distributor_customer`(
`id` INT UNSIGNED AUTO_INCREMENT COMMENT 'id',
`customer_master_user_id` INT UNSIGNED NOT NULL COMMENT '客户主账号id',
`distributor_corp_name` VARCHAR(128) NOT NULL COMMENT '渠道商实体名称',
`distributor_corp_org_id` INT UNSIGNED NOT NULL COMMENT '渠道商实体组织id',
`distributor_master_user_id` INT UNSIGNED NOT NULL COMMENT '渠道商主账号id',
`distributor_operator_user_id` INT UNSIGNED NULL COMMENT '渠道商运营人员id',
`distributor_sales_user_id` INT UNSIGNED NULL COMMENT '渠道商销售人员id',
`creator_id` INT UNSIGNED NOT NULL COMMENT '创建人id',
`ext_info` VARCHAR(1024) NULL COMMENT '扩展信息',
`deleted` BOOLEAN NOT NULL DEFAULT false COMMENT '是否删除，0未删除、1删除',
`create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
`modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
UNIQUE INDEX `udx_distributor_customer` (`distributor_master_user_id`, `customer_master_user_id`),
PRIMARY KEY ( `id` )
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
COMMENT '渠道商客户信息表';

DROP TABLE IF EXISTS `organization`;
CREATE TABLE IF NOT EXISTS `organization` (
  `id` INT UNSIGNED AUTO_INCREMENT COMMENT '组织ID',
  `parent_id` INT UNSIGNED DEFAULT NULL COMMENT '父级组织ID，根组织为0',
  `root` BOOLEAN NOT NULL DEFAULT false COMMENT '是否根结点组织，0不是，1是',
  `root_id` INT UNSIGNED NULL COMMENT '根组织ID，根组织ID等于ID,根组织有且只有一个',
  `org_type` VARCHAR(32) NOT NULL COMMENT '组织类型，DISTRIBUTOR_CORP：渠道商企业｜DISTRIBUTOR_DEPT：渠道商部门',
  `name` VARCHAR(64) NOT NULL COMMENT '组织名称',
  `tags` VARCHAR(256) DEFAULT NULL COMMENT '组织标签，作为组织类型的补充，预留',
  `org_level` INT UNSIGNED NOT NULL COMMENT '组织层级，根组织为0，根组织下有1级组织，1级组织下有2级组织，以此类推',
  `creator_master_user_id` INT UNSIGNED NOT NULL COMMENT '创建者主账号id',
  `creator_user_role_type` VARCHAR(32) NOT NULL COMMENT '创建人角色类型，DISTRIBUTOR：渠道商',
  `creator_operator_user_id` INT UNSIGNED NOT NULL COMMENT '创建人操作员账号id',
  `modifier_operator_user_id` INT UNSIGNED NOT NULL COMMENT '最近修改人的操作员账号id',
  `ext_info` VARCHAR(1024) NULL COMMENT '扩展信息',
  `create_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  KEY `idx_parent_id` (`parent_id`) USING BTREE,
  UNIQUE INDEX `udx_creator_name` (`creator_master_user_id`, `name`),
  PRIMARY KEY ( `id` )
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
COMMENT '组织';

DROP TABLE IF EXISTS `user_organization`;
CREATE TABLE IF NOT EXISTS `user_organization` (
  `id` INT UNSIGNED AUTO_INCREMENT COMMENT 'id',
  `user_id` INT UNSIGNED NOT NULL COMMENT '用户（主账号或操作员）id',
  `org_id` INT UNSIGNED NOT NULL COMMENT '组织id',
  `creator_operator_user_id` INT UNSIGNED NOT NULL COMMENT '创建人操作员账号id',
  `modifier_operator_user_id` INT UNSIGNED NOT NULL COMMENT '修改人操作员账号id',
  `ext_info` VARCHAR(1024) NULL COMMENT '扩展信息',
  `create_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  UNIQUE INDEX `udx_org_user` (`org_id`, `user_id`),
  PRIMARY KEY ( `id` )
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
COMMENT '用户组织关系';

-- 订单结算表（流水），强一对一关系
DROP TABLE IF EXISTS `order_settlement`;
CREATE TABLE IF NOT EXISTS
  `order_settlement` (
    `id` INT UNSIGNED AUTO_INCREMENT COMMENT 'id',
    `order_no` VARCHAR(32) NOT NULL COMMENT '订单号',
    `distributor_corp_id` INT UNSIGNED NULL COMMENT '渠道商实体id',
    `distributor_corp_name` VARCHAR(128) NULL COMMENT '渠道商实体名称',
    `status` TINYINT(3) NOT NULL COMMENT '状态，0初始化、1订单关闭、2待结算、3结算中、4结算成功',
    `settle_id` VARCHAR(32) NULL COMMENT '结算id',
    `settle_time` TIMESTAMP NULL COMMENT '结算完成时间',
    `total_amount` DECIMAL(20, 8) NULL COMMENT '总金额',
    `channel_rate` DECIMAL(20, 8) NOT NULL COMMENT '渠道费率',
    `settle_amount` DECIMAL(20, 8) NULL COMMENT '结算金额',
    `ext_info` VARCHAR(1024) NULL COMMENT '扩展信息',
    `create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
    `modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    INDEX `idx_status` (`status`) USING BTREE,
    INDEX `idx_settle_id` (`settle_id`) USING BTREE,
    INDEX `idx_settle_time` (`settle_time`) USING BTREE,
    UNIQUE KEY (`order_no`)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT '订单结算表（流水）';

-- 渠道商结算明细表
DROP TABLE IF EXISTS `distributor_settlement`;
CREATE TABLE IF NOT EXISTS`distributor_settlement` (
    `id` INT UNSIGNED AUTO_INCREMENT COMMENT 'id',
    `settle_id` VARCHAR(32) NOT NULL COMMENT '结算id，前8位是YYYYMMDD',
    `status` TINYINT(3) NOT NULL COMMENT '状态，0初始化、1交易关闭、2待结算、3结算中、4结算成功、5结算失败',
    `settle_type` TINYINT(3) NOT NULL COMMENT '结算类型，1系统结算，1手工结算',
    `total_amount` DECIMAL(20, 8) NOT NULL COMMENT '总金额',
    `settle_amount` DECIMAL(20, 8) NOT NULL COMMENT '结算金额，结算给渠道商的总金额',
    `order_num` INT NOT NULL COMMENT '结算订单笔数',
    `out_biz_no` VARCHAR(32) NULL COMMENT '外部业务单号，如银行流水号',
    `distributor_corp_id` INT UNSIGNED NOT NULL COMMENT '渠道商实体id',
    `distributor_corp_name` VARCHAR(128) NOT NULL COMMENT '渠道商实体名称',
    `settle_time` TIMESTAMP NULL COMMENT '结算日期',
    `ext_info` VARCHAR(1024) NULL COMMENT '扩展信息',
    `deleted` BOOLEAN NOT NULL DEFAULT false COMMENT '是否删除，0未删除、1删除',
    `create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
    `modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    INDEX `idx_status` (`status`) USING BTREE,
    INDEX `idx_settle_id` (`settle_id`) USING BTREE,
    INDEX `idx_settle_time` (`settle_time`) USING BTREE,
    INDEX `idx_create_time` (`create_time`) USING BTREE,
    UNIQUE KEY (`settle_id`),
    UNIQUE KEY (`out_biz_no`)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT '商户结算明细表';

CREATE OR REPLACE VIEW user_pipeline_mapping AS
SELECT
    user_id,
    role_type,
    pipeline_id,
    match_type
FROM (
    SELECT
        u.id user_id,
        u.role_type,
        p.id pipeline_id,
        CASE
            WHEN JSON_CONTAINS(p.user_relation->'$.user', JSON_QUOTE(CAST(u.id AS CHAR))) THEN 'user'
            WHEN JSON_CONTAINS(p.user_relation->'$.role', JSON_QUOTE(u.role_type)) THEN 'role'
            WHEN JSON_UNQUOTE(p.user_relation->'$.isDefault') = 'true' THEN 'isDefault'
            ELSE NULL
        END AS match_type,
        ROW_NUMBER() OVER (PARTITION BY u.id ORDER BY
            FIELD(CASE
                    WHEN JSON_CONTAINS(p.user_relation->'$.user', JSON_QUOTE(CAST(u.id AS CHAR))) THEN 'user'
                    WHEN JSON_CONTAINS(p.user_relation->'$.role', JSON_QUOTE(u.role_type)) THEN 'role'
                    WHEN JSON_UNQUOTE(p.user_relation->'$.isDefault') = 'true' THEN 'isDefault'
                    ELSE NULL
                END, 'user', 'role', 'isDefault')) AS rn
    FROM
        user u
    JOIN
        pipeline p
    ON
        JSON_CONTAINS(p.user_relation->'$.user', JSON_QUOTE(CAST(u.id AS CHAR)))
        OR JSON_CONTAINS(p.user_relation->'$.role', JSON_QUOTE(u.role_type))
        OR JSON_UNQUOTE(p.user_relation->'$.isDefault') = 'true'
    where u.deleted != 1 and p.deleted != 1
) AS prioritized
WHERE
    rn = 1;


--
ALTER TABLE `creative_element` ADD COLUMN `lora_model_id` INT(64) DEFAULT NULL COMMENT 'lora model id';


DROP TABLE IF EXISTS `merchant_preference`;
CREATE TABLE IF NOT EXISTS `merchant_preference`(
`id` INT UNSIGNED AUTO_INCREMENT COMMENT 'id',
`user_id` INT UNSIGNED NOT NULL COMMENT '用户id',
`type` VARCHAR(32) NOT NULL COMMENT '类型，AUTO_DELIVERY、CREATIVE',
`tags` VARCHAR(128) NULL COMMENT '标签列表，多个以逗号隔开',
`memo` VARCHAR(1024) NULL COMMENT '备注',
`faces` VARCHAR(1024) NULL COMMENT '模特id配置',
`scenes` VARCHAR(1024) NULL COMMENT '场景id配置',
`cloth_collocation` JSON NULL COMMENT '服装搭配信息',
`operator_id` INT NOT NULL COMMENT '操作者id',
`deleted` BOOLEAN NOT NULL COMMENT '是否删除，0未删除、1删除',
`create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
`modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRIMARY KEY ( `id` )
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
COMMENT '商家偏好配置表';

ALTER TABLE `merchant_preference` ADD INDEX idx_user_id_deleted (user_id, deleted);
ALTER TABLE `merchant_preference` ADD INDEX idx_type (type);
ALTER TABLE `merchant_preference` ADD COLUMN `enable_auto_creative` BOOLEAN NULL COMMENT '是否开启自动创作';
ALTER TABLE `merchant_preference` ADD COLUMN `image_num` INT UNSIGNED NULL COMMENT '图片数量';
ALTER TABLE `merchant_preference` ADD COLUMN `image_proportion` VARCHAR(16) NULL COMMENT '图片比例，3:4、1:1等';
ALTER TABLE `merchant_preference` ADD COLUMN `ext_info` JSON NULL COMMENT '扩展信息';

DROP TABLE IF EXISTS `prompt_dict`;
CREATE TABLE IF NOT EXISTS `prompt_dict`(
`id` INT UNSIGNED AUTO_INCREMENT COMMENT 'id',
`word` VARCHAR(256) NOT NULL COMMENT '名词',
`prompt` VARCHAR(512) NOT NULL COMMENT '对应的prompt',
`type` VARCHAR(32) NOT NULL DEFAULT 'SYSTEM' COMMENT '类型，COMMON、CLOTH_COLLOCATION',
`show_image` VARCHAR(256) NULL COMMENT '展示图url',
`tags` VARCHAR(128) NULL COMMENT '标签列表，多个以逗号隔开',
`memo` VARCHAR(1024) NULL COMMENT '备注',
`user_id` INT NULL COMMENT '用户id',
`operator_id` INT NULL COMMENT '操作者id',
`create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
`modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRIMARY KEY ( `id` )
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
COMMENT 'prompt关键字字典表';

ALTER TABLE `prompt_dict` ADD INDEX idx_word (word);


-- 删除表 train_param，如果存在
DROP TABLE IF EXISTS train_param;

-- 删除表 train_plan，如果存在
DROP TABLE IF EXISTS train_plan;

-- 创建表 train_plan
CREATE TABLE train_plan (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    plan_name VARCHAR(255) NOT NULL COMMENT '训练计划名称',
    clothing_id int NOT NULL COMMENT '原始克隆服装ID',
    clothing_name VARCHAR(255) NOT NULL COMMENT '原始克隆服装名称',
    remarks VARCHAR(255) DEFAULT NULL COMMENT '备注信息',
    images_per_combination INT NULL COMMENT '每组图片数量',
    face_models VARCHAR(255) DEFAULT NULL COMMENT '模特 ID 列表，以逗号分隔',
    scenes VARCHAR(255) DEFAULT NULL COMMENT '场景 ID 列表，以逗号分隔',
    sizes VARCHAR(255) DEFAULT NULL COMMENT '出图尺寸列表，以逗号分隔',
    ext_info VARCHAR(255) DEFAULT NULL COMMENT '扩展信息',
    creator_user_id INT NOT NULL COMMENT '创建者用户ID',
    creator_user_name VARCHAR(255) NOT NULL COMMENT '创建者用户名',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modify_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建表 train_param
CREATE TABLE train_param (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    train_plan_id INT NOT NULL COMMENT '关联的训练计划ID',
    train_resolution VARCHAR(50) NOT NULL COMMENT '训练分辨率',
    content_or_style VARCHAR(50) NOT NULL COMMENT '学习内容或风格',
    related_lora_model_id int null comment '关联的loraModelId',
    related_lora_model_name varchar(64) null comment '关联的loraModelName',
    lora_rank INT NOT NULL COMMENT 'Rank值',
    alpha INT NOT NULL COMMENT 'Alpha值',
    train_step INT NOT NULL COMMENT '训练步数',
    lr DECIMAL(10, 4) NOT NULL COMMENT '学习率',
    dropout DECIMAL(3, 2) NOT NULL COMMENT 'Dropout值',
    ext_info VARCHAR(255) DEFAULT NULL COMMENT '扩展信息',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modify_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    FOREIGN KEY (train_plan_id) REFERENCES train_plan(id) ON DELETE CASCADE
);

DROP TABLE IF EXISTS `show_case`;
CREATE TABLE IF NOT EXISTS `show_case`(
`id` INT UNSIGNED AUTO_INCREMENT COMMENT 'id',
`name` VARCHAR(256) NULL COMMENT '案例名称',
`type` VARCHAR(32) NOT NULL DEFAULT 'IMAGE' COMMENT '类型，IMAGE、VIDEO',
`main_url` VARCHAR(256) NOT NULL COMMENT '结果图/视频地址url',
`show_image` VARCHAR(256) NULL COMMENT '展示图url',
`face_id` INT UNSIGNED NULL COMMENT '模特id',
`scene_id` INT UNSIGNED NULL COMMENT '场景id',
`model_id` INT UNSIGNED NULL COMMENT '服装模型id',
`model_url` VARCHAR(256) NULL COMMENT '服装url',
`model_mini_url` VARCHAR(256) NULL COMMENT '服装缩略图url',
`cloth_collocation` JSON NULL COMMENT '服装搭配信息',
`order` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
`topped` BOOLEAN NOT NULL DEFAULT 0 COMMENT '是否置顶，1置顶',
`tags` VARCHAR(256) NULL COMMENT '标签列表，多个以逗号隔开',
`status` VARCHAR(32) NOT NULL DEFAULT 'ENABLED' COMMENT '状态，ENABLED、DISABLED',
`memo` VARCHAR(1024) NULL COMMENT '备注',
`user_id` INT NULL COMMENT '用户id',
`operator_id` INT NULL COMMENT '操作者id',
`create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
`modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRIMARY KEY ( `id` )
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
COMMENT '优秀案例表';

ALTER TABLE `show_case` ADD INDEX idx_type (type);
ALTER TABLE `show_case` ADD INDEX idx_status (status);
-- ALTER TABLE `show_case` ADD COLUMN `topped` BOOLEAN NOT NULL DEFAULT 0 COMMENT '是否置顶，1置顶';
-- ALTER TABLE `show_case` ADD COLUMN `model_mini_url` VARCHAR(256) NULL COMMENT '服装缩略图url';


DROP TABLE IF EXISTS `image_case`;
CREATE TABLE IF NOT EXISTS `image_case`(
`id` INT UNSIGNED AUTO_INCREMENT COMMENT 'id',
`url` VARCHAR(256) NOT NULL COMMENT '图片url',
`mini_url` VARCHAR(256) NOT NULL COMMENT '缩略图片url',
`task_id` INT UNSIGNED NULL COMMENT '任务id',
`batch_id` INT UNSIGNED NULL COMMENT '批次id',
`store_path` VARCHAR(256) NULL COMMENT '存储地址',
`store_server` VARCHAR(256) NULL COMMENT '存储服务器',
`tags` VARCHAR(256) NULL COMMENT '标签列表，多个以逗号隔开',
`status` VARCHAR(32) NOT NULL DEFAULT 'ENABLED' COMMENT '状态，ENABLED、DISABLED',
`memo` VARCHAR(1024) NULL COMMENT '备注',
`user_id` INT NULL COMMENT '用户id',
`operator_id` INT NULL COMMENT '操作者id',
`create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
`modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRIMARY KEY ( `id` )
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
COMMENT '图片案例表';

ALTER TABLE `image_case` ADD UNIQUE INDEX idx_url (url);
ALTER TABLE `image_case` ADD INDEX idx_task_id (task_id);
ALTER TABLE `image_case` ADD INDEX idx_batch_id (batch_id);
ALTER TABLE `image_case` ADD INDEX idx_status (status);

ALTER TABLE `image_case` MODIFY COLUMN `tags` JSON NULL COMMENT '标签列表，jsonArray格式';
ALTER TABLE `image_case` DROP COLUMN `tags`


DROP TABLE IF EXISTS `image_case_tags`;
CREATE TABLE IF NOT EXISTS `image_case_tags`(
`id` INT UNSIGNED AUTO_INCREMENT COMMENT 'id',
`case_id` INT UNSIGNED NOT NULL COMMENT '图片案例id',
`tag_id` INT UNSIGNED NOT NULL COMMENT '标签id',
`create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
`modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRIMARY KEY ( `id` )
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
COMMENT '图片案例标签关联表';

ALTER TABLE `image_case_tags` ADD UNIQUE INDEX idx_case_tag (case_id, tag_id);
ALTER TABLE `image_case_tags` ADD INDEX idx_case_id (case_id);
ALTER TABLE `image_case_tags` ADD INDEX idx_tag_id (tag_id);


-- 添加同步状态字段
ALTER TABLE `image_case` ADD COLUMN `sync_status` BOOLEAN NOT NULL DEFAULT 0 COMMENT '同步状态';
ALTER TABLE `image_case` ADD COLUMN `sync_time` TIMESTAMP NULL DEFAULT NULL COMMENT '同步时间';
ALTER TABLE `image_case` ADD COLUMN `re_sync_count` INTEGER NULL DEFAULT 0 COMMENT '同步次数';


-- 12.11 添加 上新 字段
ALTER TABLE `creative_element` ADD COLUMN `is_new` BOOLEAN NOT NULL DEFAULT 0 COMMENT '是否为上新模特，1是，0否，默认为 0';


-- 图片案例同步记录表
DROP TABLE IF EXISTS `image_case_sync_record`;
CREATE TABLE `aigc-platform`.`image_case_sync_record`  (
`id` int NOT NULL AUTO_INCREMENT COMMENT '主键 id',
`case_id` int NOT NULL COMMENT '图片案例id',
`target_server` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '目标服务器 IP',
`target_store_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '目标存储目录',
`image_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '目标图片完整地址',
`upload_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '图片上传时间',
`is_success` tinyint NOT NULL DEFAULT 0 COMMENT '是否同步成功',
PRIMARY KEY (`id`) USING BTREE,
UNIQUE INDEX `udx_id`(`id` ASC) USING BTREE COMMENT '主键索引',
INDEX `idx_case_id`(`case_id` ASC) USING BTREE COMMENT '图片案例 id 索引',
INDEX `idx_image_url`(`image_url` ASC) USING BTREE COMMENT '图片地址索引'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;


-- AB测试计划表

DROP TABLE IF EXISTS `test_plan`;
CREATE TABLE IF NOT EXISTS `test_plan`(
`id` INT UNSIGNED AUTO_INCREMENT COMMENT 'id',
`name` VARCHAR(64) NULL COMMENT '测试计划名称',
`type` VARCHAR(32) NOT NULL DEFAULT 'CREATIVE' COMMENT '类型，TRAIN、CREATIVE',
`status` VARCHAR(32) NOT NULL DEFAULT 'ENABLED' COMMENT '状态，DISABLED、ENABLED、PROCESSING、COMPARING、FINISHED',
`memo` VARCHAR(1024) NULL COMMENT '备注',
`user_id` INT NULL COMMENT '归属用户id',
`operator_id` INT NULL COMMENT '操作者id',
`create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
`modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRIMARY KEY ( `id` )
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
COMMENT 'AB测试计划表';

ALTER TABLE `test_plan` ADD INDEX idx_type (`type`);
ALTER TABLE `test_plan` ADD INDEX idx_status (`status`);
ALTER TABLE `test_plan` ADD INDEX idx_user_id (`user_id`);
ALTER TABLE `test_plan` ADD COLUMN conclusion VARCHAR(1024) NULL COMMENT '结论';
ALTER TABLE `test_plan` ADD COLUMN ext_info JSON NULL COMMENT '扩展信息';


DROP TABLE IF EXISTS `test_item`;
CREATE TABLE IF NOT EXISTS `test_item`(
`id` INT UNSIGNED AUTO_INCREMENT COMMENT 'id',
`plan_id` INT UNSIGNED NOT NULL COMMENT '测试计划id',
`type` VARCHAR(32) NOT NULL DEFAULT 'CREATIVE' COMMENT '类型，TRAIN、CREATIVE',
`status` VARCHAR(32) NOT NULL DEFAULT 'ENABLED' COMMENT '状态，DISABLED、ENABLED、PROCESSING、COMPARING、FINISHED',
`pre_id` INT UNSIGNED NULL DEFAULT NULL COMMENT '前置项目id',
`pre_status` VARCHAR(32) NULL NULL DEFAULT 'ENABLED' COMMENT '前置项目状态，DISABLED、ENABLED、PROCESSING、COMPARING、FINISHED',
`rounds_num` INT UNSIGNED COMMENT '轮数',
`shared_params` JSON NULL COMMENT '共用的参数信息',
`operator_id` INT NULL COMMENT '操作者id',
`create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
`modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRIMARY KEY ( `id` )
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
COMMENT 'AB测试项目表';

ALTER TABLE `test_item` ADD INDEX idx_plan_id (`plan_id`);
ALTER TABLE `test_item` ADD INDEX idx_type (`type`);
ALTER TABLE `test_item` ADD INDEX idx_status (`status`);
ALTER TABLE `test_item` ADD INDEX idx_pre_id (`pre_id`);
ALTER TABLE `test_item` ADD COLUMN conclusion VARCHAR(1024) NULL COMMENT '结论';

DROP TABLE IF EXISTS `test_item_group`;
CREATE TABLE IF NOT EXISTS `test_item_group`(
`id` INT UNSIGNED AUTO_INCREMENT COMMENT 'id',
`plan_id` INT UNSIGNED NOT NULL COMMENT '测试计划id',
`item_id` INT UNSIGNED NOT NULL COMMENT '测试项目id',
`type` VARCHAR(32) NOT NULL DEFAULT 'CREATIVE' COMMENT '类型，TRAIN、CREATIVE',
`status` VARCHAR(32) NOT NULL DEFAULT 'ENABLED' COMMENT '状态，DISABLED、ENABLED、PROCESSING、COMPARING、FINISHED',
`group_type` VARCHAR(32) NOT NULL COMMENT '分组类别，EXPERIMENTAL、CONTROL',
`comparison_params` JSON NULL COMMENT '对照参数信息',
`rounds_num` INT UNSIGNED COMMENT '轮数',
`batch_id` INT UNSIGNED NULL COMMENT '创作批次id',
`positive_num` INT UNSIGNED NULL COMMENT '正向数量',
`negative_num` INT UNSIGNED NULL COMMENT '负向数量',
`operator_id` INT NULL COMMENT '操作者id',
`create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
`modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRIMARY KEY ( `id` )
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
COMMENT 'AB测试项目分组表';

ALTER TABLE `test_item_group` ADD INDEX idx_plan_id (`plan_id`);
ALTER TABLE `test_item_group` ADD INDEX idx_item_id (`item_id`);
ALTER TABLE `test_item_group` ADD INDEX idx_type (`type`);
ALTER TABLE `test_item_group` ADD INDEX idx_status (`status`);
ALTER TABLE `test_item_group` ADD COLUMN `ext_info` JSON NULL COMMENT '扩展信息';


DROP TABLE IF EXISTS `test_result`;
CREATE TABLE IF NOT EXISTS `test_result`(
`id` INT UNSIGNED AUTO_INCREMENT COMMENT 'id',
`plan_id` INT UNSIGNED NOT NULL COMMENT '测试计划id',
`item_id` INT UNSIGNED NOT NULL COMMENT '测试项目id',
`group_id` INT UNSIGNED NOT NULL COMMENT '分组id',
`round_id` INT UNSIGNED NOT NULL COMMENT '轮次id',
`type` VARCHAR(32) NOT NULL DEFAULT 'CREATIVE' COMMENT '类型，TRAIN、CREATIVE',
`status` VARCHAR(32) NOT NULL DEFAULT 'ENABLED' COMMENT '状态，DISABLED、ENABLED、PROCESSING、COMPARING、FINISHED',
`group_type` VARCHAR(32) NOT NULL COMMENT '分组类别，EXPERIMENTAL、CONTROL',
`batch_id` INT UNSIGNED NULL COMMENT '批次id',
`task_id` INT UNSIGNED NULL COMMENT '任务id',
`request_params` JSON NULL COMMENT '请求参数',
`seed` VARCHAR(32) NULL COMMENT '种子',
`image_url` VARCHAR(256) NULL COMMENT '图片地址',
`mini_image_url` VARCHAR(256) NULL COMMENT '缩略图地址',
`score` BOOLEAN NULL COMMENT '得分,正负',
`case_id` INT UNSIGNED NULL COMMENT '图库id',
`operator_id` INT NULL COMMENT '操作者id',
`create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
`modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRIMARY KEY ( `id` )
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
COMMENT 'AB测试结果表';

ALTER TABLE `test_result` ADD INDEX idx_plan_id (`plan_id`);
ALTER TABLE `test_result` ADD INDEX idx_item_id (`item_id`);
ALTER TABLE `test_result` ADD INDEX idx_round_id (`round_id`);
ALTER TABLE `test_result` ADD INDEX idx_type (`type`);
ALTER TABLE `test_result` ADD INDEX idx_status (`status`);
ALTER TABLE `test_result` ADD INDEX idx_batch_id (`batch_id`);
ALTER TABLE `test_result` ADD INDEX idx_task_id (`task_id`);

-- 12.23
ALTER TABLE `image_case_sync_record` ADD COLUMN `sync_type` VARCHAR(64) DEFAULT NULL COMMENT '同步类型（badCase、精选图...）';

-- 12.23
ALTER TABLE `image_case` DROP INDEX `idx_url`;
ALTER TABLE `image_case` ADD INDEX idx_url (`url`);


drop table if exists `common_task`;
create table `common_task` (
`id` INT UNSIGNED AUTO_INCREMENT COMMENT '任务id',
`user_id` INT UNSIGNED NULL COMMENT '归属主账号id',
`operator_id` INT UNSIGNED NULL COMMENT '操作员/子账号id',
`task_type` VARCHAR(32) NOT NULL COMMENT '任务类型, video_generation/extend_video/lip_sync等',
`task_status` VARCHAR(32) NOT NULL COMMENT '任务状态, INIT/PENDING/RUNNING/COMPLETED/FAILED/CANCELED',
`out_task_platform` VARCHAR(32) NULL COMMENT '第三方平台名称',
`out_task_id` VARCHAR(64) NULL COMMENT '第三方平台任务id',
`out_task_status` VARCHAR(32) NULL COMMENT '任务状态',
`req_biz_params` varchar(1024) NULL COMMENT '自定义请求参数',
`related_biz_type` VARCHAR(32) NULL COMMENT '关联业务类型',
`related_biz_id` varchar(64) NULL COMMENT '关联业务id',
`complete_request` TEXT NULL COMMENT '完整请求报文，主要用于请求外部http或comfyui服务的重试',
`ret_detail` TEXT NULL COMMENT '结果详情',
`ext_info` varchar(2048) NULL COMMENT '扩展',
`task_start_time` TIMESTAMP NULL COMMENT '任务开始时间',
`task_end_time` TIMESTAMP NULL COMMENT '任务结束时间',
`create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
`modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
`deleted` BOOLEAN NOT NULL COMMENT '是否删除，0未删除、1删除',

PRIMARY KEY ( `id` )
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
COMMENT '通用任务表';

ALTER TABLE `common_task` ADD INDEX idx_type_status_deleted (`task_type`, `task_status`, `deleted`);

-- 12.30
ALTER TABLE `test_item` ADD COLUMN `name` VARCHAR(64) DEFAULT NULL COMMENT '测试项名称';

--0114
ALTER TABLE `creative_batch`
	MODIFY COLUMN `image_proportion` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '图片比例，3:4、1:1等' AFTER `show_image`
;

ALTER TABLE `creative_task`
	MODIFY COLUMN `image_proportion` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '图片比例，3:4、1:1等' AFTER `model_id`
;


-- 1.14(image_case表添加扩展字段)
ALTER TABLE `image_case` ADD COLUMN `ext_info` JSON DEFAULT NULL COMMENT '扩展信息';


-- 1.15 (test_result表添加扩展字段)
ALTER TABLE `test_result` ADD COLUMN `ext_info` JSON DEFAULT NULL COMMENT '扩展信息';



-- 2.14 (invoice_info表添加内部发票号码字段)
ALTER TABLE `invoice_info`
	ADD COLUMN `inner_invoice_no` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '内部发票号码' AFTER `finish_time`,
	MODIFY COLUMN `invoice_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '税务局发票号码' AFTER `inner_invoice_no`,
	ADD COLUMN `negative_invoice_no` varchar(32) NULL COMMENT '税务局红冲票号码' AFTER `invoice_download_url`,
	ADD COLUMN `negative_invoice_detail` varchar(1024) NULL COMMENT '红冲发票详情' AFTER `negative_invoice_no`
;


-- 2.27 添加user_vip_view
CREATE OR REPLACE VIEW `user_vip_view` AS
select a.id user_id,a.role_type,if(b.vip is not null,1,0) vip,a.nick_name,c.distributor_corp_name
from user a left join (
select distinct id as user_id, 1 as vip from `user`
  where user_type = 'MASTER' and deleted = 0 and (memo like '%VIP%' or `id` in (
      select distinct(master_user_id) from `order_info` where deleted = 0
    )
  )
) b on a.id = b.user_id
left join (
  SELECT `customer_master_user_id` ,distributor_corp_name FROM `distributor_customer` where `deleted` = 0
) c on c.customer_master_user_id = a.id
where a.nick_name not like '%登登%' and a.nick_name not like '%祝余%'
  and a.nick_name not like '%铁翅%' and a.nick_name not like '%铁柱%'
  and a.nick_name not like '%贝洋%' and a.nick_name not like '%晓白%'
  and a.nick_name not like '%楠瓜%' and a.nick_name not like '%松然%'
  and a.nick_name not like '%甲第%' and a.nick_name not like '%卷草%'
  and a.nick_name not like '%伊登%' and a.nick_name not like '%pluvious%'
  and a.nick_name not like '%测试%' and a.nick_name not like '%木樨%'
  and a.nick_name not like '%水云%' and a.nick_name not like '%shuiyun%'
  and role_type = 'MERCHANT' and a.id > 100016;

--  3.3 添加server表内网地址字段
 ALTER table `server` ADD COLUMN `intranet_address` VARCHAR(128) NULL COMMENT '内网地址';

-- 3.8 添加用户收藏表

DROP TABLE IF EXISTS `user_favor`;
CREATE TABLE `user_favor`
(
    `id`          int         NOT NULL AUTO_INCREMENT COMMENT 'id',
    `user_id`     int         NOT NULL COMMENT '用户id',
    `item_id`     int         NOT NULL COMMENT '收藏对象的id, model_id, element_id, taskId',
    `type`        varchar(32) NOT NULL COMMENT '类型',
    `model_id`    int                  DEFAULT NULL,
    `ext_info`    json                 DEFAULT NULL COMMENT '扩展字段',
    `memo`        varchar(255)         DEFAULT NULL COMMENT '备注',
    `operator_id` int         NOT NULL COMMENT '操作人id',
    `create_time` timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY           `idx_user_id` (`user_id`) USING BTREE,
    KEY           `idx_operator_id` (`operator_id`) USING BTREE,
    KEY           `idx_type` (`type`) USING BTREE,
    KEY           `idx_item_id` (`item_id`) USING BTREE,
    KEY           `idx_user_item_type_cover` (`user_id`,`item_id`,`type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=54 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户收藏表';

-- 3.8增加设备相关信息
 ALTER table `server` ADD COLUMN `device_id` VARCHAR(32) NULL COMMENT '设备信息id';

-- 3.8 `creat_batch` 表 增加业务类型
ALTER TABLE `creative_batch` ADD COLUMN `biz_type` VARCHAR(32) DEFAULT 'NORMAL' COMMENT '业务类型'

ALTER TABLE `creative_batch` ADD KEY `idx_biz_type` (`biz_type`) USING BTREE

-- 3.9 更新视图 `creative_batch_view`
ALTER VIEW `creative_batch_view` AS
SELECT
    `c`.`id` AS `id`,
    `c`.`model_id` AS `model_id`,
    `c`.`model_type` AS `model_type`,
    `c`.`type` AS `type`,
    `c`.`user_id` AS `user_id`,
    `c`.`show_image` AS `show_image`,
    `c`.`image_proportion` AS `image_proportion`,
    `c`.`batch_cnt` AS `batch_cnt`,
    `c`.`aigc_request` AS `aigc_request`,
    `c`.`prompt_id` AS `prompt_id`,
    `c`.`result_images` AS `result_images`,
    `c`.`result_path` AS `result_path`,
    `c`.`ext_info` AS `ext_info`,
    `c`.`status` AS `status`,
    `c`.`operator_id` AS `operator_id`,
    `c`.`deleted` AS `deleted`,
    `c`.`create_time` AS `create_time`,
    `c`.`modify_time` AS `modify_time`,
    `c`.`title` AS `title`,
    `c`.`biz_type` AS `biz_type`,
    `u`.`nick_name` AS `operator_nick`,
    `mu`.`nick_name` AS `user_nick`,
    `m`.`name` AS `model_name`,
    `m`.`show_image` AS `model_show_img`,
    `m`.`ext_info` AS `model_ext_info`
FROM
    (
        (
            (`creative_batch` `c` LEFT JOIN `material_model` `m` ON ((`c`.`model_id` = `m`.`id`)))
                LEFT JOIN `user` `u` ON ((`c`.`operator_id` = `u`.`id`)))
            LEFT JOIN `user` `mu` ON ((`c`.`user_id` = `mu`.`id`)))
WHERE
    ((`c`.`deleted` = 0) AND (`u`.`deleted` = 0))


-- prompt修改记录表
CREATE TABLE prompt_modification_log (
   `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
   `module_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '模块代码，如A、B、C',
   `module_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '模块主类型，如服装类型、人脸类型、场景类型',
   `parent_element_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '父级元素id',
   `old_field_value` json NULL COMMENT '旧数据（修改前的数据 json 格式）',
   `new_field_value` json NULL COMMENT '新数据（本次修改的数据 json 格式）',
   `fixed_attrs` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '固定属性JSON，记录固定属性值，如{demo:value}',
   `ext_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '扩展信息，存储额外的JSON格式数据',
   `operator_id` int NULL DEFAULT NULL COMMENT '操作人ID',
   `operator_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作人姓名',
   `operation_time` datetime NOT NULL COMMENT '操作时间',
   `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注信息',
   PRIMARY KEY (`id`) USING BTREE,
   INDEX `idx_module_entity`(`module_code` ASC, `parent_element_id` ASC) USING BTREE COMMENT '模块和实体联合索引',
   INDEX `idx_module_type`(`module_type` ASC) USING BTREE COMMENT '模块类型索引',
   INDEX `idx_operation_time`(`operation_time` ASC) USING BTREE COMMENT '操作时间索引',
   INDEX `idx_operator_id`(`operator_id` ASC) USING BTREE COMMENT '操作人ID索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='prompt修改记录表';

-- 03.27
ALTER TABLE `aigc-platform`.`organization`
    MODIFY COLUMN `creator_master_user_id` int UNSIGNED NULL COMMENT '创建者主账号id' AFTER `org_level`,
    MODIFY COLUMN `creator_user_role_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '创建人角色类型，DISTRIBUTOR：渠道商' AFTER `creator_master_user_id`,
    MODIFY COLUMN `creator_operator_user_id` int UNSIGNED NULL COMMENT '创建人操作员账号id' AFTER `creator_user_role_type`,
    MODIFY COLUMN `modifier_operator_user_id` int UNSIGNED NULL COMMENT '最近修改人的操作员账号id' AFTER `creator_operator_user_id`;


ALTER TABLE `aigc-platform`.`user_organization`
    MODIFY COLUMN `creator_operator_user_id` int UNSIGNED NULL COMMENT '创建人操作员账号id' AFTER `org_id`,
    MODIFY COLUMN `modifier_operator_user_id` int UNSIGNED NULL COMMENT '修改人操作员账号id' AFTER `creator_operator_user_id`;


-- 0408
CREATE OR REPLACE VIEW creative_element_view AS
SELECT
    ce.*,
    CASE
        WHEN JSON_EXTRACT(ce.ext_info, '$.openScope') IS NULL
             OR JSON_UNQUOTE(JSON_EXTRACT(ce.ext_info, '$.openScope')) = ''
             OR JSON_UNQUOTE(JSON_EXTRACT(ce.ext_info, '$.openScope')) = 'ALL'
        THEN NULL
        ELSE CAST(JSON_UNQUOTE(JSON_EXTRACT(ce.ext_info, '$.openScope')) AS UNSIGNED)
    END AS privatelyOpen2UserId,
    CASE
        WHEN JSON_EXTRACT(ce.ext_info, '$.openScope') IS NULL
             OR JSON_UNQUOTE(JSON_EXTRACT(ce.ext_info, '$.openScope')) = ''
             OR JSON_UNQUOTE(JSON_EXTRACT(ce.ext_info, '$.openScope')) = 'ALL'
        THEN NULL
        ELSE u.nick_name
    END AS privatelyOpen2UserNick,
    CASE
        WHEN JSON_EXTRACT(ce.ext_info, '$.openScope') IS NULL
             OR JSON_UNQUOTE(JSON_EXTRACT(ce.ext_info, '$.openScope')) = ''
             OR JSON_UNQUOTE(JSON_EXTRACT(ce.ext_info, '$.openScope')) = 'ALL'
        THEN NULL
        ELSE u.role_type
    END AS privatelyOpen2UserRoleType
FROM
    creative_element ce
LEFT JOIN
    user u ON JSON_EXTRACT(ce.ext_info, '$.openScope') IS NOT NULL
              AND JSON_UNQUOTE(JSON_EXTRACT(ce.ext_info, '$.openScope')) != ''
              AND JSON_UNQUOTE(JSON_EXTRACT(ce.ext_info, '$.openScope')) != 'ALL'
              AND u.id = CAST(JSON_UNQUOTE(JSON_EXTRACT(ce.ext_info, '$.openScope')) AS UNSIGNED)
WHERE
    ce.deleted = 0;


-- 用户点数消耗统计表
DROP TABLE IF EXISTS `stats_user_point`;
CREATE TABLE `stats_user_point`
(
    `id`                   INT UNSIGNED AUTO_INCREMENT COMMENT 'id',
    `user_id`              INT UNSIGNED NOT NULL COMMENT '用户id',
    `stats_type`           VARCHAR(16)    NOT NULL COMMENT '统计类型：DAILY/WEEKLY/MONTHLY/TOTAL',
    `stats_date`           VARCHAR(10)    NOT NULL COMMENT '统计日期: 格式为yyyy-MM-dd',
    `point_consumed`       INT            NOT NULL DEFAULT 0 COMMENT '消耗的算力点',
    `give_point_consumed`  INT            NOT NULL DEFAULT 0 COMMENT '消耗的赠送点',
    `exp_point_consumed`   INT            NOT NULL DEFAULT 0 COMMENT '消耗的体验点',
    `model_point_consumed` INT            NOT NULL DEFAULT 0 COMMENT '消耗的套内点',
    `recharge_amount`      DECIMAL(20, 8) NOT NULL DEFAULT 0 COMMENT '充值金额',
    `create_time`          TIMESTAMP      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`          TIMESTAMP      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_type_date` (`user_id`, `stats_type`, `stats_date`),
    INDEX                  `idx_user_id` (`user_id`),
    INDEX                  `idx_stats_type` (`stats_type`),
    INDEX                  `idx_stats_date` (`stats_date`)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
COMMENT '用户点数消耗统计表';

-- 4.1 排班表
CREATE TABLE `work_schedule` (
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `user_id` INT UNSIGNED NOT NULL COMMENT '人员id',
    `start_time` DATETIME NOT NULL COMMENT '开始时间',
    `end_time` DATETIME NOT NULL COMMENT '结束时间',
    `create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
    `modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE `idx_user_id_start_time_end_time` (`user_id`, `start_time`, `end_time`)
);
-- 工作流任务
CREATE TABLE `workflow_task`
(
    `id`          INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `biz_id`      INT UNSIGNED NOT NULL COMMENT '关联的业务id',
    `type`        VARCHAR(32)  NOT NULL COMMENT '任务类型',
    `operator_id` INT UNSIGNED NULL COMMENT '操作人',
    `status`      VARCHAR(32)  NOT NULL DEFAULT 'INIT' COMMENT '状态',
    `meta`        JSON         NULL COMMENT '拓展字段',
    `create_time` TIMESTAMP    NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
    `modify_time` TIMESTAMP    NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE `idx_biz_id_type` (`biz_id`, `type`)
);


-- 0414
CREATE OR REPLACE VIEW distributor_customer_view AS
SELECT d.*, u.nick_name AS customer_master_nick, u.corp_name as customer_master_corp_name
FROM `distributor_customer` d
LEFT JOIN `user` u ON d.customer_master_user_id=u.id
WHERE d.deleted=0 AND u.deleted=0
;

-- 0418
-- 用户操作统计表（出图、下载）
CREATE TABLE `aigc-platform`.`stats_user_operate`  (
    `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
    `stats_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '统计类型：DAILY/WEEKLY/MONTHLY/TOTAL',
    `stats_date` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '统计日期: 格式为yyyy-MM-dd',
    `user_id` int NOT NULL COMMENT '用户 id',
    `material_id` int NOT NULL COMMENT '服装 id',
    `user_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'MASTER' COMMENT '用户类型（主账户：MASTER  子账号：SUB）',
    `create_count` int NOT NULL DEFAULT 0 COMMENT '用户出图量',
    `download_count` int NOT NULL DEFAULT 0 COMMENT '图片下载量',
    `ext_info` json NULL COMMENT '扩展字段',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_user_type_date`(`stats_type` ASC, `stats_date` ASC, `user_id` ASC, `material_id` ASC) USING BTREE,
    INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
    INDEX `idx_stats_type`(`stats_type` ASC) USING BTREE,
    INDEX `idx_stats_date`(`stats_date` ASC) USING BTREE,
    INDEX `idx_material_id`(`material_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户操作统计表（出图、下载）' ROW_FORMAT = Dynamic;

-- 服装信息统计表
CREATE TABLE `aigc-platform`.`stats_clothes_info`  (
    `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
    `stats_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '统计类型：DAILY/WEEKLY/MONTHLY/TOTAL',
    `stats_date` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '统计日期: 格式为yyyy-MM-dd',
    `vip_clothes_count` int NOT NULL DEFAULT 0 COMMENT 'vip 用户上传服装 套数',
    `auto_train_count` int NOT NULL DEFAULT 0 COMMENT '自动训练服装 套数',
    `manual_delivery_count` int NOT NULL DEFAULT 0 COMMENT '人工交付服装 套数',
    `auto_train_and_delivery_count` int NOT NULL DEFAULT 0 COMMENT '自动训练+交付 套数',
    `retry_matting_count` int NOT NULL DEFAULT 0 COMMENT '二次抠图（手动上传图片+系统级抠图） 套数',
    `update_prompt_count` int NOT NULL DEFAULT 0 COMMENT '更新提示词 套数',
    `copy_count` int NOT NULL DEFAULT 0 COMMENT '克隆服装 套数',
    `ext_info` json NULL COMMENT '扩展字段',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_user_type_date`(`stats_type` ASC, `stats_date` ASC) USING BTREE,
    INDEX `idx_stats_type`(`stats_type` ASC) USING BTREE,
    INDEX `idx_stats_date`(`stats_date` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci  COMMENT = '服装信息统计表' ROW_FORMAT = Dynamic;


-- 0418
ALTER TABLE `user`
	ADD COLUMN `last_visit_date` varchar(32) NULL COMMENT '上次访问日期，yyyyMMDD' AFTER `user_review_info`
;

CREATE OR REPLACE VIEW
  distributor_customer_view AS
SELECT
  d.*,
  u.nick_name AS customer_master_nick,
  u.corp_name AS customer_master_corp_name,
  IF(
    up.point IS NOT NULL,
    round(up.point / 1000, 2),
    0.00
  ) AS customer_muse_point,
  IF(
    ut.total_topup_amount IS NOT NULL,
    round(ut.total_topup_amount, 2),
    0.00
  ) AS customer_total_topup_amount,
  IF(
    DATE(u.last_login_time) >= DATE_SUB(CURDATE(), INTERVAL 15 DAY)
    OR (
      u.last_visit_date IS NOT NULL
      AND u.last_visit_date >= DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 15 DAY), '%Y%m%d')
    ),
    1,
    0
  ) AS visit_in_15days
FROM
  `distributor_customer` d
  LEFT JOIN `user` u ON d.customer_master_user_id = u.id
  LEFT JOIN `user_point` up ON u.id = up.user_id
  LEFT JOIN (
    SELECT
      master_user_id,
      sum(pay_amount) AS total_topup_amount
    FROM
      `order_info`
    WHERE
      deleted = 0
    GROUP BY
      master_user_id
  ) ut ON u.id = ut.master_user_id
WHERE
  d.deleted = 0
  AND u.deleted = 0;


-- 0422
CREATE OR REPLACE VIEW
  `user_view` AS
SELECT
  u.*,
  IF(
    up.point IS NOT NULL,
    round(up.point / 1000, 2),
    0.00
  ) AS muse_point,
  IF(
    ut.total_topup_amount IS NOT NULL,
    round(ut.total_topup_amount, 2),
    0.00
  ) AS total_topup_amount,
  IF(
    DATE(u.last_login_time) >= DATE_SUB(CURDATE(), INTERVAL 15 DAY)
    OR (
      u.last_visit_date IS NOT NULL
      AND u.last_visit_date >= DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 15 DAY), '%Y%m%d')
    ),
    1,
    0
  ) AS visit_in_15days,
  dc.`distributor_master_user_id` AS related_distributor_master_userId,
  pf.prompt_engineer_user_id AS prompt_engineer_user_id
FROM
  `user` u
  LEFT JOIN `user_point` up ON u.id = up.user_id
  LEFT JOIN (
    SELECT
      master_user_id,
      sum(pay_amount) AS total_topup_amount
    FROM
      `order_info`
    WHERE
      deleted = 0
    GROUP BY
      master_user_id
  ) ut ON u.id = ut.master_user_id
  LEFT JOIN `distributor_customer` dc ON u.id = dc.`customer_master_user_id`
  LEFT JOIN (
    SELECT
      uid AS user_id,
      cast(profile_val AS CHAR) AS prompt_engineer_user_id
    FROM
      `user_profile`
    WHERE
      profile_key = 'promptUserId'
  ) pf ON u.id = pf.user_id
;

-- 0423 商户 vip 视图新增vip_3999字段
CREATE OR REPLACE VIEW user_vip_view AS
SELECT
    u.id AS user_id,
    u.role_type,
    u.nick_name,
    dc.distributor_corp_name,
    CASE WHEN is_vip.user_id IS NOT NULL THEN 1 ELSE 0 END AS vip,
    CASE WHEN high_pay_user.user_id IS NOT NULL THEN 1 ELSE 0 END AS vip_3999
FROM
    user u
        LEFT JOIN (
        -- 识别普通VIP用户（有订单或备注中包含VIP）
        SELECT DISTINCT
            user.id AS user_id
        FROM
            user
        WHERE
            user.user_type = 'MASTER'
          AND user.deleted = 0
          AND (
            user.memo LIKE '%VIP%'
                OR user.id IN (
                SELECT DISTINCT master_user_id
                FROM order_info
                WHERE deleted = 0
            )
            )
    ) is_vip ON u.id = is_vip.user_id
        LEFT JOIN (
        -- 识别高付费用户（支付金额>=3999）
        SELECT DISTINCT
            master_user_id AS user_id
        FROM
            order_info
        WHERE
            deleted = 0
          AND pay_amount >= 3999
    ) high_pay_user ON u.id = high_pay_user.user_id
        LEFT JOIN (
        -- 获取分销商信息
        SELECT
            customer_master_user_id,
            distributor_corp_name
        FROM
            distributor_customer
        WHERE
            deleted = 0
    ) dc ON dc.customer_master_user_id = u.id
WHERE
    u.role_type = 'MERCHANT'
  AND u.id > 100016
  -- 排除内部测试账号
  AND u.nick_name NOT LIKE '%登登%'
  AND u.nick_name NOT LIKE '%祝余%'
  AND u.nick_name NOT LIKE '%铁翅%'
  AND u.nick_name NOT LIKE '%铁柱%'
  AND u.nick_name NOT LIKE '%贝洋%'
  AND u.nick_name NOT LIKE '%晓白%'
  AND u.nick_name NOT LIKE '%楠瓜%'
  AND u.nick_name NOT LIKE '%松然%'
  AND u.nick_name NOT LIKE '%甲第%'
  AND u.nick_name NOT LIKE '%卷草%'
  AND u.nick_name NOT LIKE '%伊登%'
  AND u.nick_name NOT LIKE '%pluvious%'
  AND u.nick_name NOT LIKE '%测试%'
  AND u.nick_name NOT LIKE '%木樨%'
  AND u.nick_name NOT LIKE '%水云%'
  AND u.nick_name NOT LIKE '%shuiyun%'


-- 0430
CREATE TABLE `aigc-platform`.`stats_material_owner`  (
    `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
    `stats_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '统计类型：DAILY/WEEKLY/MONTHLY/TOTAL',
    `stats_date` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '统计日期: 格式为yyyy-MM-dd',
    `user_id` int NOT NULL COMMENT '用户 id（为 0 时则是汇总）',
    `nickname` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户名称',
    `delivery_count` int NOT NULL DEFAULT 0 COMMENT '交付数量',
    `ext_info` json NULL COMMENT '扩展字段',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_user_type_date`(`user_id` ASC, `stats_type` ASC, `stats_date` ASC) USING BTREE,
    INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
    INDEX `idx_stats_type`(`stats_type` ASC) USING BTREE,
    INDEX `idx_stats_date`(`stats_date` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '服装负责人表' ROW_FORMAT = Dynamic;

-- 0430
CREATE OR REPLACE VIEW
  `user_view` AS
SELECT
  u.*,
  IF(
    up.point IS NOT NULL,
    round(up.point / 1000, 2),
    0.00
  ) AS muse_point,
  IF(
    ut.total_topup_amount IS NOT NULL,
    round(ut.total_topup_amount, 2),
    0.00
  ) AS total_topup_amount,
  IF(
    DATE(u.last_login_time) >= DATE_SUB(CURDATE(), INTERVAL 15 DAY)
    OR (
      u.last_visit_date IS NOT NULL
      AND u.last_visit_date >= DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 15 DAY), '%Y%m%d')
    ),
    1,
    0
  ) AS visit_in_15days,
  dc.`distributor_master_user_id` AS related_distributor_master_userId,
  pf.prompt_engineer_user_id AS prompt_engineer_user_id,
  dc.`distributor_corp_org_id` AS related_distributor_corp_id,
  dist_org.name AS related_distributor_corp_name,
  dc.`distributor_sales_user_id` AS related_distributor_sales_userId,
  sales_user.nick_name AS related_sales_user_name,
  org.name AS related_corp_name

FROM
  `USER` u
  LEFT JOIN `user_point` up ON u.id = up.user_id
  LEFT JOIN (
    SELECT
      master_user_id,
      sum(pay_amount) AS total_topup_amount
    FROM
      `order_info`
    WHERE
      deleted = 0
    GROUP BY
      master_user_id
  ) ut ON u.id = ut.master_user_id
  LEFT JOIN `distributor_customer` dc ON u.id = dc.`customer_master_user_id`
  LEFT JOIN (
    SELECT
      uid AS user_id,
      cast(profile_val AS CHAR) AS prompt_engineer_user_id
    FROM
      `user_profile`
    WHERE
      profile_key = 'promptUserId'
  ) pf ON u.id = pf.user_id
  LEFT JOIN `organization` dist_org ON dc.`distributor_corp_org_id`=dist_org.id
  LEFT JOIN `organization` org ON u.`corp_org_id`=org.id
  LEFT JOIN `user` sales_user ON sales_user.id=dc.`distributor_sales_user_id`
;



CREATE OR REPLACE VIEW
  distributor_customer_view AS
SELECT
  d.*,
  u.nick_name AS customer_master_nick,
  user_org.name AS customer_master_corp_name,
  dist_org.name AS related_distributor_corp_name,
  IF(
    up.point IS NOT NULL,
    round(up.point / 1000, 2),
    0.00
  ) AS customer_muse_point,
  IF(
    ut.total_topup_amount IS NOT NULL,
    round(ut.total_topup_amount, 2),
    0.00
  ) AS customer_total_topup_amount,
  IF(
    DATE(u.last_login_time) >= DATE_SUB(CURDATE(), INTERVAL 15 DAY)
    OR (
      u.last_visit_date IS NOT NULL
      AND u.last_visit_date >= DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 15 DAY), '%Y%m%d')
    ),
    1,
    0
  ) AS visit_in_15days
FROM
  `distributor_customer` d
  LEFT JOIN `user` u ON d.customer_master_user_id = u.id
  LEFT JOIN `organization` user_org ON user_org.id=u.corp_org_id
  LEFT JOIN `organization` dist_org ON dist_org.id=d.distributor_corp_org_id
  LEFT JOIN `user_point` up ON u.id = up.user_id
  LEFT JOIN (
    SELECT
      master_user_id,
      sum(pay_amount) AS total_topup_amount
    FROM
      `order_info`
    WHERE
      deleted = 0
    GROUP BY
      master_user_id
  ) ut ON u.id = ut.master_user_id
;

-- vip视图添加到ddl中
CREATE OR REPLACE VIEW user_vip_view AS
SELECT
    u.id AS user_id,
    u.role_type,
    u.nick_name,
    dc.distributor_corp_name,
    CASE WHEN is_vip.user_id IS NOT NULL THEN 1 ELSE 0 END AS vip,
    CASE WHEN high_pay_user.user_id IS NOT NULL THEN 1 ELSE 0 END AS vip_3999
FROM
    user u
        LEFT JOIN (
        -- 识别普通VIP用户（有订单或备注中包含VIP）
        SELECT DISTINCT
            user.id AS user_id
        FROM
            user
        WHERE
            user.user_type = 'MASTER'
          AND user.deleted = 0
          AND (
            user.memo LIKE '%VIP%'
                OR user.id IN (
                SELECT DISTINCT master_user_id
                FROM order_info
                WHERE deleted = 0
            )
            )
    ) is_vip ON u.id = is_vip.user_id
        LEFT JOIN (
        -- 识别高付费用户（支付金额>=3999）
        SELECT DISTINCT
            master_user_id AS user_id
        FROM
            order_info
        WHERE
            deleted = 0
          AND pay_amount >= 3999
    ) high_pay_user ON u.id = high_pay_user.user_id
        LEFT JOIN (
        -- 获取分销商信息
        SELECT
            customer_master_user_id,
            distributor_corp_name
        FROM
            distributor_customer
        WHERE
            deleted = 0
    ) dc ON dc.customer_master_user_id = u.id
WHERE
    u.role_type = 'MERCHANT'
  AND u.id > 100016
  -- 排除退款账号
  AND ( `u`.`memo` is NULL or `u`.`memo` NOT LIKE '%已退款%')
  -- 排除内部测试账号
  AND u.nick_name NOT LIKE '%登登%'
  AND u.nick_name NOT LIKE '%祝余%'
  AND u.nick_name NOT LIKE '%铁翅%'
  AND u.nick_name NOT LIKE '%铁柱%'
  AND u.nick_name NOT LIKE '%贝洋%'
  AND u.nick_name NOT LIKE '%晓白%'
  AND u.nick_name NOT LIKE '%楠瓜%'
  AND u.nick_name NOT LIKE '%松然%'
  AND u.nick_name NOT LIKE '%甲第%'
  AND u.nick_name NOT LIKE '%卷草%'
  AND u.nick_name NOT LIKE '%伊登%'
  AND u.nick_name NOT LIKE '%pluvious%'
  AND u.nick_name NOT LIKE '%测试%'
  AND u.nick_name NOT LIKE '%木樨%'
  AND u.nick_name NOT LIKE '%水云%'
  AND u.nick_name NOT LIKE '%shuiyun%';



-- 5.8 多色拆分变更为主子单；后台/前台管理，看到的都是主单，调度时使用的是子单
ALTER TABLE `material_model` ADD COLUMN `main_type` VARCHAR(8) NOT NULL DEFAULT 'NORMAL' COMMENT '主子类型，NORMAL/MAIN/SUB';
ALTER TABLE `material_model` ADD COLUMN `main_id` INT UNSIGNED NULL COMMENT '主模型id';

ALTER TABLE `material_model` ADD INDEX idx_main_type (main_type);
ALTER TABLE `material_model` ADD INDEX idx_main_id (main_id);

-- 需要重新执行：
CREATE OR REPLACE VIEW `material_model_view` AS
select t.*,
	u.nick_name AS `operator_nick`,
    v.nick_name AS `user_nick`
from (
	SELECT *
	FROM `material_model`
	WHERE deleted = 0
) t
LEFT JOIN `user` u ON t.`operator_id` = u.`id`
LEFT JOIN `user` v ON t.`user_id` = v.`id`


CREATE TABLE `aigc-platform`.`stats_sale_indicators`  (
    `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
    `stats_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '统计类型：DAILY/WEEKLY/MONTHLY/TOTAL',
    `stats_date` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '统计日期: 格式为yyyy-MM-dd',
    `user_id` int NOT NULL COMMENT '用户 id（渠道/销售/运营）',
    `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称（渠道/销售/运营）',
    `parent_id` int NOT NULL DEFAULT 0 COMMENT '父级 id，默认为 0',
    `clothes_exp_count` int NOT NULL DEFAULT 0 COMMENT '服装体验量',
    `customer_conversion_count` int NOT NULL DEFAULT 0 COMMENT '客户转换量（新签 3999 以上）',
    `customer_consumption_points` int NOT NULL DEFAULT 0 COMMENT '客户消耗点数',
    `customer_activity_rate` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '活跃客户率',
    `customer_repurchase_rate` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '客户复购率',
    `custom_model_customers` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '定制模特数量',
    `custom_scene_customers` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '定制场景数量',
    `customer_protection_metrics` int NOT NULL DEFAULT 0 COMMENT '大于 60 天未充值的客户',
    `create_count` int NOT NULL DEFAULT 0 COMMENT '销售出图数量',
    `ext_info` json NULL COMMENT '扩展字段',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `udx_type_date_userId_parentId`(`stats_type` ASC, `stats_date` ASC, `user_id` ASC, `parent_id` ASC) USING BTREE,
    INDEX `idx_type`(`stats_type` ASC) USING BTREE,
    INDEX `idx_date`(`stats_date` ASC) USING BTREE,
    INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
    INDEX `idx_name`(`name` ASC) USING BTREE,
    INDEX `idx_parent_id`(`parent_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '销售指标表' ROW_FORMAT = Dynamic;

CREATE TABLE `aigc-platform`.`stats_operation_indicators`  (
    `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
    `stats_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '统计类型：DAILY/WEEKLY/MONTHLY/TOTAL',
    `stats_date` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '统计日期: 格式为yyyy-MM-dd',
    `user_id` int NOT NULL COMMENT '用户 id（渠道/销售/运营）',
    `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称（渠道/销售/运营）',
    `customer_conversion_count` int NOT NULL DEFAULT 0 COMMENT '客户转换量（新签 3999 以上）',
    `customer_consumption_points` int NOT NULL DEFAULT 0 COMMENT '客户消耗点数',
    `customer_consumption_points_avg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '平均消耗点数',
    `customer_activity_rate` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0.0' COMMENT '活跃客户率',
    `customer_repurchase_rate` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0.0' COMMENT '客户复购率',
    `custom_model_customers` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '定制模特数量',
    `custom_scene_customers` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '定制场景数量',
    `customer_protection_metrics` int NOT NULL DEFAULT 0 COMMENT '大于 60 天未充值的客户',
    `delivery_clothing_count` int NOT NULL DEFAULT 0 COMMENT '交付服装量',
    `approve_clothing_count` int NOT NULL DEFAULT 0 COMMENT '审核服装量',
    `approve_error_rate` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0.0' COMMENT '审核错误率',
    `garment_rebate_rate` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0.0' COMMENT '服装返点率',
    `customer_complaint_rate` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0.0' COMMENT '客户投诉率',
    `ext_info` json NULL COMMENT '扩展字段',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `udx_type_date_userId_parentId`(`stats_type` ASC, `stats_date` ASC, `user_id` ASC) USING BTREE,
    INDEX `idx_type`(`stats_type` ASC) USING BTREE,
    INDEX `idx_date`(`stats_date` ASC) USING BTREE,
    INDEX `idx_user_id`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;


-- 05-10
CREATE OR REPLACE VIEW user_vip_view AS
SELECT
    u.id AS user_id,
    u.role_type,
    u.nick_name,
    dc.distributor_corp_name,
    CASE WHEN is_vip.user_id IS NOT NULL THEN 1 ELSE 0 END AS vip,
    CASE WHEN high_pay_user.user_id IS NOT NULL THEN 1 ELSE 0 END AS vip_3999
FROM
    user u
        LEFT JOIN (
        -- 识别普通VIP用户（有订单或备注中包含VIP）
        SELECT DISTINCT
            user.id AS user_id
        FROM
            user
        WHERE
            user.user_type = 'MASTER'
          AND user.deleted = 0
          AND (
            user.memo LIKE '%VIP%'
                OR user.id IN (
                SELECT DISTINCT master_user_id
                FROM order_info
                WHERE deleted = 0
            )
            )
    ) is_vip ON u.id = is_vip.user_id
        LEFT JOIN (
        -- 识别高付费用户（支付金额>=3999）
        SELECT DISTINCT
            master_user_id AS user_id
        FROM
            order_info
        WHERE
            deleted = 0
          AND pay_amount >= 3999
    ) high_pay_user ON u.id = high_pay_user.user_id
        LEFT JOIN (
        -- 获取分销商信息
        SELECT
            customer_master_user_id,
            distributor_corp_name
        FROM
            distributor_customer
        WHERE
            deleted = 0
    ) dc ON dc.customer_master_user_id = u.id
WHERE
    u.role_type = 'MERCHANT'
  AND u.id > 100016
  -- 排除退款账号
  AND (NOT ((`u`.`memo` LIKE '%已退款%')))
  -- 排除内部测试账号
  AND u.nick_name NOT LIKE '%登登%'
  AND u.nick_name NOT LIKE '%祝余%'
  AND u.nick_name NOT LIKE '%铁翅%'
  AND u.nick_name NOT LIKE '%铁柱%'
  AND u.nick_name NOT LIKE '%贝洋%'
  AND u.nick_name NOT LIKE '%晓白%'
  AND u.nick_name NOT LIKE '%楠瓜%'
  AND u.nick_name NOT LIKE '%松然%'
  AND u.nick_name NOT LIKE '%甲第%'
  AND u.nick_name NOT LIKE '%卷草%'
  AND u.nick_name NOT LIKE '%伊登%'
  AND u.nick_name NOT LIKE '%pluvious%'
  AND u.nick_name NOT LIKE '%测试%'
  AND u.nick_name NOT LIKE '%木樨%'
  AND u.nick_name NOT LIKE '%水云%'
  AND u.nick_name NOT LIKE '%shuiyun%'

-- 服装管理性能优化
ALTER TABLE `material_model` ADD COLUMN `material_type` VARCHAR(16) NOT NULL DEFAULT 'cloth' COMMENT '素材类型, cloth/face/scene';

update material_model set material_type = if((JSON_UNQUOTE(JSON_EXTRACT(train_detail, '$.materialType')) IS NULL
	OR JSON_UNQUOTE(JSON_EXTRACT(train_detail, '$.materialType')) = ''
	),'cloth', JSON_UNQUOTE(JSON_EXTRACT(train_detail, '$.materialType')));

-- 需要再执行一遍
CREATE OR REPLACE VIEW `material_model_view` AS
select t.*,
	u.nick_name AS `operator_nick`,
    v.nick_name AS `user_nick`
from (
	SELECT *
	FROM `material_model`
	WHERE deleted = 0
) t
LEFT JOIN `user` u ON t.`operator_id` = u.`id`
LEFT JOIN `user` v ON t.`user_id` = v.`id`;


CREATE TABLE `aigc-platform`.`stats_warning_info`  (
    `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
    `stats_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '统计类型：DAILY/WEEKLY/MONTHLY/TOTAL',
    `stats_date` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '统计日期: 格式为yyyy-MM-dd',
    `weekly_no_consumption_rate` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0.0' COMMENT '周内不消耗客户',
    `monthly_no_consumption_rate` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0.0' COMMENT '月内不消耗客户',
    `customer_refund_rate_count` int NOT NULL DEFAULT 0 COMMENT '用户退点率大于百分之5的客户数量',
    `delivery_timeout_count` int NOT NULL DEFAULT 0 COMMENT '交付超过 240 小时的服装量',
    `customer_balance_alert_count` int NOT NULL DEFAULT 0 COMMENT '客户余额预警（缪斯点小于 2000 或 小于累计充值金额为基础的30%）',
    `customer_not_convert_count` int NOT NULL DEFAULT 0 COMMENT '客户入库时间超过 60 天未转化数量',
    `ext_info` json NULL COMMENT '扩展字段',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `udx_type_date_userId_parentId`(`stats_type` ASC, `stats_date` ASC) USING BTREE,
    INDEX `idx_type`(`stats_type` ASC) USING BTREE,
    INDEX `idx_date`(`stats_date` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- 0519:码表
DROP TABLE IF EXISTS `code`;
CREATE TABLE `code`(
`id` INT UNSIGNED AUTO_INCREMENT COMMENT 'id',
`code` VARCHAR(64) NOT NULL COMMENT '码值，全局唯一',
`code_type` VARCHAR(64) NOT NULL COMMENT '码类型，registerPromotion:推广注册',
`code_status` VARCHAR(32) NOT NULL COMMENT '码状态，valid:有效|invalid:已失效',
`code_info` VARCHAR(1024) NULL COMMENT '码的信息',
`related_user_id` INT NULL COMMENT '关联的用户',
`creator_master_id` INT NOT NULL COMMENT '创建人主账号id',
`creator_id` INT NOT NULL COMMENT '创建人id',
`modifier_id` INT NOT NULL COMMENT '最近修改人id',
`ext_info` VARCHAR(1024) NULL COMMENT '预留扩展',
`deleted` BOOLEAN NOT NULL COMMENT '是否删除，0未删除、1删除',
`create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
`modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRIMARY KEY ( `id` ),
UNIQUE KEY (`code`)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
COMMENT '码表';


-- 服装管理性能优化
ALTER TABLE `stats_operation_indicators` ADD COLUMN `customer_total_count`  int NOT NULL DEFAULT 0 COMMENT  '客户总数';
ALTER TABLE `stats_operation_indicators` ADD COLUMN `customer_upload_material_count`  int NOT NULL DEFAULT 0 COMMENT  '上传服装总数';
ALTER TABLE `stats_operation_indicators` ADD COLUMN `video_count`  int NOT NULL DEFAULT 0 COMMENT  '视频数量';
ALTER TABLE `stats_operation_indicators` ADD COLUMN `video_count_avg`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0.00' COMMENT  '客均视频数量';



-- 0527 固定创作模板表
CREATE TABLE `aigc-platform`.`fixed_creative_template`  (
    `id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `user_id` int NOT NULL COMMENT '用户id',
    `template_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模板名称',
    `template_list` json NOT NULL COMMENT '模板图片列表',
    `ext_info` json NULL COMMENT '扩展信息',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（收藏时间）',
    `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_user_id`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;


-- 0610  comfyui模板表
drop table if exists `comfyui_workflow_template`;
create table `comfyui_workflow_template` (
    `id` int not null auto_increment comment '主键id',
    `template_key` varchar(255) not null comment '模板key',
    `template_desc` varchar(255) not null comment '模板描述',
    `template_data` json not null comment '模板数据',
    `version` VARCHAR(32) not null comment '模板版本，如20250610.1',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` int not null comment '创建人id',
    `modify_by` int not null comment '修改人id',
    PRIMARY KEY (`id`),
    UNIQUE KEY (`template_key`, `version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT 'comfyui模板表';

drop table if exists `comfyui_workflow_template_active_version`;
create table `comfyui_workflow_template_active_version` (
    `id` int not null auto_increment comment '主键id',
    `template_key` varchar(255) not null comment '模板key',
    `template_desc` varchar(255) not null comment '模板描述',
    `active_version` VARCHAR(32) not null comment '模板版本，如20250610.1',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` int not null comment '创建人id',
    `modify_by` int not null comment '修改人id',
    PRIMARY KEY (`id`),
    UNIQUE KEY (`template_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT 'comfyui模板激活版本表';


ALTER TABLE `comfyui_task`
	MODIFY COLUMN `ext_info` varchar(8096) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '扩展' AFTER `ret_detail`
;

ALTER TABLE `creative_task`
	ADD COLUMN `tpl_info` json NULL COMMENT 'comfyui模板信息' AFTER `type`
;




-- 单笔订单结算精确到子账号下
ALTER TABLE `order_settlement`
    ADD COLUMN `type` VARCHAR(32) DEFAULT "COMMISSION" NOT NULL COMMENT '订单结算类型' AFTER `order_no`,
    ADD COLUMN `principal_type` VARCHAR(64) DEFAULT "CORP" NOT NULL COMMENT '结算主体类型' AFTER `distributor_corp_name`,
    ADD COLUMN `principal_id` INT UNSIGNED DEFAULT NULL COMMENT '结算主体 id' AFTER `principal_type`;

ALTER TABLE `order_settlement` DROP INDEX `order_no`;
ALTER TABLE `order_settlement` ADD INDEX `idx_order_no` (`order_no`);



-- 渠道商结算按渠道主账号 或 销售账号 或 子渠道账号 结算
ALTER TABLE `distributor_settlement`
    ADD COLUMN `principal_type` VARCHAR(64) DEFAULT "CORP" NOT NULL COMMENT '结算主体类型' AFTER `distributor_corp_name`,
    ADD COLUMN `principal_id` INT UNSIGNED DEFAULT NULL COMMENT '结算主体 id' AFTER `principal_type`;


-- 销售渠道考核表
DROP TABLE IF EXISTS `assessment_plan`;
CREATE TABLE `assessment_plan`
(
    `id`              INT         NOT NULL AUTO_INCREMENT COMMENT 'id',
    `principal_type`  VARCHAR(64) NOT NULL  COMMENT '结算主体类型',
    `principal_id`    INT UNSIGNED DEFAULT NULL COMMENT '结算主体 id',
    `type`            VARCHAR(32) NOT NULL COMMENT '考核类型',
    `status`          VARCHAR(32) NOT NULL COMMENT '考核任务状态',

    `kpi_target`      JSON        NOT NULL COMMENT '考核指标',
    `kpi_actual`      JSON        DEFAULT NULL COMMENT '实际完成情况',

    `plan_from_date`  DATE        NOT NULL COMMENT '考核计划开始日期',
    `plan_end_date`   DATE        NOT NULL COMMENT '考核计划结束日期',

    `ext_info`        JSON        DEFAULT NULL COMMENT '扩展字段',

    `creator_user_id` INT         NOT NULL COMMENT '创建人用户id',
    `modify_user_id`  INT         NOT NULL COMMENT '修改人用户id',

    `create_time`     TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`     TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `deleted`         BOOLEAN     NOT NULL DEFAULT FALSE COMMENT '是否删除，0未删除、1删除',
    PRIMARY KEY (`id`),
    KEY `principal_type` (`principal_type`) USING BTREE,
    KEY `idx_principal_id` (`principal_id`) USING BTREE,
    KEY `idx_type` (`type`) USING BTREE,
    KEY `status` (`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='销售考核计划';

-- 主体信息表
DROP TABLE IF EXISTS `principal_info`;
CREATE TABLE `principal_info`
(
    `id`              int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
    `principal_type`  varchar(64) NOT NULL COMMENT '主体类型',
    `principal_id`    int unsigned NOT NULL COMMENT '关联的用户id',
    `info_key`        varchar(64) NOT NULL COMMENT 'key',
    `info_value`      json                 DEFAULT NULL COMMENT '信息',
    `ext_info`        json                 DEFAULT NULL COMMENT '扩展信息',
    `creator_user_id` int unsigned NOT NULL COMMENT '创建人用户id',
    `modify_user_id`  int unsigned NOT NULL COMMENT '修改人用户id',
    `create_time`     timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`     timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `deleted`         tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0未删除、1删除',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_principal_type_id_key` (`principal_type`,`principal_id`,`info_key`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=62 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='通用主体属性表';


-- 06-18 更新 user_view
CREATE OR REPLACE VIEW
  `user_view` AS
SELECT
    u.*,
    IF(
            up.point IS NOT NULL,
            round(up.point / 1000, 2),
            0.00
    ) AS muse_point,
    IF(
            ut.total_topup_amount IS NOT NULL,
            round(ut.total_topup_amount, 2),
            0.00
    ) AS total_topup_amount,
    IF(
            DATE(u.last_login_time) >= DATE_SUB(CURDATE(), INTERVAL 15 DAY)
    OR (
      u.last_visit_date IS NOT NULL
      AND u.last_visit_date >= DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 15 DAY), '%Y%m%d')
    ),
            1,
            0
    ) AS visit_in_15days,
    dc.`distributor_master_user_id` AS related_distributor_master_userId,
    pf.prompt_engineer_user_id AS prompt_engineer_user_id,
    dc.`distributor_corp_org_id` AS related_distributor_corp_id,
    dist_org.name AS related_distributor_corp_name,
    dc.`distributor_sales_user_id` AS related_distributor_sales_userId,
    sales_user.nick_name AS related_sales_user_name,
    org.name AS related_corp_name,
    pi.contract_date as contract_date

FROM
    `USER` u
        LEFT JOIN `user_point` up ON u.id = up.user_id
        LEFT JOIN (
        SELECT
            master_user_id,
            sum(pay_amount) AS total_topup_amount
        FROM
            `order_info`
        WHERE
            deleted = 0
        GROUP BY
            master_user_id
    ) ut ON u.id = ut.master_user_id
        LEFT JOIN `distributor_customer` dc ON u.id = dc.`customer_master_user_id`
        LEFT JOIN (
        SELECT
            uid AS user_id,
            cast(profile_val AS CHAR) AS prompt_engineer_user_id
        FROM
            `user_profile`
        WHERE
            profile_key = 'promptUserId'
    ) pf ON u.id = pf.user_id
        LEFT JOIN `organization` dist_org ON dc.`distributor_corp_org_id`=dist_org.id
        LEFT JOIN `organization` org ON u.`corp_org_id`=org.id
        LEFT JOIN `user` sales_user ON sales_user.id=dc.`distributor_sales_user_id`
        LEFT JOIN (
        select
            JSON_UNQUOTE(JSON_EXTRACT(info_value, '$.userId')) AS user_id,
            JSON_UNQUOTE(JSON_EXTRACT(info_value, '$.beginDate')) AS contract_date
        FROM
            `principal_info`
        WHERE
            info_key = "CONTRACT_INFO"
          AND JSON_VALID(info_value)
    ) pi ON u.id = pi.user_id
;



-- 更新订单结算视图
CREATE OR REPLACE VIEW `order_settlement_view` AS
SELECT a.*,
       b.`master_user_id`   AS `merchant_id`,
       b.`master_user_nick` AS `merchant_name`,
       b.`finish_time`      AS `order_finish_time`,
       o.`id`               AS `merchant_corp_id`,
       o.`name`             AS `merchant_corp_name`
FROM `order_settlement` a
         LEFT JOIN `order_info` b ON a.`order_no` = b.`order_no`
         LEFT JOIN `user_organization` uo ON b.`master_user_id` = uo.`user_id`
         LEFT JOIN `organization` o ON uo.`org_id` = o.`id`;

-- 权限表添加版本号
ALTER TABLE `permission` ADD COLUMN `version` VARCHAR(16) NOT NULL COMMENT '版本号' DEFAULT '00000000.0';
-- 销售成功案例表
drop table if exists `sales_success_stories`;
create table `sales_success_stories` (
`id` INT UNSIGNED AUTO_INCREMENT COMMENT 'id',
`name` VARCHAR(64) NULL COMMENT '案例名称',
`topped` BOOLEAN NOT NULL COMMENT '是否置顶',
`customer_id` INT UNSIGNED NULL COMMENT '客户id',
`customer_name` VARCHAR(64) NULL COMMENT '客户名称',
`batch_id` INT UNSIGNED NULL COMMENT '创作批次id',
`model_id` INT UNSIGNED NULL COMMENT '服装id',
`model_name` VARCHAR(64) NULL COMMENT '服装名称',
`model_url` VARCHAR(512) NULL COMMENT '服装展示图片',
`image_urls` JSON NULL COMMENT '图片地址列表，jsonArray格式',
`memo` VARCHAR(1024) NULL COMMENT '备注',
`ext_info` JSON NULL COMMENT '扩展',
`user_id` INT UNSIGNED NULL COMMENT '归属主账号id',
`operator_id` INT UNSIGNED NULL COMMENT '操作员/子账号id',
`create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
`modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
`deleted` BOOLEAN NOT NULL COMMENT '是否删除，0未删除、1删除',
PRIMARY KEY ( `id` )
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
COMMENT '销售成功案例表';

ALTER TABLE `sales_success_stories` ADD INDEX idx_customer_id (`customer_id`);
ALTER TABLE `sales_success_stories` ADD INDEX idx_user_id (`user_id`);
ALTER TABLE `sales_success_stories` ADD INDEX idx_operator_id (`operator_id`);
ALTER TABLE `sales_success_stories` ADD INDEX idx_deleted (`deleted`);
ALTER TABLE `sales_success_stories` ADD INDEX idx_topped (`topped`);

-- 增加测试版本号
ALTER TABLE comfyui_workflow_template_active_version ADD COLUMN `test_version` VARCHAR(32) null comment '测试模板版本，如20250610.1' ;
ALTER TABLE comfyui_workflow_template ADD COLUMN `deleted` BOOLEAN NOT null DEFAULT 0 comment '是否删除，0未删除、1删除' ;
ALTER TABLE comfyui_workflow_template_active_version ADD COLUMN `test_open_scope` json default null comment '测试模板开放范围' ;


CREATE OR REPLACE VIEW `organization_area_view` AS
select id, name,root_id,
  case when root_id in (4,9) then '常熟'
    when root_id in (275,8,70,108,371,560) then '广州'
    when root_id in (167,202,357) then '杭州'
    when root_id in (5,6,99,230) then '嘉兴'
    else '其他'
  end as area
from organization
where root_id not in (1,2,3,4,7,15,134,136,547)
;


--7.2
CREATE OR REPLACE VIEW `material_model_view` AS
select t.*,
	u.nick_name AS `operator_nick`,
    v.nick_name AS `user_nick`,
    if(v.role_type = 'MERCHANT' and v.memo like '%虚拟商家%','DISTRIBUTOR',v.role_type) AS user_role
from (
	SELECT *
	FROM `material_model`
	WHERE deleted = 0
) t
LEFT JOIN `user` u ON t.`operator_id` = u.`id`
LEFT JOIN `user` v ON t.`user_id` = v.`id`;

CREATE OR REPLACE VIEW creative_element_view AS
SELECT
    ce.*,
    CASE
        WHEN JSON_EXTRACT(ce.ext_info, '$.openScope') IS NULL
             OR JSON_UNQUOTE(JSON_EXTRACT(ce.ext_info, '$.openScope')) = ''
             OR JSON_UNQUOTE(JSON_EXTRACT(ce.ext_info, '$.openScope')) = 'ALL'
        THEN NULL
        ELSE CAST(JSON_UNQUOTE(JSON_EXTRACT(ce.ext_info, '$.openScope')) AS UNSIGNED)
    END AS privatelyOpen2UserId,
    CASE
        WHEN JSON_EXTRACT(ce.ext_info, '$.openScope') IS NULL
             OR JSON_UNQUOTE(JSON_EXTRACT(ce.ext_info, '$.openScope')) = ''
             OR JSON_UNQUOTE(JSON_EXTRACT(ce.ext_info, '$.openScope')) = 'ALL'
        THEN NULL
        ELSE u.nick_name
    END AS privatelyOpen2UserNick,
    CASE
        WHEN JSON_EXTRACT(ce.ext_info, '$.openScope') IS NULL
             OR JSON_UNQUOTE(JSON_EXTRACT(ce.ext_info, '$.openScope')) = ''
             OR JSON_UNQUOTE(JSON_EXTRACT(ce.ext_info, '$.openScope')) = 'ALL'
        THEN NULL
        ELSE u.role_type
    END AS privatelyOpen2UserRoleType,
	if(u2.role_type = 'MERCHANT' and u2.memo like '%虚拟商家%','DISTRIBUTOR',u2.role_type) AS user_role,
    u2.nick_name AS user_nick
FROM
    creative_element ce
LEFT JOIN
    user u ON JSON_EXTRACT(ce.ext_info, '$.openScope') IS NOT NULL
              AND JSON_UNQUOTE(JSON_EXTRACT(ce.ext_info, '$.openScope')) != ''
              AND JSON_UNQUOTE(JSON_EXTRACT(ce.ext_info, '$.openScope')) != 'ALL'
              AND u.id = CAST(JSON_UNQUOTE(JSON_EXTRACT(ce.ext_info, '$.openScope')) AS UNSIGNED)
LEFT JOIN user u2 ON u2.id = ce.user_id
WHERE
    ce.deleted = 0;


-- 拍摄风格配置表
drop table if exists `shooting_style`;
create table `shooting_style` (
`id` INT UNSIGNED AUTO_INCREMENT COMMENT 'id',
`type1_name` VARCHAR(64) NOT NULL COMMENT '一级分类名称',
`type2_name` VARCHAR(64) NULL COMMENT '二级分类名称',
`type2_en_name` VARCHAR(64) NULL COMMENT '英文名称',
`exam_image_urls` JSON NULL COMMENT '示例图片地址列表，jsonArray格式',
`style_desc` TEXT NULL COMMENT '风格描述',
`representative_brand` VARCHAR(1024) NULL COMMENT '代表性品牌',
`classic_elements` VARCHAR(1024) NULL COMMENT '经典元素',
`model_tags` JSON NULL COMMENT '模特类型，适合的男模特/女模特类型，男模标签列表，女模标签列表',
`ext_info` JSON NULL COMMENT '扩展',
`create_by` INT UNSIGNED NULL COMMENT '创建账号id',
`modify_by` INT UNSIGNED NULL COMMENT '修改账号id',
`create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
`modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRIMARY KEY ( `id` )
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
COMMENT '拍摄风格配置表';

-- 07-08
ALTER TABLE `order_settlement` ADD UNIQUE INDEX `uk_order_no_type` (`order_no`, `type`);

-- 7.13 优化count(*)的性能，去掉了material_model的关联
create or replace view `creative_batch_view` as
select
  c.*,
  u.nick_name as `operator_nick`,
  mu.nick_name as `user_nick`
from
  `creative_batch` c
  left join `user` u on c.`operator_id` = u.`id`
  left join `user` mu on c.`user_id` = mu.`id`
where
  c.deleted = 0
  and u.deleted = 0;

ALTER TABLE `creative_batch` ADD INDEX idx_operator_id (operator_id);

-- 07-25
ALTER TABLE `creative_task`
    ADD COLUMN `pre_task_id` INT DEFAULT NULL COMMENT '前置任务ID'
;


-- 08-12 添加测试用例集
drop table if exists `test_case`;
create table `test_case` (
`id` INT UNSIGNED AUTO_INCREMENT COMMENT 'id',
`name` VARCHAR(64) NOT NULL COMMENT '测试用例名称',
`type` VARCHAR(32) NOT NULL COMMENT '类型',
`case_num` INT UNSIGNED NOT NULL COMMENT '用例数量',
`user_id` INT UNSIGNED NOT NULL COMMENT '上传用户id',
`ext_info` JSON NULL COMMENT '扩展',
`deleted` BOOLEAN NOT NULL COMMENT '是否删除，0未删除、1删除',
`create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
`modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRIMARY KEY ( `id` )
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
COMMENT '测试用例集表';

ALTER TABLE `test_case` ADD index idx_type_deleted (`type`, `deleted`);

drop table if exists `test_case_item`;
create table `test_case_item` (
`id` INT UNSIGNED AUTO_INCREMENT COMMENT 'id',
`case_id` INT UNSIGNED COMMENT '测试用例id',
`name` VARCHAR(64) NULL COMMENT '测试用例项名称',
`type` VARCHAR(32) NOT NULL COMMENT '类型',
`run_times` INT UNSIGNED NOT NULL COMMENT '执行次数',
`ext_info` JSON NULL COMMENT '扩展',
`deleted` BOOLEAN NOT NULL COMMENT '是否删除，0未删除、1删除',
`create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
`modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRIMARY KEY ( `id` )
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
COMMENT '测试用例项表';

ALTER TABLE `test_case_item` ADD index idx_type_deleted (`case_id`, `deleted`);

-- 08.20
drop table if exists `gallery`;
CREATE TABLE `gallery`(
    `id`          int unsigned                                                 NOT NULL AUTO_INCREMENT  COMMENT 'id',
    `user_id`     int unsigned                                                          DEFAULT NULL    COMMENT '用户id',
    `operator_id` int unsigned                                                 NOT NULL                 COMMENT '操作人id',
    `type`        varchar(64)                                                  NOT NULL COMMENT '一级分类(按功能分)',
    `sub_type`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci          DEFAULT NULL    COMMENT '二级分类(功能内细分)',
    `image_url`   varchar(1023)                                                         DEFAULT NULL    COMMENT '单张图片url',
    `md5`         varchar(255)                                                 NOT NULL                 COMMENT 'md5值',
    `ext_info`    json                                                                  DEFAULT NULL    COMMENT '配置信息',
    `belong`      varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL                 COMMENT '归属',
    `create_time` timestamp                                                    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` timestamp                                                    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `deleted`     tinyint(1) NOT NULL DEFAULT '0'                                                       COMMENT '逻辑删除',
    PRIMARY KEY (`id`),
    KEY           `idx_user_id` (`user_id`)                                    USING BTREE,
    KEY           `idx_operator_id_type_sub_type` (`operator_id`,`type`,`sub_type`) USING BTREE,
    KEY           `idx_type_sub_type` (`type`,`sub_type`)                      USING BTREE,
    KEY           `idx_belong` (`belong`)                                      USING BTREE,
    KEY           `idx_deleted` (`deleted`)                                    USING BTREE,
    KEY           `idx_md5` (`md5`)                                            USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='图库表: 保存系统配置的商品图、用户的上传历史等';

-- 08.27 表结构创建
CREATE TABLE `aigc-platform`.`agent_session`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键 id',
  `session_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'CREATE_IMAGE' COMMENT 'agent类型',
  `clothes_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '服装名称',
  `clothes_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '服装类型：上衣/裤子/裙子等',
  `clothes_thumbnail` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '服装缩略图',
  `other_thumbnails` json NULL COMMENT '其他参考图（JSON数组，如：[{url: \"...\", type: \"style_ref\"}]）',
  `user_id` int UNSIGNED NOT NULL COMMENT '归属主账号id',
  `ext_info` json NULL COMMENT '扩展信息',
  `status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'INIT' COMMENT '状态，ACTIVE：活跃中   FINISHED：已完成   CANCELED：已取消   FAILED：失败',
  `operator_id` int UNSIGNED NOT NULL COMMENT '操作者id',
  `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0未删除、1删除',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `last_interactive_time` timestamp NULL DEFAULT NULL COMMENT '最后一次交互时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_operator_status`(`operator_id` ASC, `status` ASC) USING BTREE,
  INDEX `idx_type_status`(`session_type` ASC) USING BTREE,
  INDEX `idx_deleted`(`deleted` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_creatime`(`create_time` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;


CREATE TABLE `aigc-platform`.`agent_session_task`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '任务主键ID',
  `session_id` int UNSIGNED NOT NULL COMMENT '关联 agent_session.id',
  `batch_id` int NULL DEFAULT NULL COMMENT '批次 ID',
  `pre_task_id` int NULL DEFAULT NULL COMMENT '前置任务ID（仅关联一个前置任务，第一个任务为NULL）',
  `pre_task_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '多个前置任务 id（,分割）',
  `user_id` int UNSIGNED NOT NULL COMMENT '用户ID（冗余，方便直接查询）',
  `task_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务类型：CLOTH_ANALYSIS（服装分析）、REF_IMAGE_ANALYSIS（参考图分析）等',
  `message_from` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '消息来源（system：系统恢复  user：用户发送）',
  `status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'ACTIVE' COMMENT '任务状态：ACTIVE/FINISHED/CANCELED/FAILED',
  `ext_info` json NULL COMMENT '任务扩展信息',
  `order_num` int NULL DEFAULT 0 COMMENT '任务排序（由小到大排序）',
  `result_info` json NULL COMMENT '任务结果（JSON 存结构化数据）',
  `deleted_by_rollback` tinyint(1) NOT NULL DEFAULT 0 COMMENT '回滚删除 正常：0  删除：1',
  `rollback_to_task_id` int NULL DEFAULT NULL COMMENT '回滚至哪个任务节点',
  `finish_time` timestamp NULL DEFAULT NULL COMMENT '任务完成时间',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_session`(`session_id` ASC) USING BTREE,
  INDEX `idx_user_task`(`user_id` ASC, `task_type` ASC, `status` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE,
  INDEX `idx_session_task_session_id`(`session_id` ASC) USING BTREE,
  INDEX `idx_session_task_task_id`(`pre_task_id` ASC) USING BTREE,
  INDEX `idx_order_num`(`order_num` ASC) USING BTREE,
  INDEX `idx_pre_task_id`(`pre_task_id` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会话任务表：1个会话可包含多个子任务' ROW_FORMAT = Dynamic;


drop table if exists `customer_activity_log`;
create table `customer_activity_log` (
`id` INT UNSIGNED AUTO_INCREMENT COMMENT 'id',
`dt` VARCHAR(16) NOT NULL COMMENT '记录日期',
`title` VARCHAR(16) NOT NULL COMMENT '分类标题',
`user_id` INT UNSIGNED NOT NULL COMMENT '用户id',
`customer_name` VARCHAR(64) NULL COMMENT '客户名称',
`customer_corp` VARCHAR(64) NULL COMMENT '客户公司',
`user_group` VARCHAR(16) NOT NULL COMMENT '用户分组',
`sales_id` INT UNSIGNED NULL COMMENT '销售id',
`sales_name` VARCHAR(32) NULL COMMENT '销售id',
`sales_area` VARCHAR(16) NULL COMMENT '销售区域',
`first_pay_time` TIMESTAMP NOT NULL COMMENT '首次充值时间',
`pay_amount` VARCHAR(16) NULL COMMENT '充值金额',
`consume_amount` VARCHAR(16) NULL COMMENT '消费金额',
`pay_times` INT UNSIGNED NOT NULL COMMENT '充值次数',
`create_time` TIMESTAMP NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
`modify_time` TIMESTAMP NOT NULL COMMENT '修改时间' DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRIMARY KEY ( `id` )
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
COMMENT '客户活跃记录表';

ALTER TABLE `customer_activity_log` ADD index idx_dt (`dt`);
ALTER TABLE `customer_activity_log` ADD index idx_user_id (`user_id`);
ALTER TABLE `customer_activity_log` ADD index idx_user_group (`user_group`);
ALTER TABLE `customer_activity_log` ADD index idx_sales_id (`sales_id`);
ALTER TABLE `customer_activity_log` ADD index idx_sales_name (`sales_name`);
ALTER TABLE `customer_activity_log` ADD index idx_sales_area (`sales_area`);


-- 9.12 新增财务
update permission set config = concat(config,',FINANCE') where action like '%/distributorSettlement/getDistributorOrgTrees';
update permission set config = concat(config,',FINANCE') where action like '%/user/allMaster';
update permission set config = concat(config,',FINANCE') where action like '%/orderInfo/queryList';
update permission set config = concat(config,',FINANCE') where action like '%/invoiceInfo/%';
update permission set config = concat(config,',FINANCE') where action like '%/organization/queryRootCorpByPage';
update permission set config = concat(config,',FINANCE') where action like '%/assessmentPlan/getAllDistributorAssessment';
update permission set config = concat(config,',FINANCE') where action like '%/sys/queryConfigByKeys';
update permission set config = concat(config,',FINANCE') where action like '%/distributorSettlement/queryAllDistributorPrincipalBasicInfo';
update permission set config = concat(config,',FINANCE') where action like '%/distributorSettlement/queryByPage';
update permission set config = concat(config,',FINANCE') where action like '%/orderInfo/checkIfShowTopup';

-- 9.19 短链表
CREATE TABLE `short_link`
(
    `id`         int            unsigned                                            NOT NULL AUTO_INCREMENT COMMENT 'id',
    `url`        varchar(512)   CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci    NOT NULL COMMENT '原始url',
    `short_code` varchar(64)    CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci    NOT NULL COMMENT '短链码',
    PRIMARY KEY (`id`),
    KEY          `idx_url`      (`url`)         USING BTREE COMMENT 'url 索引',
    KEY          `idx_short`    (`short_code`)  USING BTREE COMMENT 'short 索引'
) ENGINE=InnoDB AUTO_INCREMENT=151 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='短链接 表';