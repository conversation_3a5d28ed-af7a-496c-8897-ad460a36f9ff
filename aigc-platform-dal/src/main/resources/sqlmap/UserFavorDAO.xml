<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.conrain.aigc.platform.dal.dao.UserFavorDAO">
  <resultMap id="BaseResultMap" type="ai.conrain.aigc.platform.dal.entity.UserFavorDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="item_id" jdbcType="INTEGER" property="itemId" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="model_id" jdbcType="INTEGER" property="modelId" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="operator_id" jdbcType="INTEGER" property="operatorId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="ai.conrain.aigc.platform.dal.entity.UserFavorDO">
    <result column="ext_info" jdbcType="LONGVARCHAR" property="extInfo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, user_id, item_id, type, model_id, memo, operator_id, create_time, modify_time
  </sql>
  <sql id="Blob_Column_List">
    ext_info
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="ai.conrain.aigc.platform.dal.example.UserFavorExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from user_favor
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="ai.conrain.aigc.platform.dal.example.UserFavorExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from user_favor
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from user_favor
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from user_favor
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="ai.conrain.aigc.platform.dal.entity.UserFavorDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into user_favor (user_id, item_id, type, 
      model_id, memo, operator_id, 
      create_time, modify_time, ext_info
      )
    values (#{userId,jdbcType=INTEGER}, #{itemId,jdbcType=INTEGER}, #{type,jdbcType=VARCHAR}, 
      #{modelId,jdbcType=INTEGER}, #{memo,jdbcType=VARCHAR}, #{operatorId,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{modifyTime,jdbcType=TIMESTAMP}, #{extInfo,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="ai.conrain.aigc.platform.dal.entity.UserFavorDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into user_favor
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="itemId != null">
        item_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="modelId != null">
        model_id,
      </if>
      <if test="memo != null">
        memo,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="itemId != null">
        #{itemId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="modelId != null">
        #{modelId,jdbcType=INTEGER},
      </if>
      <if test="memo != null">
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="ai.conrain.aigc.platform.dal.example.UserFavorExample" resultType="java.lang.Long">
    select count(*) from user_favor
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update user_favor
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=INTEGER},
      </if>
      <if test="record.itemId != null">
        item_id = #{record.itemId,jdbcType=INTEGER},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.modelId != null">
        model_id = #{record.modelId,jdbcType=INTEGER},
      </if>
      <if test="record.memo != null">
        memo = #{record.memo,jdbcType=VARCHAR},
      </if>
      <if test="record.operatorId != null">
        operator_id = #{record.operatorId,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifyTime != null">
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update user_favor
    set id = #{record.id,jdbcType=INTEGER},
      user_id = #{record.userId,jdbcType=INTEGER},
      item_id = #{record.itemId,jdbcType=INTEGER},
      type = #{record.type,jdbcType=VARCHAR},
      model_id = #{record.modelId,jdbcType=INTEGER},
      memo = #{record.memo,jdbcType=VARCHAR},
      operator_id = #{record.operatorId,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      ext_info = #{record.extInfo,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update user_favor
    set id = #{record.id,jdbcType=INTEGER},
      user_id = #{record.userId,jdbcType=INTEGER},
      item_id = #{record.itemId,jdbcType=INTEGER},
      type = #{record.type,jdbcType=VARCHAR},
      model_id = #{record.modelId,jdbcType=INTEGER},
      memo = #{record.memo,jdbcType=VARCHAR},
      operator_id = #{record.operatorId,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="ai.conrain.aigc.platform.dal.entity.UserFavorDO">
    update user_favor
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="itemId != null">
        item_id = #{itemId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="modelId != null">
        model_id = #{modelId,jdbcType=INTEGER},
      </if>
      <if test="memo != null">
        memo = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="ai.conrain.aigc.platform.dal.entity.UserFavorDO">
    update user_favor
    set user_id = #{userId,jdbcType=INTEGER},
      item_id = #{itemId,jdbcType=INTEGER},
      type = #{type,jdbcType=VARCHAR},
      model_id = #{modelId,jdbcType=INTEGER},
      memo = #{memo,jdbcType=VARCHAR},
      operator_id = #{operatorId,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      ext_info = #{extInfo,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="ai.conrain.aigc.platform.dal.entity.UserFavorDO">
    update user_favor
    set user_id = #{userId,jdbcType=INTEGER},
      item_id = #{itemId,jdbcType=INTEGER},
      type = #{type,jdbcType=VARCHAR},
      model_id = #{modelId,jdbcType=INTEGER},
      memo = #{memo,jdbcType=VARCHAR},
      operator_id = #{operatorId,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="count4Model" parameterType="ai.conrain.aigc.platform.dal.entity.UserFavorDO" resultType="java.lang.Long" >
    select count(distinct model_id)
    from user_favor
    where user_id = #{userId,jdbcType=INTEGER}
      and type = #{type,jdbcType=VARCHAR}
      and model_id is not null
  </select>

  <select id="select4ModelByExample" parameterType="ai.conrain.aigc.platform.dal.example.UserFavorExample" resultMap="ResultMapWithBLOBs">
    SELECT
        favor.modify_time,
        favor.type,
        favor.imageCount,
        main_model.id           AS model_id,
        main_model.name         AS model_name,
        batch.show_image        AS show_image
    FROM (
      SELECT
        model_id,
        type,
        -- 计算分组总图片数
        SUM(JSON_LENGTH(COALESCE(ext_info->'$.imageIndexes', JSON_ARRAY()))) AS imageCount,
        -- 获取每组最大 modify_time 对应的 item_id
        SUBSTRING_INDEX(GROUP_CONCAT(item_id ORDER BY modify_time DESC), ',', 1) AS item_id,
        -- 获取每组的最大 modify_time
        MAX(modify_time) AS modify_time
      FROM
        user_favor
      <if test="_parameter != null">
        <include refid="Example_Where_Clause" />
      </if>
      GROUP BY model_id
      ORDER BY modify_time DESC
      <if test="rows != null">
        <if test="offset != null">
          limit ${offset}, ${rows}
        </if>
        <if test="offset == null">
          limit ${rows}
        </if>
      </if>
    ) AS favor
    INNER JOIN material_model favor_model ON favor.model_id = favor_model.id
    -- 关联到 main_id
    INNER JOIN material_model main_model ON
        CASE
            WHEN favor_model.main_type = 'SUB' THEN favor_model.main_id
            ELSE favor_model.id
        END = main_model.id
    INNER JOIN creative_batch batch ON favor.item_id = batch.id;
  </select>

  <select id="select4Model" parameterType="ai.conrain.aigc.platform.dal.example.UserFavorExample" resultType="ai.conrain.aigc.platform.dal.entity.UserFavorDO">
    SELECT DISTINCT
        main_model.id AS model_id,
        main_model.name AS model_name
    FROM (
        select model_id
        from user_favor
        <if test="_parameter != null">
          <include refid="Example_Where_Clause" />
        </if>
    ) AS favor
    INNER JOIN material_model favor_model ON favor.model_id = favor_model.id
    -- 关联到 main_id
    INNER JOIN material_model main_model ON
        CASE
            WHEN favor_model.main_type = 'SUB' THEN favor_model.main_id
            ELSE favor_model.id
        END = main_model.id
    ORDER BY main_model.id
  </select>

</mapper>