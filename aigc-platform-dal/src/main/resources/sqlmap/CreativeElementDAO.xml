<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.conrain.aigc.platform.dal.dao.CreativeElementDAO">
    <resultMap id="BaseResultMap" type="ai.conrain.aigc.platform.dal.entity.CreativeElementDO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="config_key" jdbcType="VARCHAR" property="configKey"/>
        <result column="level" jdbcType="INTEGER" property="level"/>
        <result column="parent_id" jdbcType="INTEGER" property="parentId"/>
        <result column="show_image" jdbcType="VARCHAR" property="showImage"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="order" jdbcType="INTEGER" property="order"/>
        <result column="memo" jdbcType="VARCHAR" property="memo"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="belong" jdbcType="VARCHAR" property="belong"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="operator_id" jdbcType="INTEGER" property="operatorId"/>
        <result column="deleted" jdbcType="BIT" property="deleted"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="tags" jdbcType="LONGVARCHAR" property="tags"/>
        <result column="ext_tags" jdbcType="LONGVARCHAR" property="extTags"/>
        <result column="ext_info" jdbcType="LONGVARCHAR" property="extInfo"/>
        <result column="lora_model_id" jdbcType="INTEGER" property="loraModelId"/>
        <result column="is_new" jdbcType="BOOLEAN" property="isNew"/>
        <result column="privatelyOpen2UserId" jdbcType="INTEGER" property="privatelyOpen2UserId"/>
        <result column="privatelyOpen2UserNick" jdbcType="VARCHAR" property="privatelyOpen2UserNick"/>
        <result column="privatelyOpen2UserRoleType" jdbcType="VARCHAR" property="privatelyOpen2UserRoleType"/>
    </resultMap>

    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>

    <sql id="Base_Column_List">
        id, name, config_key, level, parent_id, show_image, status, `order`, memo, type,
        belong, user_id, operator_id, deleted, create_time, modify_time, tags, ext_tags, lora_model_id, is_new
    </sql>
    <sql id="Blob_Column_List">
        ext_info
    </sql>

    <sql id="View_Column_List">
        privatelyOpen2UserId, privatelyOpen2UserNick, privatelyOpen2UserRoleType
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        update creative_element set deleted = true
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="ai.conrain.aigc.platform.dal.entity.CreativeElementDO">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into creative_element (name, config_key, level,
        parent_id, show_image, status, `order`,
        memo, type, belong, user_id, operator_id,
        deleted, create_time,
        modify_time, tags, ext_tags,
        ext_info, lora_model_id,is_new)
        values (#{name,jdbcType=VARCHAR}, #{configKey,jdbcType=VARCHAR}, #{level,jdbcType=INTEGER},
        #{parentId,jdbcType=INTEGER}, #{showImage,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR},
        #{order,jdbcType=INTEGER},
        #{memo,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{belong,jdbcType=VARCHAR}, #{userId,jdbcType=INTEGER},
        #{operatorId,jdbcType=INTEGER}, #{deleted,jdbcType=BIT}, #{createTime,jdbcType=TIMESTAMP},
        #{modifyTime,jdbcType=TIMESTAMP}, #{tags,jdbcType=LONGVARCHAR}, #{extTags,jdbcType=LONGVARCHAR},
        #{extInfo,jdbcType=LONGVARCHAR}, #{loraModelId},#{isNew})
    </insert>
    <update id="updateByPrimaryKey" parameterType="ai.conrain.aigc.platform.dal.entity.CreativeElementDO">
        update creative_element
        set name = #{name,jdbcType=VARCHAR},
        config_key = #{configKey,jdbcType=VARCHAR},
        level = #{level,jdbcType=INTEGER},
        parent_id = #{parentId,jdbcType=INTEGER},
        show_image = #{showImage,jdbcType=VARCHAR},
        status = #{status,jdbcType=VARCHAR},
        `order` = #{order,jdbcType=INTEGER},
        memo = #{memo,jdbcType=VARCHAR},
        type = #{type,jdbcType=VARCHAR},
        belong = #{belong,jdbcType=VARCHAR},
        user_id = #{userId,jdbcType=INTEGER},
        operator_id = #{operatorId,jdbcType=INTEGER},
        deleted = #{deleted,jdbcType=BIT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        tags = #{tags,jdbcType=LONGVARCHAR},
        ext_tags = #{extTags,jdbcType=LONGVARCHAR},
        lora_model_id = #{loraModelId},
        ext_info = #{extInfo,jdbcType=LONGVARCHAR}
        is_new = #{isNew,jdbcType=BOOLEAN}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="ai.conrain.aigc.platform.dal.entity.CreativeElementDO">
        update creative_element
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="configKey != null">
                config_key = #{configKey,jdbcType=VARCHAR},
            </if>
            <if test="level != null">
                level = #{level,jdbcType=INTEGER},
            </if>
            <if test="parentId != null">
                parent_id = #{parentId,jdbcType=INTEGER},
            </if>
            <if test="showImage != null">
                show_image = #{showImage,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="order != null">
                `order` = #{order,jdbcType=INTEGER},
            </if>
            <if test="memo != null">
                memo = #{memo,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="belong != null">
                belong = #{belong,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=INTEGER},
            </if>
            <if test="operatorId != null">
                operator_id = #{operatorId,jdbcType=INTEGER},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=BIT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tags != null">
                tags = #{tags,jdbcType=LONGVARCHAR},
            </if>
            <if test="extTags != null">
                ext_tags = #{extTags,jdbcType=LONGVARCHAR},
            </if>
            <if test="extInfo != null">
                ext_info = #{extInfo,jdbcType=LONGVARCHAR},
            </if>
            <if test="loraModelId != null">
                lora_model_id = #{loraModelId},
            </if>
            <if test="isNew != null">
                is_new = #{isNew},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
            , <include refid="Blob_Column_List" />
        from creative_element
        where id = #{id,jdbcType=INTEGER}
            and deleted = 0
    </select>
    <select id="selectByPrimaryKeyWithChildren" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
            , <include refid="Blob_Column_List" />
        from creative_element
        where (id = #{id,jdbcType=INTEGER} or parent_id = #{id,jdbcType=INTEGER})
            and deleted = 0
    </select>
    <select id="selectRootByKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        , <include refid="Blob_Column_List" />
        from creative_element
        where config_key = #{configKey,jdbcType=VARCHAR}
            and deleted = 0
            and level = 1
    </select>
    <select id="selectAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
            , <include refid="Blob_Column_List" />
        from creative_element
        where deleted = 0
        order by `order`
    </select>
    <update id="batchResetOrder">
        UPDATE creative_element
        <trim prefix="SET" suffixOverrides=",">
            `order` = CASE
            <foreach collection="list" item="item" index="index">
                WHEN id = #{item.id} THEN #{item.order}
            </foreach>
            ELSE name END
        </trim>
        WHERE id IN
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>
    <select id="selectByExample" parameterType="ai.conrain.aigc.platform.dal.example.CreativeElementExample"
            resultMap="BaseResultMap">
        select
            <if test="distinct">
                distinct
            </if>
            <include refid="Base_Column_List" />
            ,
            <include refid="Blob_Column_List" />
            ,
            <include refid="View_Column_List" />
        from creative_element_view
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="rows != null">
            <if test="offset != null">
                limit ${offset}, ${rows}
            </if>
            <if test="offset == null">
                limit ${rows}
            </if>
        </if>
    </select>
    <select id="selectByExampleWithSimple" parameterType="ai.conrain.aigc.platform.dal.example.CreativeElementExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List" />
        ,
        <include refid="Blob_Column_List" />
        from creative_element
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="rows != null">
            <if test="offset != null">
                limit ${offset}, ${rows}
            </if>
            <if test="offset == null">
                limit ${rows}
            </if>
        </if>
    </select>
    <select id="countByExample" parameterType="ai.conrain.aigc.platform.dal.example.CreativeElementExample"
            resultType="java.lang.Long">
        select count(*)
        from creative_element_view
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>

    <select id="selectPrimaryInfoByIdWithChildren"
            resultType="ai.conrain.aigc.platform.dal.entity.CreativeElementDO">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from creative_element
        where (id = #{id,jdbcType=INTEGER} or parent_id = #{id,jdbcType=INTEGER})
        and deleted = 0
    </select>

    <select id="countNeedProcessByKeys" resultType="map">
        select config_key configKey, count(*) cnt
        from creative_element a
            left join user b on a.user_id = b.id
        where a.deleted = 0
            and config_key in
            <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
            and a.status != 'PROD'
            and b.role_type in ('MERCHANT','DISTRIBUTOR')
        group by config_key
    </select>
    <select id="selectAllUserElementByDate" resultType="java.util.Map">
        select cast(ext_info->>'$.openScope' as char) user_id,sum(if(`config_key` ='FACE',1,0)) face_cnt,sum(if(`config_key` ='SCENE',1,0)) scene_cnt
        from `creative_element`
        where ext_info->>'$.openScope' is not null and ext_info->>'$.openScope' != 'ALL'
        and deleted != 1 and `level` = 2
        <if test="startDate != null">
            and create_time &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            and create_time &lt;= #{endDate}
        </if>
        GROUP BY cast(ext_info->>'$.openScope' as char)
    </select>
    <select id="queryExperimentalIds" resultType="java.lang.Integer">
        select id
        from creative_element
        where deleted = 0
        and level = 2
        and config_key = 'SCENE'
        and ext_info->>'$.experimental' = 'true'
        <if test="idList != null">
            and id in
            <foreach item="item" index="index" collection="idList" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="lockByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
            , <include refid="Blob_Column_List" />
        from creative_element
        where id = #{id,jdbcType=INTEGER}
            and deleted = 0
        for update nowait
    </select>
</mapper>