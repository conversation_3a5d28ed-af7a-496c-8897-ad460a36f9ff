<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.conrain.aigc.platform.dal.pgsql.dao.ImageRecordDAO">
  <resultMap id="BaseResultMap" type="ai.conrain.aigc.platform.dal.pgsql.entity.ImageRecordDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <id column="sceneId" jdbcType="BIGINT" property="sceneId" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="pair_url" jdbcType="VARCHAR" property="pairUrl" />
    <result column="show_img_url" jdbcType="VARCHAR" property="showImgUrl" />
    <result column="image_path" jdbcType="VARCHAR" property="imagePath" />
    <result column="image_hash" jdbcType="VARCHAR" property="imageHash" />
    <result column="metadata" jdbcType="OTHER" property="metadata" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="result" property="result" jdbcType="OTHER"/>
    <result column="agg" property="agg" jdbcType="OTHER"/>
    <result column="intendedUse" jdbcType="VARCHAR" property="intendedUse" />
    <result column="clothTypeDesc" jdbcType="VARCHAR" property="clothTypeDesc" />
  </resultMap>

  <sql id="Base_Column_List">
    id, type, url, show_img_url, image_path, image_hash, metadata, create_time, modify_time,
    deleted
  </sql>

  <!-- 公共的图片组标题关联 LEFT JOIN，用于筛选 result 字段是否存在 -->
  <sql id="_image_group_caption_join">
    LEFT JOIN image_group_caption igc ON ig.id = igc.image_group_id AND igc.deleted = false
  </sql>

  <!-- 基于CTE子表的聚合筛选条件 -->
  <sql id="_agg_filter_cte">
    <if test="agg != null and agg.size() > 0">
      AND (
        <foreach collection="agg" item="aggItem" separator=" AND ">
          <choose>
            <when test="aggItem.edited == true">
              EXISTS (
                SELECT 1 FROM jsonb_array_elements(sub.agg) AS elem
                WHERE (elem->>'userId')::int = #{aggItem.userId}
                AND elem->>'editTime' IS NOT NULL
              )
            </when>
            <when test="aggItem.edited == false">
              NOT EXISTS (
                SELECT 1 FROM jsonb_array_elements(sub.agg) AS elem
                WHERE (elem->>'userId')::int = #{aggItem.userId}
                AND elem->>'editTime' IS NOT NULL
              )
            </when>
            <otherwise>
              1=1
            </otherwise>
          </choose>
        </foreach>
      )
    </if>
  </sql>

  <!-- CTE子表的排序条件 -->
  <sql id="_order_by_direction_cte">
    <choose>
      <when test="direction == 'prev'">
          ORDER BY sub.id DESC
      </when>
      <otherwise>
          ORDER BY sub.id ASC
      </otherwise>
    </choose>
  </sql>

  <select id="findPageable" resultMap="BaseResultMap">
    WITH filtered_images AS (
      -- 第一步：基于查询条件预过滤 image 表，利用索引
      SELECT
        i.id,
        i.type,
        i.url,
        i.show_img_url,
        i.image_path,
        i.image_hash,
        i.metadata,
        COALESCE(NULLIF(i.metadata->>'intendedUse', ''), 'unassigned') as intendedUse,
        COALESCE(i.metadata->>'clothTypeDesc', '') as clothTypeDesc,
        i.ext_info,
        i.create_time,
        i.modify_time,
        i.deleted
      FROM image i
      WHERE i.deleted = false
        <!-- 前置过滤条件，利用索引 -->
        <if test="id != null">
          AND i.id = #{id,jdbcType=BIGINT}
        </if>
        <if test="imageType != null and imageType != ''">
          AND i.type = #{imageType,jdbcType=VARCHAR}
        </if>
        <if test="createTimeStart != null">
          AND i.create_time >= #{createTimeStart,jdbcType=TIMESTAMP}
        </if>
        <if test="createTimeEnd != null">
          AND i.create_time &lt;= #{createTimeEnd,jdbcType=TIMESTAMP}
        </if>
        <if test="cursor != null and direction == 'next'">
          AND i.id > #{cursor,jdbcType=BIGINT}
        </if>
        <if test="cursor != null and direction == 'prev'">
          AND i.id &lt; #{cursor,jdbcType=BIGINT}
        </if>
        <!-- 基于 metadata 的索引过滤 -->
        <if test="tag != null and tag == 'no_tag'">
          AND COALESCE(jsonb_array_length(i.metadata -> 'tags'), 0) = 0
        </if>
        <if test="tags != null and tags.size() > 0 and tag != 'no_tag'">
          AND jsonb_exists_any(i.metadata -> 'tags', string_to_array(#{tag,jdbcType=VARCHAR}, ','))
        </if>
        <if test="intendedUse != null and intendedUse != ''">
          AND COALESCE(NULLIF(i.metadata->>'intendedUse', ''), 'unassigned') = #{intendedUse,jdbcType=VARCHAR}
        </if>
        <if test="quality != null and quality != ''">
          AND COALESCE(NULLIF(i.metadata->>'quality', ''), 'unassigned') = #{quality,jdbcType=VARCHAR}
        </if>
        <if test="lowQuality != null and lowQuality != ''">
          AND COALESCE(NULLIF(i.metadata->>'lowQuality', ''), 'no') = #{lowQuality,jdbcType=VARCHAR}
        </if>
        <if test="metadataFields != null and metadataFields.size() > 0">
          <foreach collection="metadataFields" item="metadataField">
            <if test="metadataField.values != null and metadataField.values.size() > 0">
              AND COALESCE(NULLIF(TRIM(metadata->>'${metadataField.fieldName}'), ''), 'null') IN
              <foreach collection="metadataField.values" item="value" open="(" separator="," close=")">
                #{value}
              </foreach>
            </if>
          </foreach>
        </if>
        <if test="owner != null and owner != ''">
          AND jsonb_exists(i.ext_info->'owner', #{owner})
        </if>
      <!-- 排序和限制，减少后续处理的数据量 -->
      <choose>
        <when test="direction == 'prev'">
          ORDER BY i.id DESC
        </when>
        <otherwise>
          ORDER BY i.id ASC
        </otherwise>
      </choose>
      <if test="agg == null or agg.size() == 0">
        LIMIT #{limit} OFFSET #{offset}
      </if>
    ),
    filtered_caption_users AS (
      -- 第二步：基于已过滤的 image_id 预过滤 image_caption_user 表（仅在需要聚合过滤时执行）
      <choose>
        <when test="agg != null and agg.size() > 0">
          SELECT DISTINCT ON (image_id, user_id)
            image_id, user_id, modify_time
          FROM image_caption_user icu
          WHERE icu.deleted = false
            AND EXISTS (SELECT 1 FROM filtered_images fi WHERE fi.id = icu.image_id)
          ORDER BY image_id, user_id, modify_time DESC
        </when>
        <otherwise>
          -- 当不需要聚合过滤时，返回空结果集占位
          SELECT NULL::integer as image_id, NULL::integer as user_id, NULL::timestamp as modify_time
          WHERE FALSE
        </otherwise>
      </choose>
    ),
    image_agg AS (
      -- 第三步：在已过滤的数据集上进行聚合
      SELECT
        fi.id,
        fi.id as sceneId,
        fi.type,
        fi.url,
        fi.show_img_url,
        fi.image_path,
        fi.image_hash,
        fi.metadata,
        fi.intendedUse,
        fi.clothTypeDesc,
        fi.ext_info,
        fi.create_time,
        fi.modify_time,
        fi.deleted,
        <choose>
          <when test="agg != null and agg.size() > 0">
            COALESCE(
              jsonb_agg(
                jsonb_build_object(
                  'userId', fcu.user_id,
                  'editTime', fcu.modify_time
                )
              ) FILTER (WHERE fcu.user_id IS NOT NULL),
              '[]'::jsonb
            ) as agg
          </when>
          <otherwise>
            '[]'::jsonb as agg
          </otherwise>
        </choose>
      FROM filtered_images fi
      <if test="agg != null and agg.size() > 0">
      LEFT JOIN filtered_caption_users fcu ON fi.id = fcu.image_id
      </if>
      GROUP BY fi.id, fi.type, fi.url, fi.show_img_url, fi.image_path, fi.image_hash,
               fi.metadata, fi.intendedUse, fi.clothTypeDesc, fi.ext_info, fi.create_time, fi.modify_time, fi.deleted
    )
    SELECT
      sub.id,
      sub.sceneId,
      sub.type,
      sub.url,
      sub.show_img_url,
      sub.image_path,
      sub.image_hash,
      sub.metadata,
      sub.intendedUse,
      sub.clothTypeDesc,
      sub.ext_info,
      sub.create_time,
      sub.modify_time,
      sub.deleted,
      NULL as result,
      sub.agg
    FROM image_agg sub
    <where>
      1=1
      <!-- 仅保留无法在第一步过滤的条件 -->
      <include refid="_agg_filter_cte" />
    </where>
    <include refid="_order_by_direction_cte" />
    <if test="agg != null and agg.size() > 0">
      LIMIT #{limit} OFFSET #{offset}
    </if>
  </select>

  <select id="countPageable" resultType="long">
    WITH filtered_images AS (
      -- 第一步：基于查询条件预过滤 image 表，利用索引
      SELECT
        i.id,
        i.type,
        i.metadata,
        COALESCE(NULLIF(i.metadata->>'intendedUse', ''), 'unassigned') as intendedUse,
        i.ext_info,
        i.create_time,
        i.modify_time
      FROM image i
      WHERE i.deleted = false
        <!-- 前置过滤条件，利用索引 -->
        <if test="id != null">
          AND i.id = #{id,jdbcType=BIGINT}
        </if>
        <if test="imageType != null and imageType != ''">
          AND i.type = #{imageType,jdbcType=VARCHAR}
        </if>
        <if test="createTimeStart != null">
          AND i.create_time >= #{createTimeStart,jdbcType=TIMESTAMP}
        </if>
        <if test="createTimeEnd != null">
          AND i.create_time &lt;= #{createTimeEnd,jdbcType=TIMESTAMP}
        </if>
        <if test="cursor != null and direction == 'next'">
          AND i.id > #{cursor,jdbcType=BIGINT}
        </if>
        <if test="cursor != null and direction == 'prev'">
          AND i.id &lt; #{cursor,jdbcType=BIGINT}
        </if>
        <!-- 基于 metadata 的索引过滤 -->
        <if test="tag != null and tag == 'no_tag'">
          AND COALESCE(jsonb_array_length(i.metadata -> 'tags'), 0) = 0
        </if>
        <if test="tags != null and tags.size() > 0 and tag != 'no_tag'">
          AND jsonb_exists_any(i.metadata -> 'tags', string_to_array(#{tag,jdbcType=VARCHAR}, ','))
        </if>
        <if test="intendedUse != null and intendedUse != ''">
          AND COALESCE(NULLIF(i.metadata->>'intendedUse', ''), 'unassigned') = #{intendedUse,jdbcType=VARCHAR}
        </if>
        <if test="quality != null and quality != ''">
          AND COALESCE(NULLIF(i.metadata->>'quality', ''), 'unassigned') = #{quality,jdbcType=VARCHAR}
        </if>
        <if test="lowQuality != null and lowQuality != ''">
          AND COALESCE(NULLIF(i.metadata->>'lowQuality', ''), 'no') = #{lowQuality,jdbcType=VARCHAR}
        </if>
        <if test="metadataFields != null and metadataFields.size() > 0">
          <foreach collection="metadataFields" item="metadataField">
            <if test="metadataField.values != null and metadataField.values.size() > 0">
              AND COALESCE(NULLIF(TRIM(metadata->>'${metadataField.fieldName}'), ''), 'null') IN
              <foreach collection="metadataField.values" item="value" open="(" separator="," close=")">
                #{value}
              </foreach>
            </if>
          </foreach>
        </if>
        <if test="owner != null and owner != ''">
          AND jsonb_exists(i.ext_info->'owner', #{owner})
        </if>
    ),
    filtered_caption_users AS (
      -- 第二步：基于已过滤的 image_id 预过滤 image_caption_user 表（仅在需要聚合过滤时执行）
      <choose>
        <when test="agg != null and agg.size() > 0">
          SELECT DISTINCT ON (image_id, user_id)
            image_id, user_id, modify_time
          FROM image_caption_user icu
          WHERE icu.deleted = false
            AND EXISTS (SELECT 1 FROM filtered_images fi WHERE fi.id = icu.image_id)
          ORDER BY image_id, user_id, modify_time DESC
        </when>
        <otherwise>
          -- 当不需要聚合过滤时，返回空结果集占位
          SELECT NULL::integer as image_id, NULL::integer as user_id, NULL::timestamp as modify_time
          WHERE FALSE
        </otherwise>
      </choose>
    ),
    image_agg AS (
      -- 第三步：在已过滤的数据集上进行聚合（仅在需要时）
      SELECT
        fi.id,
        <choose>
          <when test="agg != null and agg.size() > 0">
            COALESCE(
              jsonb_agg(
                jsonb_build_object(
                  'userId', fcu.user_id,
                  'editTime', fcu.modify_time
                )
              ) FILTER (WHERE fcu.user_id IS NOT NULL),
              '[]'::jsonb
            ) as agg
          </when>
          <otherwise>
            '[]'::jsonb as agg
          </otherwise>
        </choose>
      FROM filtered_images fi
      <if test="agg != null and agg.size() > 0">
      LEFT JOIN filtered_caption_users fcu ON fi.id = fcu.image_id
      GROUP BY fi.id
      </if>
      <if test="agg == null or agg.size() == 0">
      -- 当不需要聚合时，不执行 GROUP BY，直接返回每行
      </if>
    )
    SELECT count(sub.id)
    FROM image_agg sub
    <where>
      1=1
      <!-- 仅保留无法在第一步过滤的条件 -->
      <include refid="_agg_filter_cte" />
    </where>
  </select>

  <!-- CTE子表的图片组查询条件 -->
  <sql id="_pageable_conditions_group_cte">
    AND sub.type = 'pair'
    <if test="id != null">
      AND sub.id = #{id,jdbcType=BIGINT}
    </if>
    <if test="paired != null and paired == true">
      AND sub.pair_url is not null
    </if>
    <if test="paired != null and paired == false">
      AND sub.pair_url is null
    </if>
    <if test="tag != null and tag == 'no_tag'">
      AND COALESCE(jsonb_array_length(metadata -> 'tags'), 0) = 0
    </if>
    <if test="tags != null and tags.size() > 0 and tag != 'no_tag'">
      AND jsonb_exists_any(sub.metadata -> 'tags', string_to_array(#{tag,jdbcType=VARCHAR}, ','))
    </if>
    <if test="cursor != null and direction == 'next'">
      AND sub.id > #{cursor,jdbcType=BIGINT}
    </if>
    <if test="cursor != null and direction == 'prev'">
      AND sub.id &lt; #{cursor,jdbcType=BIGINT}
    </if>
    <!-- 筛选 result 字段中是否包含指定的 result 值 -->
    <if test="result != null and result != ''">
      AND sub.result::jsonb ?? #{result,jdbcType=VARCHAR}
    </if>
    <if test="intendedUse != null and intendedUse != ''">
      AND COALESCE(NULLIF(sub.intendedUse, ''), 'unassigned') = #{intendedUse,jdbcType=VARCHAR}
    </if>
    <if test="createTimeStart != null">
      AND sub.create_time >= #{createTimeStart,jdbcType=TIMESTAMP}
    </if>
    <if test="createTimeEnd != null">
      AND sub.create_time &lt;= #{createTimeEnd,jdbcType=TIMESTAMP}
    </if>
  </sql>

  <select id="findImageGroupPageable" resultMap="BaseResultMap">
    WITH image_group_agg AS (
      SELECT
        ig.id,
        ig.type,
        i1.id as sceneId,
        i1.image_path,
        i1.url,
        i2.url as pair_url,
        ig.metadata,
        COALESCE(NULLIF(i1.metadata->>'intendedUse', ''), 'unassigned') as intendedUse,
        COALESCE(i2.metadata->>'clothTypeDesc', '') as clothTypeDesc,
        ig.create_time,
        ig.modify_time,
        igc.result,
        COALESCE(
          jsonb_agg(
            jsonb_build_object(
              'userId', igcu.user_id,
              'editTime', igcu.modify_time
            )
          ) FILTER (WHERE igcu.user_id IS NOT NULL),
          '[]'::jsonb
        ) as agg
      FROM image_group ig
      LEFT JOIN image i1 ON (ig.image_ids ->> 0)::bigint = i1.id AND i1.deleted = false
      LEFT JOIN image i2 ON (ig.image_ids ->> 1)::bigint = i2.id AND i2.deleted = false
      <include refid="_image_group_caption_join" />
      LEFT JOIN (
        SELECT DISTINCT ON (image_group_id, user_id) image_group_id, user_id, modify_time
        FROM image_group_caption_user
        WHERE deleted = false
        ORDER BY image_group_id, user_id, modify_time DESC
      ) igcu ON ig.id = igcu.image_group_id
      WHERE ig.deleted = false
        AND ig.type = 'pair'
      GROUP BY ig.id, i1.id, i1.image_path, i1.url, i2.url, ig.metadata, ig.create_time, ig.modify_time, igc.result, i1.metadata, i2.metadata
    )
    SELECT
      sub.id,
      sub.sceneId,
      sub.type,
      sub.image_path,
      sub.url,
      sub.pair_url,
      sub.metadata,
      sub.intendedUse,
      sub.clothTypeDesc,
      sub.create_time,
      sub.modify_time,
      sub.result,
      sub.agg
    FROM image_group_agg sub
    <where>
      1=1
      <include refid="_pageable_conditions_group_cte" />
      <include refid="_agg_filter_cte" />
    </where>
    <include refid="_order_by_direction_cte" />
    LIMIT #{limit} OFFSET #{offset}
  </select>

  <select id="countImageGroupPageable" resultType="long">
    WITH image_group_agg AS (
      SELECT
        ig.id,
        ig.type,
        i1.url,
        i2.url as pair_url,
        ig.metadata,
        COALESCE(NULLIF(i1.metadata->>'intendedUse', ''), 'unassigned') as intendedUse,
        COALESCE(i2.metadata->>'clothTypeDesc', '') as clothTypeDesc,
        igc.result,
        COALESCE(
          jsonb_agg(
            jsonb_build_object(
              'userId', igcu.user_id,
              'editTime', igcu.modify_time
            )
          ) FILTER (WHERE igcu.user_id IS NOT NULL),
          '[]'::jsonb
        ) as agg
      FROM image_group ig
      LEFT JOIN image i1 ON (ig.image_ids ->> 0)::bigint = i1.id AND i1.deleted = false
      LEFT JOIN image i2 ON (ig.image_ids ->> 1)::bigint = i2.id AND i2.deleted = false
      <include refid="_image_group_caption_join" />
      LEFT JOIN (
        SELECT DISTINCT ON (image_group_id, user_id) image_group_id, user_id, modify_time
        FROM image_group_caption_user
        WHERE deleted = false
        ORDER BY image_group_id, user_id, modify_time DESC
      ) igcu ON ig.id = igcu.image_group_id
      WHERE ig.deleted = false
        AND ig.type = 'pair'
      GROUP BY ig.id, i1.url, i2.url, ig.metadata, igc.result, i1.metadata, i2.metadata
    )
    SELECT count(sub.id)
    FROM image_group_agg sub
    <where>
      1=1
      <include refid="_pageable_conditions_group_cte" />
      <include refid="_agg_filter_cte" />
    </where>
  </select>
</mapper>
