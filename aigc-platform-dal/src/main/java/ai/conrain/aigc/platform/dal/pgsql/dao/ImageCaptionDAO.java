package ai.conrain.aigc.platform.dal.pgsql.dao;

import ai.conrain.aigc.platform.dal.example.ImageCaptionExample;
import ai.conrain.aigc.platform.dal.pgsql.entity.ImageCaptionDO;
import com.pgvector.PGvector;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Mapper
public interface ImageCaptionDAO {
    long countByExample(ImageCaptionExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(ImageCaptionDO record);

    int insertSelective(ImageCaptionDO record);

    List<ImageCaptionDO> selectNoQualityScoreImageCaptionIds();

    List<ImageCaptionDO> selectNoPreCaption(@Param("limit") int limit);

    List<ImageCaptionDO> selectSimpleByExample(ImageCaptionExample example);

    List<ImageCaptionDO> selectByExample(ImageCaptionExample example);

    List<ImageCaptionDO> selectRecallVectorsByExample(ImageCaptionExample example);

    ImageCaptionDO selectByPrimaryKey(Integer id);

    ImageCaptionDO selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id,
            @Param("andLogicalDeleted") boolean andLogicalDeleted);

    int updateByExampleSelective(@Param("record") ImageCaptionDO record, @Param("example") ImageCaptionExample example);

    int updateByExample(@Param("record") ImageCaptionDO record, @Param("example") ImageCaptionExample example);

    int updateByPrimaryKeySelective(ImageCaptionDO record);

    int updateByPrimaryKey(ImageCaptionDO record);

    int logicalDeleteByExample(@Param("example") ImageCaptionExample example);

    int logicalDeleteByPrimaryKey(Integer id);

    /**
     * 根据服装款式向量相似度查询图像标注
     *
     * @param styleVector             服装款式向量
     * @param limit                   返回结果数量限制
     * @param genre
     * @param ageGroup
     * @param excludeImageCaptionIds  需要排除的图像标注ID列表
     * @param styleWhitelistRules     款式类型白名单规则Map，key对应pre_caption字段路径
     * @param styleBlacklistRules     款式类型黑名单规则Map，key对应pre_caption字段路径
     * @param functionWhitelistRules  功能分类白名单规则Map，key对应pre_caption字段路径
     * @param functionBlacklistRules  功能分类黑名单规则Map，key对应pre_caption字段路径
     * @param compositionBlacklistRules 构图黑名单规则Map，key对应caption字段路径
     * @return 相似度匹配的图像标注列表
     */
    List<ImageCaptionDO> selectByStyleVector(
            @Param("styleVector") PGvector styleVector,
            @Param("gender") String gender,
            @Param("similarityThreshold") Double similarityThreshold,
            @Param("limit") Integer limit, String genre, String ageGroup,
            @Param("excludeImageCaptionIds") Set<Integer> excludeImageCaptionIds,
            @Param("styleWhitelistRules") Map<String, List<String>> styleWhitelistRules,
            @Param("styleBlacklistRules") Map<String, List<String>> styleBlacklistRules,
            @Param("functionWhitelistRules") Map<String, List<String>> functionWhitelistRules,
            @Param("functionBlacklistRules") Map<String, List<String>> functionBlacklistRules,
            @Param("compositionBlacklistRules") Map<String, List<String>> compositionBlacklistRules);

    List<ImageCaptionDO> selectRecallVectors(@Param("ids") List<Integer> ids);

    List<ImageCaptionDO> selectSortVectors(@Param("ids") List<Integer> ids);

    ImageCaptionDO selectOneByImageId(Integer imageId);

    void updateGenreByTmp(@Param("batchSize") int batchSize);
}