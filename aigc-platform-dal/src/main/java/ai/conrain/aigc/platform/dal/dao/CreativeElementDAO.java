package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.CreativeElementDO;
import ai.conrain.aigc.platform.dal.example.CreativeElementExample;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface CreativeElementDAO {
    int deleteByPrimaryKey(Integer id);

    int insert(CreativeElementDO record);

    CreativeElementDO selectByPrimaryKey(Integer id);

    List<CreativeElementDO> selectAll();

    int updateByPrimaryKey(CreativeElementDO record);

    int updateByPrimaryKeySelective(CreativeElementDO record);

    void batchResetOrder(List<CreativeElementDO> list);

    List<CreativeElementDO> selectByExample(CreativeElementExample example);

    List<CreativeElementDO> selectByExampleWithSimple(CreativeElementExample example);

    long countByExample(CreativeElementExample example);

    CreativeElementDO selectRootByKey(String configKey);

    List<CreativeElementDO> selectByPrimaryKeyWithChildren(@Param("id") Integer id);

    List<CreativeElementDO> selectPrimaryInfoByIdWithChildren(@Param("id") Integer id);

    @MapKey("configKey")
    Map<String, Map<String, Object>> countNeedProcessByKeys(List<String> list);

    /**
     * 查询所有用户元素
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return 用户元素列表
     */
    @MapKey("userId")
    List<Map<String, Object>> selectAllUserElementByDate(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 查询实验场景 id 列表
     * @param idList id 列表
     */
    List<Integer> queryExperimentalIds(@Param("idList") List<Integer> idList);

    CreativeElementDO lockByPrimaryKey(@Param("id") Integer id);
}