package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.CommonTaskDO;
import ai.conrain.aigc.platform.dal.example.CommonTaskExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CommonTaskDAO {
    long countByExample(CommonTaskExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(CommonTaskDO record);

    int insertSelective(CommonTaskDO record);

    List<CommonTaskDO> selectByExampleWithBLOBs(CommonTaskExample example);

    List<CommonTaskDO> selectByExample(CommonTaskExample example);

    CommonTaskDO selectByPrimaryKey(Integer id);

    CommonTaskDO selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    CommonTaskDO lockById(Integer id);

    int updateByExampleSelective(@Param("record") CommonTaskDO record, @Param("example") CommonTaskExample example);

    int updateByExampleWithBLOBs(@Param("record") CommonTaskDO record, @Param("example") CommonTaskExample example);

    int updateByExample(@Param("record") CommonTaskDO record, @Param("example") CommonTaskExample example);

    int updateByPrimaryKeySelective(CommonTaskDO record);

    int updateByPrimaryKeyWithBLOBs(CommonTaskDO record);

    int updateByPrimaryKey(CommonTaskDO record);

    int logicalDeleteByExample(@Param("example") CommonTaskExample example);

    int logicalDeleteByPrimaryKey(Integer id);

    void batchInsert(List<CommonTaskDO> list);
}