/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.integration.ai;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.integration.ai.model.FileVO;
import ai.conrain.aigc.platform.integration.ai.model.PromptResult;
import ai.conrain.aigc.platform.integration.ai.model.QueueResult;
import ai.conrain.aigc.platform.integration.ai.model.UploadFileResult;
import ai.conrain.aigc.platform.integration.wechat.model.ImageQualityVO;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * 调用comfyUI服务
 *
 * <AUTHOR>
 * @version : ComfyUIService.java, v 0.1 2024/5/9 21:56 renxiao.wu Exp $
 */
public interface ComfyUIService {

    /**
     * 提交任务
     *
     * @param request 请求json
     * @param url     服务地址
     * @return 结果
     */
    PromptResult prompt(String request, String url);

    /**
     * 调用queue结果查询状态
     *
     * @param promptId 任务id
     * @param url      服务地址
     * @return 结果状态
     */
    Map<String, QueueResult> queryStatusByQueue(List<String> promptId, String url);

    /**
     * 查指定目录下指定类型的所有文件内容，用于查看图片和文件内容，图片会返回50kb以内的压缩图，文件直接返回文本内容，用于展示抠图和打标结果
     *
     * @param directory 目录
     * @param fileTypes {"img", "text"}
     * @param serverUrl 服务地址
     * @return 返回结果
     */
    List<FileVO> viewFiles(String directory, String[] fileTypes, String serverUrl);

    String calcDirMd5(String directory, String serverUrl);

    /**
     * 调用queue进行测试
     *
     * @param cfgUrl url
     * @param port   端口
     * @return 测试结果
     */
    QueueResult testByQueue(String cfgUrl, String port);

    /**
     * 历史记录中查询结果
     *
     * @param promptId      请求id
     * @param url           服务地址
     * @param fileServerUrl 文件服务地址，用于重启
     * @return 返回结果
     */
    QueueResult queryStatusByHistory(String promptId, String url, String fileServerUrl);

    /**
     * 清空历史记录
     *
     * @param url 服务地址
     * @return 返回结果
     */
    boolean clearHistory(String url);

    /**
     * 查询路径下的图片数量
     *
     * @param fullPath       路径
     * @param fileNamePrefix 文件名前缀
     * @param url            服务地址
     * @return 图片数量
     */
    Integer queryImageCnt(String fullPath, String fileNamePrefix, String url);

    /**
     * 查询路径下的图片数量
     *
     * @param path           路径
     * @param fileNamePrefix 文件名前缀
     * @param url            服务地址
     * @return 图片数量
     */
    Integer queryOutputImageCnt(String path, String fileNamePrefix, String url);

    /**
     * 下载文件，获取文件流
     *
     * @param path
     * @return
     */
    ByteArrayOutputStream downloadFile(String path, String serverUrl);

    /**
     * 获取图片文件流
     *
     * @param path           文件路径
     * @param fileNamePrefix 文件名前缀
     * @param url            文件服务地址
     * @return 返回zip结果
     */
    byte[] batchFetchImageFile(String path, String fileNamePrefix, String url);

    /**
     * 获取图片文件流
     *
     * @param path           文件路径
     * @param fileNamePrefix 文件名前缀
     * @param fileServerUrl  文件服务地址
     * @return 返回图片结果
     */
    Map<String, byte[]> downloadImage(String path, String fileNamePrefix, String fileServerUrl);

    /**
     * 获取视频文件流
     *
     * @param path           文件路径
     * @param fileNamePrefix 文件名前缀
     * @param fileServerUrl  文件服务地址
     * @return 返回图片结果
     */
    Map<String, byte[]> downloadVideo(String path, String fileNamePrefix, String fileServerUrl);

    /**
     * 远程删除图片文件
     *
     * @param path           文件路径
     * @param fileNamePrefix 文件名前缀
     * @param fileServerUrl  文件服务地址
     */
    boolean removeImage(String path, String fileNamePrefix, String fileServerUrl);

    /**
     * 远程删除图片或txt文件
     *
     * @param dir            文件路径
     * @param fileNamePrefix 文件名前缀
     * @param serverUrl      文件服务地址
     */
    boolean removeImageOrTxtFile(String dir, String fileNamePrefix, String serverUrl);

    /**
     * 将图片的二进制流上传到目标服务的/input目录下
     *
     * @param fileName    文件名
     * @param inputStream 图片二进制流
     * @param serverUrl   服务地址
     */
    void uploadInputImage(String fileName, InputStream inputStream, String serverUrl) throws IOException;

    /**
     * 将文件二进制流上传到目标服务
     *
     * @param filePath    文件完整路径
     * @param inputStream 图片二进制流
     * @param serverUrl   服务地址
     */
    void uploadFile(String filePath, InputStream inputStream, String serverUrl) throws IOException;

    /**
     * 读取远程文本文件内容
     */
    String fetchFileContent(String directory, String fileNamePrefix, String serverUrl);

    /**
     * 读取远程文本文件内容
     */
    String fetchFileContent(String directory, String fileNamePrefix, String fileNameSuffix, String serverUrl);

    /**
     * 文件同步服务
     *
     * @param path      文件的全路径
     * @param targetUrl 目标服务地址
     * @param originUrl 文件所在服务地址
     * @return true 成功
     */
    boolean fileSync(String path, String targetUrl, String originUrl);

    /**
     * 从oss进行文件同步服务
     *
     * @param path            保存的文件全路径
     * @param ossUrl          目标服务地址
     * @param targetServerUrl 目标文件所在服务地址
     * @param originServerUrl 源文件所在服务地址
     * @param md5             文件md5
     * @return true 成功
     */
    boolean fileSyncByOss(String path, String ossUrl, String targetServerUrl, String originServerUrl, String md5);

    /**
     * 文件复制服务
     *
     * @param fromFilePath  原始文件路径
     * @param toFilePath    复制文件路径
     * @param fileServerUrl 文件服务地址
     * @return true 成功
     */
    boolean fileCopy(String fromFilePath, String toFilePath, String fileServerUrl);

    /**
     * 远程文件同步服务 【originalServer + originalPath = 源文件地址】【targetServer + targetPath = 文件目标存储位置】
     *
     * @param originalServer 文件所在服务地址
     * @param originalPath   文件目录
     * @param targetServer   目标服务地址
     * @param targetPath     目标地址
     * @return true 成功
     */
    boolean remoteFileSync(String originalServer, String originalPath, String targetServer, String targetPath);

    /**
     * 远程文件同步服务 【originalServer + originalPath = 源文件地址】【targetServer + targetPath = 文件目标存储位置】
     *
     * @param thirdPartFile 第三方文件（阿里云 oss）【eg: https://www.oss.com/file/file.jpg】
     * @param targetServer  目标服务地址
     * @param targetPath    目标地址
     * @return true 成功
     */
    boolean remoteFileSync(String thirdPartFile, String targetServer, String targetPath);

    /**
     * 文件夹同步服务
     *
     * @param path      文件的全路径
     * @param targetUrl 目标服务地址
     * @param originUrl 文件所在服务地址
     * @param md5       目标文件夹md5
     * @return true 成功
     */
    boolean folderSync(String path, String targetUrl, String originUrl, String md5);

    /**
     * 文件夹拷贝服务
     *
     * @param path          文件的全路径
     * @param newPath       新文件路径
     * @param fileServerUrl 目标服务地址
     * @return true 成功
     */
    boolean folderCopy(String path, String newPath, String fileServerUrl);

    /**
     * 检查远程文件夹是否存在
     *
     * @param path
     * @param serverUrl
     * @return
     */
    boolean checkFolderExists(String path, String serverUrl);

    /**
     * 检查远程文件是否存在
     *
     * @param filePath  文件路径
     * @param serverUrl 文件服务地址
     * @return true存在
     */
    boolean checkFileExists(String filePath, String serverUrl);

    /**
     * 更新远程文本文件内容
     *
     * @param filePath
     * @param content
     */
    void updateTextFileContent(String filePath, String content, String serverUrl);

    /**
     * 图片移除背景
     * <br/>
     * 默认以Inspyrenet模型进行抠图
     *
     * @param byteArray 图片二进制流
     * @param serverUrl 文件服务地址
     * @return 移除背景后的图片二进制流
     */
    byte[] removeBg(byte[] byteArray, String serverUrl);

    /**
     * 图片移除背景
     *
     * @param byteArray 图片二进制流
     * @param serverUrl 文件服务地址
     * @param modelType 模型类型
     * @return 移除背景后的图片二进制流
     */
    byte[] removeBg(byte[] byteArray, String modelType, String serverUrl);

    /**
     * 调用 lama_remover API，传入原图地址、蒙版文件、输出路径和文件名前缀
     *
     * @param originImagePath   原图的 comfyUI 地址 (绝对路径)
     * @param originImagePrefix 原图文件名前缀
     * @param maskBytes         蒙版图片字节数组
     * @param resultImagePath   指定结果图片保存位置 (绝对路径)
     * @param resultImagePrefix 指定结果图片文件名前缀
     * @param resultFormat      指定结果图片格式
     * @param serverUrl         服务地址
     * @return 返回图片文件
     * @throws IOException 如果API调用失败
     */
    byte[] callLamaRemoverApi(String originImagePath, String originImagePrefix, byte[] maskBytes,
                              String resultImagePath, String resultImagePrefix, String resultFormat, String serverUrl)
        throws IOException;

    /**
     * 对服装搭配进行翻译
     *
     * @param clothCollocation 服装搭配json string
     * @return 翻译结果
     */
    JSONObject transClothCollocation(String clothCollocation, String serverUrl);

    /**
     * 对用户自定义场景翻译
     *
     * @param customScene 用户自定义场景
     * @param serverUrl   服务地址
     * @return 翻译结果
     */
    JSONObject transCustomScene(String customScene, String serverUrl);

    /**
     * 兼容逻辑：对原有搭配（一句话）进行兼容处理
     *
     * @param clothCollocation 服装搭配json string
     * @return 翻译结果
     */
    JSONObject extractClothCollocation(String clothCollocation, String serverUrl);

    /**
     * 对重绘描述进行翻译
     *
     * @param origin        重绘描述
     * @param fileServerUrl 文件服务地址
     * @return 翻译结果
     */
    JSONObject generalTranslate(String origin, String fileServerUrl);

    /**
     * 是否质量差的图片
     *
     * @param imagePath 图片相对路径
     * @param imageUrl  图片oss地址
     * @param isPureBG  是否存的背景
     * @param serverUrl 服务地址
     * @return true，质量差
     */
    ImageQualityVO isBadImage(String imagePath, String imageUrl, boolean isPureBG, String serverUrl);

    /**
     * 低质量检测 /low_quality
     *
     * @param base64    图片 bash64
     * @param serverUrl 服务
     * @return true: 是低质量; false: 不是
     */
    boolean lowQuality(String base64, String serverUrl);

    /**
     * 在指定目录下查找未完整的图片，返回不完整图片文件名列表
     *
     * @param folderPath
     * @param serverUrl
     * @return
     */
    List<String> findInCompleteImgs(String folderPath, String serverUrl);

    /**
     * 重命名文件
     *
     * @param oldPath   原始路径
     * @param newPath   新路径
     * @param serverUrl 服务地址
     * @return true 成功
     */
    boolean rename(String oldPath, String newPath, String serverUrl);

    /**
     * 调用gpt
     *
     * @param prompt    prompt
     * @param imagePath 文件地址
     * @param serverUrl 服务地址
     * @return 结果
     */
    String callLLM(String prompt, String imagePath, String serverUrl);

    /**
     * 获取服务端运行日志
     *
     * @param type      服务类型，CREATIVE/TRAIN
     * @param port      端口
     * @param lines     行数
     * @param serverUrl 服务地址
     * @return 日志
     */
    String fetchRunLog(String type, Integer port, Integer lines, String serverUrl);

    /**
     * 重启服务端口
     *
     * @param port          服务端口
     * @param fileServerUrl 文件服务地址
     * @return true 成功
     */
    boolean restartPort(Integer port, String fileServerUrl);

    /**
     * 重启服务
     *
     * @param type          服务类型
     * @param fileServerUrl 文件服务地址
     * @return true 成功
     */
    boolean restartServer(String type, String fileServerUrl);

    /**
     * 更新comfyui节点
     *
     * @param fileServerUrl 文件服务地址
     * @return true 成功
     */
    boolean updateCreativeNode(String fileServerUrl);

    /**
     * 上传文件到OSS
     *
     * @param filePath      文件全路径
     * @param fileName      文件名
     * @param fileServerUrl 文件服务地址
     * @return oss文件地址
     */
    UploadFileResult uploadToOss(String filePath, String fileName, String fileServerUrl);

    /**
     * 写入文件
     *
     * @param filePath
     * @param content
     * @param serverUrl
     * @return
     */
    boolean writeToFile(String filePath, String content, String serverUrl);
}
