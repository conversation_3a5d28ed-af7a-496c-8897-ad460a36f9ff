package ai.conrain.aigc.platform.integration.aliyun;

import ai.conrain.aigc.platform.integration.aliyun.model.StsCredentials;
import ai.conrain.aigc.platform.integration.aliyun.model.XOssProcessEnum;
import ai.conrain.aigc.platform.integration.constant.OssBucketNames;
import ai.conrain.aigc.platform.integration.utils.IntegrationUtils;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.HttpMethod;
import com.aliyun.oss.OSS;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.PutObjectResult;
import com.aliyun.sts20150401.models.AssumeRoleResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * oss service
 */
@Slf4j
@Component
public class OssService {

    @Value("${aliyun.oss.bucket}")
    String bucketName;

    @Value("${aliyun.sts.endpoint}")
    String stsEndpoint;

    //ram oss用户的ak/sk
    @Value("${aliyun.oss.accessKeyId}")
    String accessKeyId;

    @Value("${aliyun.oss.accessKeySecret}")
    String accessKeySecret;

    @Autowired
    private OSS oss;

    @Autowired
    private TairService tairService;

    @Autowired
    private ImmService immService;

    public static String BLIND_WATERMARK = "MuseGate";

    //50年过期时间（毫秒）
    private static final long EXPIRES_MILLS_50_YEARS = 50 * 365 * 24 * 3600 * 1000L;

    /**
     * 获取oss ram用户对应的sts临时身份凭证，用于前端直传文件到oss（前端需要依赖oss js sdk）
     * <a href="https://help.aliyun.com/zh/oss/use-cases/client-direct-transmission-overview?spm=a2c4g.11186623.0.0.5bf87baaW9O3bW">客户端直传概述</a>
     * <a href="https://help.aliyun.com/zh/oss/use-cases/use-wechat-mini-programs-to-upload-objects?spm=a2c4g.11186623.0.i66">微信小程序直传实践</a>
     * <a href="https://help.aliyun.com/zh/oss/developer-reference/use-temporary-access-credentials-provided-by-sts-to-access-oss?spm=a2c4g.11186623.0.i4#concept-xzh-nzk-2gb">使用STS临时访问凭证访问OSS</a>
     */
    public StsCredentials getStsCredentials(Long durationSeconds, boolean refreshCache) {

        String cacheKey = "aigc-platform-sts-" + durationSeconds;

        if (refreshCache) {
            tairService.clear(cacheKey);
        } else {
            StsCredentials cachedSts = tairService.getObject(cacheKey, StsCredentials.class);
            if (cachedSts != null) {
                return cachedSts;
            }
        }

        if (durationSeconds == null) {
            durationSeconds = 30 * 60L;
        }

        //durationSeconds，单位为秒，允许的取值范围是15min ~ 1小时
        if (durationSeconds < 15 * 60 || durationSeconds > 3600) {
            throw new IllegalArgumentException("durationSeconds must be between 15min and 1hour");
        }

        try {
            com.aliyun.sts20150401.Client client = createStsClient(this.accessKeyId, this.accessKeySecret);
            com.aliyun.sts20150401.models.AssumeRoleRequest assumeRoleRequest
                = new com.aliyun.sts20150401.models.AssumeRoleRequest().setDurationSeconds(durationSeconds).setRoleArn(
                "acs:ram::1052491479391425:role/ramoss").setRoleSessionName("RamOss");
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            AssumeRoleResponse roleResponse = client.assumeRoleWithOptions(assumeRoleRequest, runtime);

            if (roleResponse == null || roleResponse.getBody() == null
                || roleResponse.getBody().getCredentials() == null) {
                log.error("get sts token failed, response: {}", JSONObject.toJSONString(roleResponse));
                throw new RuntimeException("get oss sts token failed");
            }

            StsCredentials ret = new StsCredentials();
            ret.setAccessKeyId(roleResponse.getBody().getCredentials().getAccessKeyId());
            ret.setAccessKeySecret(roleResponse.getBody().getCredentials().getAccessKeySecret());
            ret.setExpiration(roleResponse.getBody().getCredentials().getExpiration());
            ret.setSecurityToken(roleResponse.getBody().getCredentials().getSecurityToken());

            //提前10分钟过期，兜底一下
            tairService.setObject(cacheKey, ret, durationSeconds.intValue() - 10*60);

            return ret;

        } catch (Exception e) {
            log.error("get sts token failed", e);
            throw new RuntimeException("get oss sts token failed", e);
        }
    }

    private com.aliyun.sts20150401.Client createStsClient(String accessKeyId, String accessKeySecret) throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
            // 必填，您的 AccessKey ID
            .setAccessKeyId(accessKeyId)
            // 必填，您的 AccessKey Secret
            .setAccessKeySecret(accessKeySecret);
        // Endpoint 请参考 https://api.aliyun.com/product/Sts
        config.endpoint = stsEndpoint;
        return new com.aliyun.sts20150401.Client(config);
    }

    /**
     * 生成文件的预签名url，用于前端上传文件到oss（前端不用依赖oss js sdk）
     * <a href="https://help.aliyun.com/zh/oss/use-cases/client-direct-transmission-overview?spm=a2c4g.11186623.0.0.358d393edZtWAJ#36c322a137x9q">...</a>
     * <a href="https://help.aliyun.com/zh/oss/use-cases/use-wechat-mini-programs-to-upload-objects?spm=a2c4g.11186623.0.0.124d71bfA3YiFw#section-mx7-v31-uy7">微信小程序上传文件</a>
     */
    public String getUploadPresignedUrl(String objectName, Integer expireInSecs, HttpMethod method) {

        if (StringUtils.isEmpty(objectName) || expireInSecs == null) {
            throw new IllegalArgumentException("objectName/expireInSecs为空");
        }

        GeneratePresignedUrlRequest generatePresignedUrlRequest = new GeneratePresignedUrlRequest(bucketName,
            objectName);
        generatePresignedUrlRequest.setMethod(method);
        Date expiration = new Date(new Date().getTime() + expireInSecs * 1000L);
        generatePresignedUrlRequest.setExpiration(expiration);
        URL url = oss.generatePresignedUrl(generatePresignedUrlRequest);

        return url.toString();
    }

    /**
     * 上传文件流到oss
     * @param fileName
     * @param is
     * @return
     */
    public String upload(String fileName, InputStream is) {
        if (StringUtils.isEmpty(fileName) || is == null) {
            throw new IllegalArgumentException("fileName is emtpy or input stream is null");
        }

        try {
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, fileName, is);
            PutObjectResult result = oss.putObject(putObjectRequest);
            log.info("upload file to oss, fileName={} , response:{}", fileName, JSONObject.toJSONString(result));

            // 设置签名URL过期时间，单位为毫秒，50年
            String url = getSignedFileUrl(fileName);
            if (url != null) return url;

        } catch (Throwable e) {
            log.error("upload file to oss failed:" + fileName, e);
        }

        return null;
    }

    /**
     * 上传图片并添加水印 MuseGate
     * @param fileName
     * @param is
     * @return
     */
    public String uploadImgWithBlindWatermark(String fileName, InputStream is) {
        String imageUrl = this.upload(fileName, is);
        if (StringUtils.isNotBlank(imageUrl)) {
            try {
                String oriObjectName = IntegrationUtils.getOssObjectNameFromUrl(imageUrl);
                String watermarked = this.addBlindWatermark2Img(imageUrl, BLIND_WATERMARK);
                if (StringUtils.isNotBlank(watermarked)) {
                    log.info("[电子水印]文件水印写入成功：{}，水印内容：{}，url：{}", oriObjectName, BLIND_WATERMARK, watermarked);
                    return watermarked;
                } else {
                    log.error("[电子水印]文件水印写入失败：{}，水印内容：{}，返回无水印url：{}", oriObjectName, BLIND_WATERMARK, imageUrl);
                    return imageUrl;
                }
            } catch (Exception e){
                log.error("[电子水印]文件水印写入失败：{}，水印内容：{}，返回无水印url：{}", imageUrl, BLIND_WATERMARK, imageUrl);
                return imageUrl;
            }

        } else {
            throw new RuntimeException("upload file to oss failed");
        }
    }

    /**
     * 下载文件到本地
     * @param ossObjName
     * @param localDir
     * @param localFileName
     * @return
     */
    public String downloadFile(String ossObjName, String localDir, String localFileName) {
        new File(localDir).mkdirs();
        String localPath = localDir + "/" + localFileName + getFileExtension(ossObjName);
        oss.getObject(new GetObjectRequest(bucketName, ossObjName), new File(localPath));
        return localPath;
    }

    /**
     * 获取外部在线文件流，上传到oss，不用下载文件到本地
     * @param fileUrl
     * @return
     */
    public String fetchStreamAndUpload(String fileUrl) {
        if (StringUtils.isEmpty(fileUrl)) {
            throw new IllegalArgumentException("fileUrl is empty");
        }

        HttpURLConnection connection = null;
        try {
            // 建立连接
            URL url = new URL(fileUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000); // 设置超时时间
            connection.setReadTimeout(30000);  // 设置读取超时时间

            // 检查响应状态
            int responseCode = connection.getResponseCode();
            if (responseCode != 200) {
                throw new RuntimeException("Failed to fetch file stream. HTTP response code: " + responseCode);
            }

            // 获取文件名
            String fileName = IntegrationUtils.getFileNameFromUrl(fileUrl);

            // 获取输入流
            try (InputStream inputStream = connection.getInputStream()) {
                if (inputStream != null) {
                    // 上传文件流到 OSS
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM/");
                    String ossFileName = sdf.format(new Date()) + System.currentTimeMillis() + "_" + fileName;
                    String ossUrl = this.upload(ossFileName, inputStream);
                    if (StringUtils.isNotBlank(ossUrl)) {
                        return ossUrl;
                    }
                }
            }
            throw new RuntimeException("Failed to upload file to OSS");
        } catch (Throwable t) {
            log.error("Error while uploading file from stream. URL: " + fileUrl, t);
            throw new RuntimeException(t);
        } finally {
            // 断开连接
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    /**
     * 将图片宽高缩放到大边不超过1024
     */
    public String resize1024(String url) {
        //对于已经处理过或者非当前bucket的文件，直接返回，防止dev的image在prod环境图片出错
        if (StringUtils.isBlank(url) || StringUtils.contains(url, "x-oss-process=image")) {
            return url;
        }

        String bucketName = IntegrationUtils.getOssBucketName(url);
        if (OssBucketNames.isMGBucket(bucketName)) {
            return this.reprocessImage(bucketName, IntegrationUtils.getOssObjectNameFromUrl(url), XOssProcessEnum.RESIZE_1024);
        } else {
            return url;
        }
    }

    public String reprocessImage(String bucketName, String ossObjName, XOssProcessEnum type) {

        //兼容直接传入url的情况
        if (ossObjName.startsWith("http")) {
            ossObjName = IntegrationUtils.getOssObjectNameFromUrl(ossObjName);
        }

        GeneratePresignedUrlRequest req = new GeneratePresignedUrlRequest(bucketName, ossObjName);
        //image/resize,m_lfit,w_400/quality,q_99/format,jpg

        req.setProcess(type.getCode());

        // 设置签名URL过期时间，单位为毫秒，50年
        Date expiration = new Date(new Date().getTime() + EXPIRES_MILLS_50_YEARS);

        // 设置过期时间。
        req.setExpiration(expiration);

        // 通过HTTP GET请求生成签名URL。
        URL signedUrl = oss.generatePresignedUrl(req);

        if (signedUrl != null) {
            return signedUrl.toExternalForm().replace("oss-cn-zhangjiakou-internal.aliyuncs.com",
                    "oss-cn-zhangjiakou.aliyuncs.com");
        }

        return null;
    }

    public String reprocessImage(String ossObjName, XOssProcessEnum type) {
        return this.reprocessImage(this.bucketName, ossObjName, type);
    }

    /**
     * 为图片添加盲水印
     * 注意：
     * 1.盲水印只支持图片，不支持视频
     * 2.这个实现，对于oss文件添加水印后，会！！【原地回写】！！更新oss同一个文件，因此文件md5会有更新
     *
     * @param ossImgUrl
     * @param watermark
     * @return
     */
    public String addBlindWatermark2Img(String ossImgUrl, String watermark) {
        if (StringUtils.isEmpty(ossImgUrl) || StringUtils.isEmpty(watermark) || !StringUtils.contains(ossImgUrl, bucketName)) {
            throw new IllegalArgumentException("ossImgUrl or watermark is invalid," + ossImgUrl + "," + watermark);
        }

        String objectName = IntegrationUtils.getOssObjectNameFromUrl(ossImgUrl);
        boolean success = immService.encodeBlindWatermark(String.format("oss://%s/%s", bucketName, objectName), String.format("oss://%s/%s", bucketName, objectName), watermark);
        if (success){
            String url = getSignedFileUrl(objectName);
            if (url != null) return url;
        }

        throw new RuntimeException("addBlindWatermark2Img failed");
    }

    /**
     * 检查文件是否存在于oss
     * @param filePath
     * @return
     */
    public boolean checkFileExists(String filePath){
        return oss.doesObjectExist(bucketName, filePath);
    }

    public String getSignedFileUrl(String targetObjectName) {
        if (StringUtils.isBlank(targetObjectName)) {
            throw new IllegalArgumentException("targetObjectName is blank");
        }

        //prod的这个bucket，走cdn域名加速，不必访问oss
        if (StringUtils.equals(bucketName, "aigc-platform-online")) {
            return String.format("%s/%s", "https://cdn.musegate.tech", targetObjectName);
        }

        // 设置签名URL过期时间，单位为毫秒，50年
        Date expiration = new Date(new Date().getTime() + EXPIRES_MILLS_50_YEARS);

        // 生成以GET方法访问的签名URL，访客可以直接通过浏览器访问相关内容。
        URL url = oss.generatePresignedUrl(bucketName, targetObjectName, expiration, HttpMethod.GET);

        if (url != null) {
            return url.toExternalForm().replace("oss-cn-zhangjiakou-internal.aliyuncs.com",
                    "oss-cn-zhangjiakou.aliyuncs.com");
        }
        return null;
    }

    /**
     * 根据url获取oss文件的md5
     * @param url
     * @return
     */
    public String getFileMd5ByUrl(String url) {
        if (StringUtils.isEmpty(url) || !StringUtils.contains(url, bucketName)) {
            throw new IllegalArgumentException("url is invalid," + url);
        }
        String targetObjectName = IntegrationUtils.getOssObjectNameFromUrl(url);
        return this.getFileMd5(targetObjectName);
    }

    /**
     * 获取oss文件的md5
     * @param objectName
     * @return
     */
    public String getFileMd5(String objectName) {
        if (StringUtils.isEmpty(objectName)) {
            throw new IllegalArgumentException("objectName is invalid," + objectName);
        }
        ObjectMetadata metadata = oss.getObjectMetadata(bucketName, objectName);
        return metadata.getETag();
    }

    private static String getFileExtension(String filePath) {
        if (filePath == null) {
            return "";
        }
        int index = filePath.lastIndexOf('.');
        if (index == -1) {
            // 没有扩展名
            return "";
        } else {
            // 返回从最后一个点开始到字符串结束的部分
            return filePath.substring(index);
        }
    }
}