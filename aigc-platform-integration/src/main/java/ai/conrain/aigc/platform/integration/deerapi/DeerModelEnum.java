/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.integration.deerapi;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * deerAPI的模型枚举
 *
 * <AUTHOR>
 * @version : DeerModelEnum.java, v 0.1 2025/8/6 19:46 renxiao.wu Exp $
 */
@Getter
public enum DeerModelEnum {
    GPT_4_1("gpt-4.1", SourceTypeEnum.COMMON),

    GPT_5_NANO("gpt-5-nano", SourceTypeEnum.COMMON),

    GPT_5_MINI("gpt-5-mini", SourceTypeEnum.COMMON),

    GPT_5("gpt-5", SourceTypeEnum.COMMON),

    GEMINI_2_5_PRO("gemini-2.5-pro", SourceTypeEnum.COMMON),

    GEMINI_2_5_FLASH("gemini-2.5-flash", SourceTypeEnum.COMMON),

    GEMINI_2_5_FLASH_IMAGE_PREVIEW("gemini-2.5-flash-image-preview", SourceTypeEnum.GOOGLE_IMAGE),

    DOUBAO_SEEDREAM_4_0_250828("doubao-seedream-4-0-250828", SourceTypeEnum.DOUBAO),

    VEO3("veo3", SourceTypeEnum.GOOGLE_VIDEO),
    ;

    /** 枚举码 */
    private final String code;

    /** 是否生成内容 */
    private final SourceTypeEnum sourceType;

    private DeerModelEnum(String code, SourceTypeEnum sourceType) {
        this.code = code;
        this.sourceType = sourceType;
    }

    /**
     * 根据枚举码获取枚举
     *
     * @param code 枚举码
     * @return 对应枚举
     */
    public static DeerModelEnum getByCode(String code, DeerModelEnum defaultEnum) {
        if (StringUtils.isBlank(code)) {
            return defaultEnum;
        }

        for (DeerModelEnum item : values()) {
            if (StringUtils.equals(item.getCode(), code)) {
                return item;
            }
        }

        return defaultEnum;
    }
}
