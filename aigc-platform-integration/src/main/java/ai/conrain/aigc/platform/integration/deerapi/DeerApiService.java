package ai.conrain.aigc.platform.integration.deerapi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.integration.utils.FileUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import static ai.conrain.aigc.platform.integration.gpt.AIModel.Base64ImageInfo;
import static ai.conrain.aigc.platform.integration.gpt.AIModel.GenerationConfig;
import static ai.conrain.aigc.platform.integration.gpt.AIModel.GptResponse;
import static ai.conrain.aigc.platform.integration.gpt.AIModel.Status;
import static ai.conrain.aigc.platform.integration.gpt.AIModel.createImageContent;
import static ai.conrain.aigc.platform.integration.gpt.AIModel.createImageContentForGemini;
import static ai.conrain.aigc.platform.integration.gpt.AIModel.createTextContent;
import static ai.conrain.aigc.platform.integration.gpt.AIModel.createTextContentForGemini;

@Slf4j
@Service
public class DeerApiService {

    @Autowired
    private RestTemplate restTemplate3min;

    /**
     * 调用大模型
     *
     * @param model        模型类型
     * @param platformType 平台类型
     * @param prompt       prompt
     * @param imgUrls      图片列表
     * @return 返回结果
     */
    public GptResponse call(DeerModelEnum model, PlatformTypeEnum platformType, String prompt, List<String> imgUrls) {

        if (StringUtils.isBlank(prompt) || model == null) {
            throw new RuntimeException("prompt is empty");
        }
        if (platformType == null) { // 默认小鹿api
            platformType = PlatformTypeEnum.API_DEER;
        }

        if (platformType == PlatformTypeEnum.API_DEER) {
            if (model.getSourceType() == SourceTypeEnum.GOOGLE_IMAGE) {
                return callGenerateContent(model, platformType, prompt, imgUrls);
            }

            if (model.getSourceType() == SourceTypeEnum.DOUBAO) {
                return callDoubaoContent(model, platformType, prompt, imgUrls);
            }
        }

        // 构建请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", String.format("Bearer %s", platformType.getApiKey()));

        // 构建消息体
        JSONObject payload = new JSONObject();
        payload.put("model", model.getCode());

        JSONArray messages = new JSONArray();
        JSONObject message = new JSONObject();
        message.put("role", "user");

        JSONArray content = new JSONArray();
        content.add(createTextContent(prompt));

        if (!CollectionUtils.isEmpty(imgUrls)) {
            for (String url : imgUrls) {
                content.add(createImageContent(url));
            }
        }

        message.put("content", content);
        messages.add(message);
        payload.put("messages", messages);

        try {
            log.info("【请求GPT】，payload={}", payload);
            HttpEntity<String> entity = new HttpEntity<>(payload.toJSONString(), headers);
            String endpoint = model.getSourceType().getEndPoint(platformType, model);
            ResponseEntity<String> response = restTemplate3min.exchange(endpoint, HttpMethod.POST, entity,
                String.class);

            JSONObject result = JSON.parseObject(response.getBody());

            log.info("【请求GPT】，result={}", result);

            String text = null;
            if (result != null) {
                text = result.getJSONArray("choices").getJSONObject(0).getJSONObject("message").getString("content");
            }

            if (model.getSourceType() != SourceTypeEnum.COMMON) {
                List<String> imageUrls = extractImageUrl(text);
                if (CollectionUtils.isNotEmpty(imageUrls)) {
                    return new GptResponse(null, imageUrls);
                }
            }

            return new GptResponse(text, Status.OK);
        } catch (Exception e) {
            log.error("Failed to make the request.", e);
            String errorMsg = "Failed to make the request. Error: " + e.getMessage();
            return new GptResponse(errorMsg, Status.ERROR);
        }
    }

    /**
     * 调用大模型
     *
     * @param model        模型类型
     * @param platformType 平台类型
     * @param prompt       prompt
     * @param imgUrls      图片列表
     * @return 返回结果
     */
    public GptResponse callGenerateContent(DeerModelEnum model, PlatformTypeEnum platformType, String prompt,
                                           List<String> imgUrls) {

        if (StringUtils.isBlank(prompt) || model == null) {
            throw new RuntimeException("prompt is empty");
        }

        // 构建请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", String.format("Bearer %s", platformType.getApiKey()));

        String endpoint = model.getSourceType().getEndPoint(platformType, model);

        // 构建消息体
        JSONObject payload = new JSONObject();
        payload.put("model", model.getCode());

        JSONArray messages = new JSONArray();
        JSONObject message = new JSONObject();
        message.put("role", "user");

        JSONArray content = new JSONArray();
        content.add(createTextContentForGemini(prompt));

        if (!CollectionUtils.isEmpty(imgUrls)) {
            for (String url : imgUrls) {
                content.add(createImageContentForGemini(url));
            }
        }

        message.put("parts", content);
        messages.add(message);
        if (platformType == PlatformTypeEnum.API_DEER) {
            payload.put("contents", messages);
        } else {
            payload.put("content", messages);
        }
        payload.put("generationConfig", GenerationConfig);

        try {
            log.info("【请求内容生成】，payload={}", prompt);
            HttpEntity<String> entity = new HttpEntity<>(payload.toJSONString(), headers);

            ResponseEntity<String> response = restTemplate3min.exchange(endpoint, HttpMethod.POST, entity,
                String.class);

            JSONObject result = JSON.parseObject(response.getBody());

            log.info("【请求内容生成】，result={}", result);

            AtomicReference<String> text = new AtomicReference<>();
            List<Base64ImageInfo> imageBase64List = new ArrayList<>();
            if (result != null) {
                JSONArray parts = result.getJSONArray("candidates").getJSONObject(0).getJSONObject("content")
                    .getJSONArray("parts");
                parts.forEach(part -> {
                    JSONObject json = (JSONObject)part;
                    if (json.containsKey("text")) {
                        text.set(json.getString("text"));
                    } else if (json.containsKey("inlineData")) {
                        imageBase64List.add(json.getJSONObject("inlineData").toJavaObject(Base64ImageInfo.class));
                    }
                });
            }

            return new GptResponse(text.get(), Status.OK, imageBase64List);
        } catch (Exception e) {
            log.error("Failed to make the request.", e);
            String errorMsg = "Failed to make the request. Error: " + e.getMessage();
            return new GptResponse(errorMsg, Status.ERROR);
        }
    }

    /**
     * 调用大模型
     *
     * @param model        模型类型
     * @param platformType 平台类型
     * @param prompt       prompt
     * @param imgUrls      图片列表
     * @return 返回结果
     */
    public GptResponse callDoubaoContent(DeerModelEnum model, PlatformTypeEnum platformType, String prompt,
                                         List<String> imgUrls) {

        if (StringUtils.isBlank(prompt) || model == null) {
            throw new RuntimeException("prompt is empty");
        }

        // 构建请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", String.format("Bearer %s", platformType.getApiKey()));

        String endpoint = model.getSourceType().getEndPoint(platformType, model);

        // 构建消息体
        JSONObject payload = new JSONObject();
        payload.put("model", model.getCode());
        payload.put("prompt", prompt);
        payload.put("image", imgUrls);
        payload.put("response_format", "url"); //url和b64_json
        payload.put("size", "4K");
        payload.put("watermark", false);
        payload.put("n", CollectionUtils.size(imgUrls));

        try {
            log.info("【请求豆包生成】，payload={}，imgUrls={}", prompt, imgUrls);
            HttpEntity<String> entity = new HttpEntity<>(payload.toJSONString(), headers);

            ResponseEntity<String> response = restTemplate3min.exchange(endpoint, HttpMethod.POST, entity,
                String.class);

            JSONObject result = JSON.parseObject(response.getBody());

            log.info("【请求豆包生成】，result={}", result);

            AtomicReference<String> text = new AtomicReference<>();
            List<Base64ImageInfo> imageBase64List = new ArrayList<>();
            List<String> imageUrlList = new ArrayList<>();
            if (result != null) {
                JSONArray parts = result.getJSONArray("data");
                parts.forEach(part -> {
                    JSONObject json = (JSONObject)part;
                    if (json.containsKey("url")) {
                        imageUrlList.add(json.getString("url"));
                    } else if (json.containsKey("b64_json")) {
                        Base64ImageInfo base64ImageInfo = new Base64ImageInfo("image/png", json.getString("b64_json"));
                        imageBase64List.add(base64ImageInfo);
                    }
                });
            }

            if (CollectionUtils.isNotEmpty(imageBase64List)) {
                return new GptResponse(text.get(), Status.OK, imageBase64List);
            }

            GptResponse gptResponse = new GptResponse(text.get(), imageUrlList);
            log.info("【请求豆包生成】，返回结果，result={}", gptResponse);
            return gptResponse;

        } catch (Exception e) {
            log.error("Failed to make the request.", e);
            String errorMsg = "Failed to make the request. Error: " + e.getMessage();
            return new GptResponse(errorMsg, Status.ERROR);
        }
    }

    /**
     * 从字符串中提取image1的URL
     *
     * @param input 包含图片链接的字符串
     * @return 提取到的URL，如果未找到则返回null
     */
    private static List<String> extractImageUrl(String input) {
        List<String> urls = new ArrayList<>();
        if (input == null || input.isEmpty()) {
            return null;
        }

        // 匹配Markdown图片语法 ![alt](url)
        Pattern markdownImagePattern = Pattern.compile("!\\[([^\\]]*)\\]\\(([^)]+)\\)");
        Matcher markdownImageMatcher = markdownImagePattern.matcher(input);

        while (markdownImageMatcher.find()) {
            String url = markdownImageMatcher.group(2);
            if (!urls.contains(url)) {
                urls.add(url);
            }
        }

        // 匹配Markdown链接语法 [text](url)
        Pattern markdownLinkPattern = Pattern.compile("\\[([^\\]]+)\\]\\(([^)]+)\\)");
        Matcher markdownLinkMatcher = markdownLinkPattern.matcher(input);

        while (markdownLinkMatcher.find()) {
            String url = markdownLinkMatcher.group(2);
            // 只添加图片或视频链接
            if (url.matches(".*\\.(png|jpg|jpeg|gif|webp|mp4|avi|mov|wmv|flv|mkv)(\\?.*)?$")) {
                // 避免重复添加
                if (!urls.contains(url) && urls.stream().noneMatch(each -> FileUtils.isFileNameEquals(each, url))) {
                    urls.add(url);
                }
            }
        }

        return urls;
    }
}