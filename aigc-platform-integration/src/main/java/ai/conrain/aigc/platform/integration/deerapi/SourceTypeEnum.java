/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.integration.deerapi;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import static ai.conrain.aigc.platform.integration.deerapi.PlatformTypeEnum.API_147;
import static ai.conrain.aigc.platform.integration.deerapi.PlatformTypeEnum.API_DEER;
import static ai.conrain.aigc.platform.integration.deerapi.PlatformTypeEnum.COMMON_ENDPOINT_TEMPLATE;

/**
 * 来源类型枚举
 *
 * <AUTHOR>
 * @version : SourceTypeEnum.java, v 0.1 2025/9/18 14:50 renxiao.wu Exp $
 */
@Getter
public enum SourceTypeEnum {
    COMMON("COMMON", "通用", COMMON_ENDPOINT_TEMPLATE),

    GOOGLE_IMAGE("GOOGLE_IMAGE", "谷歌图片生成", "%s/v1/models/%s:generateContent"),

    GOOGLE_VIDEO("GOOGLE_VIDEO", "谷歌视频生成", COMMON_ENDPOINT_TEMPLATE),

    DOUBAO("DOUBAO", "豆包", "%s/v1/images/generations"),

    ;

    /** 枚举码 */
    private final String code;

    /** 枚举描述 */
    private final String desc;

    /** 端点模板 */
    private final String endpointTemplate;

    private SourceTypeEnum(String code, String desc, String endpointTemplate) {
        this.code = code;
        this.desc = desc;
        this.endpointTemplate = endpointTemplate;
    }

    /**
     * 根据枚举码获取枚举
     *
     * @param code 枚举码
     * @return 对应枚举
     */
    public static SourceTypeEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }

        for (SourceTypeEnum item : values()) {
            if (StringUtils.equals(item.getCode(), code)) {
                return item;
            }
        }

        return null;
    }

    /**
     * 获取端点信息
     *
     * @param platformType 平台类型
     * @return 模型类型
     */
    public String getEndPoint(PlatformTypeEnum platformType, DeerModelEnum model) {
        if (platformType == null) {
            platformType = API_DEER;
        }

        if (platformType == API_147) {
            return String.format(COMMON_ENDPOINT_TEMPLATE, platformType.getDomain());
        }

        return String.format(this.getEndpointTemplate(), platformType.getDomain(), model.getCode());
    }
}
