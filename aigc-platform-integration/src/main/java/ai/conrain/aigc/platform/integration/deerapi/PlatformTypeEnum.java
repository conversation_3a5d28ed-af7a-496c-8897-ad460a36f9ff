/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.integration.deerapi;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 模型平台枚举
 *
 * <AUTHOR>
 * @version : PlatformTypeEnum.java, v 0.1 2025/9/24 15:15 renxiao.wu Exp $
 */
@Getter
public enum PlatformTypeEnum {
    API_DEER("API_DEER", "https://api2.deerapi.com", "sk-oxJh2MZywKKIuLKs6xWDeKtQL0jYnb0PQ6JdePpuf2Ofbanf"),
    API_147("API_147", "https://147ai.com", "sk-og1ZyjnBqfQQoUEUapb2p9mc5hmkkisdSgbBBUauzpf3zgk1"),

    ;
    public static final String COMMON_ENDPOINT_TEMPLATE = "%s/v1/chat/completions";

    /** 枚举码 */
    private final String code;

    /** 域名 */
    private final String domain;

    /** apiKey */
    private final String apiKey;

    private PlatformTypeEnum(String code, String domain, String apiKey) {
        this.code = code;
        this.domain = domain;
        this.apiKey = apiKey;
    }

    /**
     * 根据枚举码获取枚举
     *
     * @param code 枚举码
     * @return 对应枚举
     */
    public static PlatformTypeEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }

        for (PlatformTypeEnum item : values()) {
            if (StringUtils.equals(item.getCode(), code)) {
                return item;
            }
        }

        return null;
    }
}
